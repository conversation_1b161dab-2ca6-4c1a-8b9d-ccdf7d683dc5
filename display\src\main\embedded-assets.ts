// Auto-generated file - DO NOT EDIT
// Generated on: 2025-05-28T15:57:50.218Z
// Total assets: 183

import { readFileSync, existsSync } from 'fs';
import { join } from 'path';

export interface EmbeddedAsset {
  offset: number;
  length: number;
  size: number;
  extension: string;
  mimeType: string;
}

export interface EmbeddedAssetsManifest {
  version: string;
  timestamp: string;
  assets: { [path: string]: EmbeddedAsset };
}

// Load manifest and bundle data
let MANIFEST: EmbeddedAssetsManifest | null = null;
let BUNDLE_DATA: Buffer | null = null;

function loadEmbeddedAssets(): boolean {
  if (MANIFEST && BUNDLE_DATA) {
    return true;
  }

  try {
    // Determine paths based on environment
    const isDev = process.resourcesPath && process.resourcesPath.includes('node_modules');

    let manifestPath: string;
    let bundlePath: string;

    if (isDev) {
      // Development mode
      const srcMainPath = join(__dirname, '../../src/main');
      manifestPath = join(srcMainPath, 'embedded-assets-manifest.json');
      bundlePath = join(srcMainPath, 'embedded-assets.bundle');
    } else {
      // Production mode
      const basePath = join(process.resourcesPath, 'app.asar.unpacked', 'out', 'main');
      manifestPath = join(basePath, 'embedded-assets-manifest.json');
      bundlePath = join(basePath, 'embedded-assets.bundle');
    }

    if (!existsSync(manifestPath) || !existsSync(bundlePath)) {
      console.error('Embedded assets not found:', { manifestPath, bundlePath });
      return false;
    }

    MANIFEST = JSON.parse(readFileSync(manifestPath, 'utf8'));
    BUNDLE_DATA = readFileSync(bundlePath);

    console.log(`✅ Loaded embedded assets: ${Object.keys(MANIFEST.assets).length} assets, ${(BUNDLE_DATA.length / 1024 / 1024).toFixed(2)} MB`);
    return true;
  } catch (error) {
    console.error('Failed to load embedded assets:', error);
    return false;
  }
}

export function getEmbeddedAssetData(assetPath: string): Buffer | null {
  if (!loadEmbeddedAssets() || !MANIFEST || !BUNDLE_DATA) {
    return null;
  }

  const normalizedPath = assetPath.replace(/\\\\/g, '/').replace(/^\//, '');
  const assetInfo = MANIFEST.assets[normalizedPath];

  if (!assetInfo) {
    return null;
  }

  return BUNDLE_DATA.subarray(assetInfo.offset, assetInfo.offset + assetInfo.length);
}

export const EMBEDDED_ASSETS: EmbeddedAssetsManifest = {
  get version() { loadEmbeddedAssets(); return MANIFEST?.version || ''; },
  get timestamp() { loadEmbeddedAssets(); return MANIFEST?.timestamp || ''; },
  get assets() { loadEmbeddedAssets(); return MANIFEST?.assets || {}; }
};
