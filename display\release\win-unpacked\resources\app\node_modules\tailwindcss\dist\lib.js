"use strict";var ci=Object.defineProperty;var fi=(t,r)=>{for(var i in r)ci(t,i,{get:r[i],enumerable:!0})};var mt={};fi(mt,{Features:()=>Ve,Polyfills:()=>Ze,__unstable__loadDesignSystem:()=>Gn,compile:()=>Hn,compileAst:()=>ui,default:()=>Le});var jt="4.1.4";var Re=92,Me=47,We=42,di=34,mi=39,gi=58,Be=59,he=10,Oe=32,qe=9,It=123,ht=125,kt=40,zt=41,hi=91,vi=93,Ft=45,vt=64,wi=33;function ve(t){t[0]==="\uFEFF"&&(t=t.slice(1)),t=t.replaceAll(`\r
`,`
`);let r=[],i=[],e=[],n=null,s=null,a="",p="",u;for(let c=0;c<t.length;c++){let m=t.charCodeAt(c);if(m===Re)a+=t.slice(c,c+2),c+=1;else if(m===Me&&t.charCodeAt(c+1)===We){let g=c;for(let w=c+2;w<t.length;w++)if(u=t.charCodeAt(w),u===Re)w+=1;else if(u===We&&t.charCodeAt(w+1)===Me){c=w+1;break}let h=t.slice(g,c+1);h.charCodeAt(2)===wi&&i.push(He(h.slice(2,-2)))}else if(m===mi||m===di){let g=c;for(let h=c+1;h<t.length;h++)if(u=t.charCodeAt(h),u===Re)h+=1;else if(u===m){c=h;break}else{if(u===Be&&t.charCodeAt(h+1)===he)throw new Error(`Unterminated string: ${t.slice(g,h+1)+String.fromCharCode(m)}`);if(u===he)throw new Error(`Unterminated string: ${t.slice(g,h)+String.fromCharCode(m)}`)}a+=t.slice(g,c+1)}else{if((m===Oe||m===he||m===qe)&&(u=t.charCodeAt(c+1))&&(u===Oe||u===he||u===qe))continue;if(m===he){if(a.length===0)continue;u=a.charCodeAt(a.length-1),u!==Oe&&u!==he&&u!==qe&&(a+=" ")}else if(m===Ft&&t.charCodeAt(c+1)===Ft&&a.length===0){let g="",h=c,w=-1;for(let A=c+2;A<t.length;A++)if(u=t.charCodeAt(A),u===Re)A+=1;else if(u===Me&&t.charCodeAt(A+1)===We){for(let b=A+2;b<t.length;b++)if(u=t.charCodeAt(b),u===Re)b+=1;else if(u===We&&t.charCodeAt(b+1)===Me){A=b+1;break}}else if(w===-1&&u===gi)w=a.length+A-h;else if(u===Be&&g.length===0){a+=t.slice(h,A),c=A;break}else if(u===kt)g+=")";else if(u===hi)g+="]";else if(u===It)g+="}";else if((u===ht||t.length-1===A)&&g.length===0){c=A-1,a+=t.slice(h,A);break}else(u===zt||u===vi||u===ht)&&g.length>0&&t[A]===g[g.length-1]&&(g=g.slice(0,-1));let v=wt(a,w);if(!v)throw new Error("Invalid custom property, expected a value");n?n.nodes.push(v):r.push(v),a=""}else if(m===Be&&a.charCodeAt(0)===vt)s=Pe(a),n?n.nodes.push(s):r.push(s),a="",s=null;else if(m===Be&&p[p.length-1]!==")"){let g=wt(a);if(!g)throw a.length===0?new Error("Unexpected semicolon"):new Error(`Invalid declaration: \`${a.trim()}\``);n?n.nodes.push(g):r.push(g),a=""}else if(m===It&&p[p.length-1]!==")")p+="}",s=W(a.trim()),n&&n.nodes.push(s),e.push(n),n=s,a="",s=null;else if(m===ht&&p[p.length-1]!==")"){if(p==="")throw new Error("Missing opening {");if(p=p.slice(0,-1),a.length>0)if(a.charCodeAt(0)===vt)s=Pe(a),n?n.nodes.push(s):r.push(s),a="",s=null;else{let h=a.indexOf(":");if(n){let w=wt(a,h);if(!w)throw new Error(`Invalid declaration: \`${a.trim()}\``);n.nodes.push(w)}}let g=e.pop()??null;g===null&&n&&r.push(n),n=g,a="",s=null}else if(m===kt)p+=")",a+="(";else if(m===zt){if(p[p.length-1]!==")")throw new Error("Missing opening (");p=p.slice(0,-1),a+=")"}else{if(a.length===0&&(m===Oe||m===he||m===qe))continue;a+=String.fromCharCode(m)}}}if(a.charCodeAt(0)===vt&&r.push(Pe(a)),p.length>0&&n){if(n.kind==="rule")throw new Error(`Missing closing } at ${n.selector}`);if(n.kind==="at-rule")throw new Error(`Missing closing } at ${n.name} ${n.params}`)}return i.length>0?i.concat(r):r}function Pe(t,r=[]){for(let i=5;i<t.length;i++){let e=t.charCodeAt(i);if(e===Oe||e===kt){let n=t.slice(0,i).trim(),s=t.slice(i).trim();return I(n,s,r)}}return I(t.trim(),"",r)}function wt(t,r=t.indexOf(":")){if(r===-1)return null;let i=t.indexOf("!important",r+1);return l(t.slice(0,r).trim(),t.slice(r+1,i===-1?t.length:i).trim(),i!==-1)}function fe(t){if(arguments.length===0)throw new TypeError("`CSS.escape` requires an argument.");let r=String(t),i=r.length,e=-1,n,s="",a=r.charCodeAt(0);if(i===1&&a===45)return"\\"+r;for(;++e<i;){if(n=r.charCodeAt(e),n===0){s+="\uFFFD";continue}if(n>=1&&n<=31||n===127||e===0&&n>=48&&n<=57||e===1&&n>=48&&n<=57&&a===45){s+="\\"+n.toString(16)+" ";continue}if(n>=128||n===45||n===95||n>=48&&n<=57||n>=65&&n<=90||n>=97&&n<=122){s+=r.charAt(e);continue}s+="\\"+r.charAt(e)}return s}function we(t){return t.replace(/\\([\dA-Fa-f]{1,6}[\t\n\f\r ]?|[\S\s])/g,r=>r.length>2?String.fromCodePoint(Number.parseInt(r.slice(1).trim(),16)):r[1])}var Mt=new Map([["--font",["--font-weight","--font-size"]],["--inset",["--inset-shadow","--inset-ring"]],["--text",["--text-color","--text-decoration-color","--text-decoration-thickness","--text-indent","--text-shadow","--text-underline-offset"]]]);function Lt(t,r){return(Mt.get(r)??[]).some(i=>t===i||t.startsWith(`${i}-`))}var Ge=class{constructor(r=new Map,i=new Set([])){this.values=r;this.keyframes=i}prefix=null;add(r,i,e=0){if(r.endsWith("-*")){if(i!=="initial")throw new Error(`Invalid theme value \`${i}\` for namespace \`${r}\``);r==="--*"?this.values.clear():this.clearNamespace(r.slice(0,-2),0)}if(e&4){let n=this.values.get(r);if(n&&!(n.options&4))return}i==="initial"?this.values.delete(r):this.values.set(r,{value:i,options:e})}keysInNamespaces(r){let i=[];for(let e of r){let n=`${e}-`;for(let s of this.values.keys())s.startsWith(n)&&s.indexOf("--",2)===-1&&(Lt(s,e)||i.push(s.slice(n.length)))}return i}get(r){for(let i of r){let e=this.values.get(i);if(e)return e.value}return null}hasDefault(r){return(this.getOptions(r)&4)===4}getOptions(r){return r=we(this.#r(r)),this.values.get(r)?.options??0}entries(){return this.prefix?Array.from(this.values,r=>(r[0]=this.prefixKey(r[0]),r)):this.values.entries()}prefixKey(r){return this.prefix?`--${this.prefix}-${r.slice(2)}`:r}#r(r){return this.prefix?`--${r.slice(3+this.prefix.length)}`:r}clearNamespace(r,i){let e=Mt.get(r)??[];e:for(let n of this.values.keys())if(n.startsWith(r)){if(i!==0&&(this.getOptions(n)&i)!==i)continue;for(let s of e)if(n.startsWith(s))continue e;this.values.delete(n)}}#e(r,i){for(let e of i){let n=r!==null?`${e}-${r}`:e;if(!this.values.has(n))if(r!==null&&r.includes(".")){if(n=`${e}-${r.replaceAll(".","_")}`,!this.values.has(n))continue}else continue;if(!Lt(n,e))return n}return null}#t(r){let i=this.values.get(r);if(!i)return null;let e=null;return i.options&2&&(e=i.value),`var(${fe(this.prefixKey(r))}${e?`, ${e}`:""})`}markUsedVariable(r){let i=we(this.#r(r)),e=this.values.get(i);if(!e)return!1;let n=e.options&16;return e.options|=16,!n}resolve(r,i,e=0){let n=this.#e(r,i);if(!n)return null;let s=this.values.get(n);return(e|s.options)&1?s.value:this.#t(n)}resolveValue(r,i){let e=this.#e(r,i);return e?this.values.get(e).value:null}resolveWith(r,i,e=[]){let n=this.#e(r,i);if(!n)return null;let s={};for(let p of e){let u=`${n}${p}`,c=this.values.get(u);c&&(c.options&1?s[p]=c.value:s[p]=this.#t(u))}let a=this.values.get(n);return a.options&1?[a.value,s]:[this.#t(n),s]}namespace(r){let i=new Map,e=`${r}-`;for(let[n,s]of this.values)n===r?i.set(null,s.value):n.startsWith(`${e}-`)?i.set(n.slice(r.length),s.value):n.startsWith(e)&&i.set(n.slice(e.length),s.value);return i}addKeyframes(r){this.keyframes.add(r)}getKeyframes(){return Array.from(this.keyframes)}};var B=class extends Map{constructor(i){super();this.factory=i}get(i){let e=super.get(i);return e===void 0&&(e=this.factory(i,this),this.set(i,e)),e}};function yt(t){return{kind:"word",value:t}}function ki(t,r){return{kind:"function",value:t,nodes:r}}function bi(t){return{kind:"separator",value:t}}function te(t,r,i=null){for(let e=0;e<t.length;e++){let n=t[e],s=!1,a=0,p=r(n,{parent:i,replaceWith(u){s||(s=!0,Array.isArray(u)?u.length===0?(t.splice(e,1),a=0):u.length===1?(t[e]=u[0],a=1):(t.splice(e,1,...u),a=u.length):t[e]=u)}})??0;if(s){p===0?e--:e+=a-1;continue}if(p===2)return 2;if(p!==1&&n.kind==="function"&&te(n.nodes,r,n)===2)return 2}}function J(t){let r="";for(let i of t)switch(i.kind){case"word":case"separator":{r+=i.value;break}case"function":r+=i.value+"("+J(i.nodes)+")"}return r}var Wt=92,yi=41,Bt=58,qt=44,xi=34,Ht=61,Gt=62,Yt=60,Jt=10,Ai=40,Ci=39,Qt=47,Zt=32,Xt=9;function H(t){t=t.replaceAll(`\r
`,`
`);let r=[],i=[],e=null,n="",s;for(let a=0;a<t.length;a++){let p=t.charCodeAt(a);switch(p){case Wt:{n+=t[a]+t[a+1],a++;break}case Bt:case qt:case Ht:case Gt:case Yt:case Jt:case Qt:case Zt:case Xt:{if(n.length>0){let g=yt(n);e?e.nodes.push(g):r.push(g),n=""}let u=a,c=a+1;for(;c<t.length&&(s=t.charCodeAt(c),!(s!==Bt&&s!==qt&&s!==Ht&&s!==Gt&&s!==Yt&&s!==Jt&&s!==Qt&&s!==Zt&&s!==Xt));c++);a=c-1;let m=bi(t.slice(u,c));e?e.nodes.push(m):r.push(m);break}case Ci:case xi:{let u=a;for(let c=a+1;c<t.length;c++)if(s=t.charCodeAt(c),s===Wt)c+=1;else if(s===p){a=c;break}n+=t.slice(u,a+1);break}case Ai:{let u=ki(n,[]);n="",e?e.nodes.push(u):r.push(u),i.push(u),e=u;break}case yi:{let u=i.pop();if(n.length>0){let c=yt(n);u.nodes.push(c),n=""}i.length>0?e=i[i.length-1]:e=null;break}default:n+=String.fromCharCode(p)}}return n.length>0&&r.push(yt(n)),r}function Ye(t){let r=[];return te(H(t),i=>{if(!(i.kind!=="function"||i.value!=="var"))return te(i.nodes,e=>{e.kind!=="word"||e.value[0]!=="-"||e.value[1]!=="-"||r.push(e.value)}),1}),r}var $i=64;function L(t,r=[]){return{kind:"rule",selector:t,nodes:r}}function I(t,r="",i=[]){return{kind:"at-rule",name:t,params:r,nodes:i}}function W(t,r=[]){return t.charCodeAt(0)===$i?Pe(t,r):L(t,r)}function l(t,r,i=!1){return{kind:"declaration",property:t,value:r,important:i}}function He(t){return{kind:"comment",value:t}}function le(t,r){return{kind:"context",context:t,nodes:r}}function D(t){return{kind:"at-root",nodes:t}}function z(t,r,i=[],e={}){for(let n=0;n<t.length;n++){let s=t[n],a=i[i.length-1]??null;if(s.kind==="context"){if(z(s.nodes,r,i,{...e,...s.context})===2)return 2;continue}i.push(s);let p=!1,u=0,c=r(s,{parent:a,context:e,path:i,replaceWith(m){p||(p=!0,Array.isArray(m)?m.length===0?(t.splice(n,1),u=0):m.length===1?(t[n]=m[0],u=1):(t.splice(n,1,...m),u=m.length):(t[n]=m,u=1))}})??0;if(i.pop(),p){c===0?n--:n+=u-1;continue}if(c===2)return 2;if(c!==1&&"nodes"in s){i.push(s);let m=z(s.nodes,r,i,e);if(i.pop(),m===2)return 2}}}function Je(t,r,i=[],e={}){for(let n=0;n<t.length;n++){let s=t[n],a=i[i.length-1]??null;if(s.kind==="rule"||s.kind==="at-rule")i.push(s),Je(s.nodes,r,i,e),i.pop();else if(s.kind==="context"){Je(s.nodes,r,i,{...e,...s.context});continue}i.push(s),r(s,{parent:a,context:e,path:i,replaceWith(p){Array.isArray(p)?p.length===0?t.splice(n,1):p.length===1?t[n]=p[0]:t.splice(n,1,...p):t[n]=p,n+=p.length-1}}),i.pop()}}function ke(t,r,i=3){let e=[],n=new Set,s=new B(()=>new Set),a=new B(()=>new Set),p=new Set,u=new Set,c=[],m=[],g=new B(()=>new Set);function h(v,A,b={},y=0){if(v.kind==="declaration"){if(v.property==="--tw-sort"||v.value===void 0||v.value===null)return;if(b.theme&&v.property[0]==="-"&&v.property[1]==="-"){if(v.value==="initial"){v.value=void 0;return}b.keyframes||s.get(A).add(v)}if(v.value.includes("var("))if(b.theme&&v.property[0]==="-"&&v.property[1]==="-")for(let V of Ye(v.value))g.get(V).add(v.property);else r.trackUsedVariables(v.value);if(v.property==="animation")for(let V of er(v.value))u.add(V);i&2&&v.value.includes("color-mix(")&&a.get(A).add(v),A.push(v)}else if(v.kind==="rule")if(v.selector==="&")for(let V of v.nodes){let T=[];h(V,T,b,y+1),T.length>0&&A.push(...T)}else{let V={...v,nodes:[]};for(let T of v.nodes)h(T,V.nodes,b,y+1);V.nodes.length>0&&A.push(V)}else if(v.kind==="at-rule"&&v.name==="@property"&&y===0){if(n.has(v.params))return;if(i&1){let T=v.params,O=null,_=!1;for(let j of v.nodes)j.kind==="declaration"&&(j.property==="initial-value"?O=j.value:j.property==="inherits"&&(_=j.value==="true"));_?c.push(l(T,O??"initial")):m.push(l(T,O??"initial"))}n.add(v.params);let V={...v,nodes:[]};for(let T of v.nodes)h(T,V.nodes,b,y+1);A.push(V)}else if(v.kind==="at-rule"){v.name==="@keyframes"&&(b={...b,keyframes:!0});let V={...v,nodes:[]};for(let T of v.nodes)h(T,V.nodes,b,y+1);v.name==="@keyframes"&&b.theme&&p.add(V),(V.nodes.length>0||V.name==="@layer"||V.name==="@charset"||V.name==="@custom-media"||V.name==="@namespace"||V.name==="@import")&&A.push(V)}else if(v.kind==="at-root")for(let V of v.nodes){let T=[];h(V,T,b,0);for(let O of T)e.push(O)}else if(v.kind==="context"){if(v.context.reference)return;for(let V of v.nodes)h(V,A,{...b,...v.context},y)}else v.kind==="comment"&&A.push(v)}let w=[];for(let v of t)h(v,w,{},0);e:for(let[v,A]of s)for(let b of A){if(tr(b.property,r.theme,g)){if(b.property.startsWith(r.theme.prefixKey("--animate-")))for(let T of er(b.value))u.add(T);continue}let V=v.indexOf(b);if(v.splice(V,1),v.length===0){let T=Vi(w,O=>O.kind==="rule"&&O.nodes===v);if(!T||T.length===0)continue e;T.unshift({kind:"at-root",nodes:w});do{let O=T.pop();if(!O)break;let _=T[T.length-1];if(!_||_.kind!=="at-root"&&_.kind!=="at-rule")break;let j=_.nodes.indexOf(O);if(j===-1)break;_.nodes.splice(j,1)}while(!0);continue e}}for(let v of p)if(!u.has(v.params)){let A=e.indexOf(v);e.splice(A,1)}if(w=w.concat(e),i&2)for(let[v,A]of a)for(let b of A){let y=v.indexOf(b);if(y===-1||b.value==null)continue;let V=H(b.value),T=!1;if(te(V,(j,{replaceWith:P})=>{if(j.kind!=="function"||j.value!=="color-mix")return;let Y=!1,M=!1;if(te(j.nodes,(F,{replaceWith:re})=>{if(F.kind=="word"&&F.value.toLowerCase()==="currentcolor"){M=!0,T=!0;return}let X=F,se=null,o=new Set;do{if(X.kind!=="function"||X.value!=="var")return;let f=X.nodes[0];if(!f||f.kind!=="word")return;let d=f.value;if(o.has(d)){Y=!0;return}if(o.add(d),T=!0,se=r.theme.resolveValue(null,[f.value]),!se){Y=!0;return}if(se.toLowerCase()==="currentcolor"){M=!0;return}se.startsWith("var(")?X=H(se)[0]:X=null}while(X);re({kind:"word",value:se})}),Y||M){let F=j.nodes.findIndex(X=>X.kind==="separator"&&X.value.trim().includes(","));if(F===-1)return;let re=j.nodes.length>F?j.nodes[F+1]:null;if(!re)return;P(re)}else if(T){let F=j.nodes[2];F.kind==="word"&&(F.value==="oklab"||F.value==="oklch"||F.value==="lab"||F.value==="lch")&&(F.value="srgb")}}),!T)continue;let O={...b,value:J(V)},_=W("@supports (color: color-mix(in lab, red, red))",[b]);v.splice(y,1,O,_)}if(i&1){let v=[];if(c.length>0&&v.push(W(":root, :host",c)),m.length>0&&v.push(W("*, ::before, ::after, ::backdrop",m)),v.length>0){let A=w.findIndex(b=>!(b.kind==="comment"||b.kind==="at-rule"&&(b.name==="@charset"||b.name==="@import")));w.splice(A<0?w.length:A,0,I("@layer","properties",[])),w.push(W("@layer properties",[I("@supports","((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b))))",v)]))}}return w}function ie(t){function r(e,n=0){let s="",a="  ".repeat(n);if(e.kind==="declaration")s+=`${a}${e.property}: ${e.value}${e.important?" !important":""};
`;else if(e.kind==="rule"){s+=`${a}${e.selector} {
`;for(let p of e.nodes)s+=r(p,n+1);s+=`${a}}
`}else if(e.kind==="at-rule"){if(e.nodes.length===0)return`${a}${e.name} ${e.params};
`;s+=`${a}${e.name}${e.params?` ${e.params} `:" "}{
`;for(let p of e.nodes)s+=r(p,n+1);s+=`${a}}
`}else if(e.kind==="comment")s+=`${a}/*${e.value}*/
`;else if(e.kind==="context"||e.kind==="at-root")return"";return s}let i="";for(let e of t){let n=r(e);n!==""&&(i+=n)}return i}function Vi(t,r){let i=[];return z(t,(e,{path:n})=>{if(r(e))return i=[...n],2}),i}function tr(t,r,i,e=new Set){if(e.has(t)||(e.add(t),r.getOptions(t)&24))return!0;{let s=i.get(t)??[];for(let a of s)if(tr(a,r,i,e))return!0}return!1}function er(t){return t.split(/[\s,]+/)}var xt=["calc","min","max","clamp","mod","rem","sin","cos","tan","asin","acos","atan","atan2","pow","sqrt","hypot","log","exp","round"],Xe=["anchor-size"],rr=new RegExp(`(${Xe.join("|")})\\(`,"g");function _e(t){return t.indexOf("(")!==-1&&xt.some(r=>t.includes(`${r}(`))}function ir(t){if(!xt.some(n=>t.includes(n)))return t;let r=!1;Xe.some(n=>t.includes(n))&&(rr.lastIndex=0,t=t.replace(rr,(n,s)=>(r=!0,`$${Xe.indexOf(s)}$(`)));let i="",e=[];for(let n=0;n<t.length;n++){let s=t[n];if(s==="("){i+=s;let a=n;for(let u=n-1;u>=0;u--){let c=t.charCodeAt(u);if(c>=48&&c<=57)a=u;else if(c>=97&&c<=122)a=u;else break}let p=t.slice(a,n);if(xt.includes(p)){e.unshift(!0);continue}else if(e[0]&&p===""){e.unshift(!0);continue}e.unshift(!1);continue}else if(s===")")i+=s,e.shift();else if(s===","&&e[0]){i+=", ";continue}else{if(s===" "&&e[0]&&i[i.length-1]===" ")continue;if((s==="+"||s==="*"||s==="/"||s==="-")&&e[0]){let a=i.trimEnd(),p=a[a.length-1];if(p==="+"||p==="*"||p==="/"||p==="-"){i+=s;continue}else if(p==="("||p===","){i+=s;continue}else t[n-1]===" "?i+=`${s} `:i+=` ${s} `}else if(e[0]&&t.startsWith("to-zero",n)){let a=n;n+=7,i+=t.slice(a,n+1)}else i+=s}}return r?i.replace(/\$(\d+)\$/g,(n,s)=>Xe[s]??n):i}function pe(t){if(t.indexOf("(")===-1)return Ne(t);let r=H(t);return At(r),t=J(r),t=ir(t),t}function Ne(t,r=!1){let i="";for(let e=0;e<t.length;e++){let n=t[e];n==="\\"&&t[e+1]==="_"?(i+="_",e+=1):n==="_"&&!r?i+=" ":i+=n}return i}function At(t){for(let r of t)switch(r.kind){case"function":{if(r.value==="url"||r.value.endsWith("_url")){r.value=Ne(r.value);break}if(r.value==="var"||r.value.endsWith("_var")||r.value==="theme"||r.value.endsWith("_theme")){r.value=Ne(r.value);for(let i=0;i<r.nodes.length;i++){if(i==0&&r.nodes[i].kind==="word"){r.nodes[i].value=Ne(r.nodes[i].value,!0);continue}At([r.nodes[i]])}break}r.value=Ne(r.value),At(r.nodes);break}case"separator":case"word":{r.value=Ne(r.value);break}default:Si(r)}}function Si(t){throw new Error(`Unexpected value: ${t}`)}var Ct=new Uint8Array(256);function de(t){let r=0,i=t.length;for(let e=0;e<i;e++){let n=t.charCodeAt(e);switch(n){case 92:e+=1;break;case 39:case 34:for(;++e<i;){let s=t.charCodeAt(e);if(s===92){e+=1;continue}if(s===n)break}break;case 40:Ct[r]=41,r++;break;case 91:Ct[r]=93,r++;break;case 123:break;case 93:case 125:case 41:if(r===0)return!1;r>0&&n===Ct[r-1]&&r--;break;case 59:if(r===0)return!1;break}}return!0}var et=new Uint8Array(256);function K(t,r){let i=0,e=[],n=0,s=t.length,a=r.charCodeAt(0);for(let p=0;p<s;p++){let u=t.charCodeAt(p);if(i===0&&u===a){e.push(t.slice(n,p)),n=p+1;continue}switch(u){case 92:p+=1;break;case 39:case 34:for(;++p<s;){let c=t.charCodeAt(p);if(c===92){p+=1;continue}if(c===u)break}break;case 40:et[i]=41,i++;break;case 91:et[i]=93,i++;break;case 123:et[i]=125,i++;break;case 93:case 125:case 41:i>0&&u===et[i-1]&&i--;break}}return e.push(t.slice(n)),e}var Ti=58,nr=45,or=97,lr=122;function*ar(t,r){let i=K(t,":");if(r.theme.prefix){if(i.length===1||i[0]!==r.theme.prefix)return null;i.shift()}let e=i.pop(),n=[];for(let g=i.length-1;g>=0;--g){let h=r.parseVariant(i[g]);if(h===null)return;n.push(h)}let s=!1;e[e.length-1]==="!"?(s=!0,e=e.slice(0,-1)):e[0]==="!"&&(s=!0,e=e.slice(1)),r.utilities.has(e,"static")&&!e.includes("[")&&(yield{kind:"static",root:e,variants:n,important:s,raw:t});let[a,p=null,u]=K(e,"/");if(u)return;let c=p===null?null:Nt(p);if(p!==null&&c===null)return;if(a[0]==="["){if(a[a.length-1]!=="]")return;let g=a.charCodeAt(1);if(g!==nr&&!(g>=or&&g<=lr))return;a=a.slice(1,-1);let h=a.indexOf(":");if(h===-1||h===0||h===a.length-1)return;let w=a.slice(0,h),v=pe(a.slice(h+1));if(!de(v))return;yield{kind:"arbitrary",property:w,value:v,modifier:c,variants:n,important:s,raw:t};return}let m;if(a[a.length-1]==="]"){let g=a.indexOf("-[");if(g===-1)return;let h=a.slice(0,g);if(!r.utilities.has(h,"functional"))return;let w=a.slice(g+1);m=[[h,w]]}else if(a[a.length-1]===")"){let g=a.indexOf("-(");if(g===-1)return;let h=a.slice(0,g);if(!r.utilities.has(h,"functional"))return;let w=a.slice(g+2,-1),v=K(w,":"),A=null;if(v.length===2&&(A=v[0],w=v[1]),w[0]!=="-"&&w[1]!=="-")return;m=[[h,A===null?`[var(${w})]`:`[${A}:var(${w})]`]]}else m=ur(a,g=>r.utilities.has(g,"functional"));for(let[g,h]of m){let w={kind:"functional",root:g,modifier:c,value:null,variants:n,important:s,raw:t};if(h===null){yield w;continue}{let v=h.indexOf("[");if(v!==-1){if(h[h.length-1]!=="]")return;let b=pe(h.slice(v+1,-1));if(!de(b))continue;let y="";for(let V=0;V<b.length;V++){let T=b.charCodeAt(V);if(T===Ti){y=b.slice(0,V),b=b.slice(V+1);break}if(!(T===nr||T>=or&&T<=lr))break}if(b.length===0||b.trim().length===0)continue;w.value={kind:"arbitrary",dataType:y||null,value:b}}else{let b=p===null||w.modifier?.kind==="arbitrary"?null:`${h}/${p}`;w.value={kind:"named",value:h,fraction:b}}}yield w}}function Nt(t){if(t[0]==="["&&t[t.length-1]==="]"){let r=pe(t.slice(1,-1));return!de(r)||r.length===0||r.trim().length===0?null:{kind:"arbitrary",value:r}}if(t[0]==="("&&t[t.length-1]===")"){let r=pe(t.slice(1,-1));return!de(r)||r.length===0||r.trim().length===0||r[0]!=="-"&&r[1]!=="-"?null:{kind:"arbitrary",value:`var(${r})`}}return{kind:"named",value:t}}function sr(t,r){if(t[0]==="["&&t[t.length-1]==="]"){if(t[1]==="@"&&t.includes("&"))return null;let i=pe(t.slice(1,-1));if(!de(i)||i.length===0||i.trim().length===0)return null;let e=i[0]===">"||i[0]==="+"||i[0]==="~";return!e&&i[0]!=="@"&&!i.includes("&")&&(i=`&:is(${i})`),{kind:"arbitrary",selector:i,relative:e}}{let[i,e=null,n]=K(t,"/");if(n)return null;let s=ur(i,a=>r.variants.has(a));for(let[a,p]of s)switch(r.variants.kind(a)){case"static":return p!==null||e!==null?null:{kind:"static",root:a};case"functional":{let u=e===null?null:Nt(e);if(e!==null&&u===null)return null;if(p===null)return{kind:"functional",root:a,modifier:u,value:null};if(p[p.length-1]==="]"){if(p[0]!=="[")continue;let c=pe(p.slice(1,-1));return!de(c)||c.length===0||c.trim().length===0?null:{kind:"functional",root:a,modifier:u,value:{kind:"arbitrary",value:c}}}if(p[p.length-1]===")"){if(p[0]!=="(")continue;let c=pe(p.slice(1,-1));return!de(c)||c.length===0||c.trim().length===0||c[0]!=="-"&&c[1]!=="-"?null:{kind:"functional",root:a,modifier:u,value:{kind:"arbitrary",value:`var(${c})`}}}return{kind:"functional",root:a,modifier:u,value:{kind:"named",value:p}}}case"compound":{if(p===null)return null;let u=r.parseVariant(p);if(u===null||!r.variants.compoundsWith(a,u))return null;let c=e===null?null:Nt(e);return e!==null&&c===null?null:{kind:"compound",root:a,modifier:c,variant:u}}}}return null}function*ur(t,r){r(t)&&(yield[t,null]);let i=t.lastIndexOf("-");for(;i>0;){let e=t.slice(0,i);if(r(e)){let n=[e,t.slice(i+1)];if(n[1]==="")break;yield n}i=t.lastIndexOf("-",i-1)}t[0]==="@"&&r("@")&&(yield["@",t.slice(1)])}function be(t,r,i){if(t===r)return 0;let e=t.indexOf("("),n=r.indexOf("("),s=e===-1?t.replace(/[\d.]+/g,""):t.slice(0,e),a=n===-1?r.replace(/[\d.]+/g,""):r.slice(0,n),p=(s===a?0:s<a?-1:1)||(i==="asc"?parseInt(t)-parseInt(r):parseInt(r)-parseInt(t));return Number.isNaN(p)?t<r?-1:1:p}var Ei=new Set(["black","silver","gray","white","maroon","red","purple","fuchsia","green","lime","olive","yellow","navy","blue","teal","aqua","aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","green","greenyellow","grey","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen","transparent","currentcolor","canvas","canvastext","linktext","visitedtext","activetext","buttonface","buttontext","buttonborder","field","fieldtext","highlight","highlighttext","selecteditem","selecteditemtext","mark","marktext","graytext","accentcolor","accentcolortext"]),Ri=/^(rgba?|hsla?|hwb|color|(ok)?(lab|lch)|light-dark|color-mix)\(/i;function cr(t){return t.charCodeAt(0)===35||Ri.test(t)||Ei.has(t.toLowerCase())}var Oi={color:cr,length:tt,percentage:$t,ratio:Bi,number:pr,integer:E,url:fr,position:Gi,"bg-size":Yi,"line-width":_i,image:Di,"family-name":Ii,"generic-name":ji,"absolute-size":zi,"relative-size":Fi,angle:Zi,vector:en};function q(t,r){if(t.startsWith("var("))return null;for(let i of r)if(Oi[i]?.(t))return i;return null}var Pi=/^url\(.*\)$/;function fr(t){return Pi.test(t)}function _i(t){return K(t," ").every(r=>tt(r)||pr(r)||r==="thin"||r==="medium"||r==="thick")}var Ki=/^(?:element|image|cross-fade|image-set)\(/,Ui=/^(repeating-)?(conic|linear|radial)-gradient\(/;function Di(t){let r=0;for(let i of K(t,","))if(!i.startsWith("var(")){if(fr(i)){r+=1;continue}if(Ui.test(i)){r+=1;continue}if(Ki.test(i)){r+=1;continue}return!1}return r>0}function ji(t){return t==="serif"||t==="sans-serif"||t==="monospace"||t==="cursive"||t==="fantasy"||t==="system-ui"||t==="ui-serif"||t==="ui-sans-serif"||t==="ui-monospace"||t==="ui-rounded"||t==="math"||t==="emoji"||t==="fangsong"}function Ii(t){let r=0;for(let i of K(t,",")){let e=i.charCodeAt(0);if(e>=48&&e<=57)return!1;i.startsWith("var(")||(r+=1)}return r>0}function zi(t){return t==="xx-small"||t==="x-small"||t==="small"||t==="medium"||t==="large"||t==="x-large"||t==="xx-large"||t==="xxx-large"}function Fi(t){return t==="larger"||t==="smaller"}var ue=/[+-]?\d*\.?\d+(?:[eE][+-]?\d+)?/,Li=new RegExp(`^${ue.source}$`);function pr(t){return Li.test(t)||_e(t)}var Mi=new RegExp(`^${ue.source}%$`);function $t(t){return Mi.test(t)||_e(t)}var Wi=new RegExp(`^${ue.source}s*/s*${ue.source}$`);function Bi(t){return Wi.test(t)||_e(t)}var qi=["cm","mm","Q","in","pc","pt","px","em","ex","ch","rem","lh","rlh","vw","vh","vmin","vmax","vb","vi","svw","svh","lvw","lvh","dvw","dvh","cqw","cqh","cqi","cqb","cqmin","cqmax"],Hi=new RegExp(`^${ue.source}(${qi.join("|")})$`);function tt(t){return Hi.test(t)||_e(t)}function Gi(t){let r=0;for(let i of K(t," ")){if(i==="center"||i==="top"||i==="right"||i==="bottom"||i==="left"){r+=1;continue}if(!i.startsWith("var(")){if(tt(i)||$t(i)){r+=1;continue}return!1}}return r>0}function Yi(t){let r=0;for(let i of K(t,",")){if(i==="cover"||i==="contain"){r+=1;continue}let e=K(i," ");if(e.length!==1&&e.length!==2)return!1;if(e.every(n=>n==="auto"||tt(n)||$t(n))){r+=1;continue}}return r>0}var Ji=["deg","rad","grad","turn"],Qi=new RegExp(`^${ue.source}(${Ji.join("|")})$`);function Zi(t){return Qi.test(t)}var Xi=new RegExp(`^${ue.source} +${ue.source} +${ue.source}$`);function en(t){return Xi.test(t)}function E(t){let r=Number(t);return Number.isInteger(r)&&r>=0&&String(r)===String(t)}function Vt(t){let r=Number(t);return Number.isInteger(r)&&r>0&&String(r)===String(t)}function ye(t){return dr(t,.25)}function rt(t){return dr(t,.25)}function dr(t,r){let i=Number(t);return i>=0&&i%r===0&&String(i)===String(t)}var tn=new Set(["inset","inherit","initial","revert","unset"]),mr=/^-?(\d+|\.\d+)(.*?)$/g;function Ke(t,r){return K(t,",").map(e=>{e=e.trim();let n=K(e," ").filter(c=>c.trim()!==""),s=null,a=null,p=null;for(let c of n)tn.has(c)||(mr.test(c)?(a===null?a=c:p===null&&(p=c),mr.lastIndex=0):s===null&&(s=c));if(a===null||p===null)return e;let u=r(s??"currentcolor");return s!==null?e.replace(s,u):`${e} ${u}`}).join(", ")}var rn=/^-?[a-z][a-zA-Z0-9/%._-]*$/,nn=/^-?[a-z][a-zA-Z0-9/%._-]*-\*$/,nt=["0","0.5","1","1.5","2","2.5","3","3.5","4","5","6","7","8","9","10","11","12","14","16","20","24","28","32","36","40","44","48","52","56","60","64","72","80","96"],St=class{utilities=new B(()=>[]);completions=new Map;static(r,i){this.utilities.get(r).push({kind:"static",compileFn:i})}functional(r,i,e){this.utilities.get(r).push({kind:"functional",compileFn:i,options:e})}has(r,i){return this.utilities.has(r)&&this.utilities.get(r).some(e=>e.kind===i)}get(r){return this.utilities.has(r)?this.utilities.get(r):[]}getCompletions(r){return this.completions.get(r)?.()??[]}suggest(r,i){this.completions.set(r,i)}keys(r){let i=[];for(let[e,n]of this.utilities.entries())for(let s of n)if(s.kind===r){i.push(e);break}return i}};function C(t,r,i){return I("@property",t,[l("syntax",i?`"${i}"`:'"*"'),l("inherits","false"),...r?[l("initial-value",r)]:[]])}function G(t,r){if(r===null)return t;let i=Number(r);return Number.isNaN(i)||(r=`${i*100}%`),`color-mix(in oklab, ${t} ${r}, transparent)`}function hr(t,r){let i=Number(r);return Number.isNaN(i)||(r=`${i*100}%`),`oklab(from ${t} l a b / ${r})`}function Q(t,r,i){if(!r)return t;if(r.kind==="arbitrary")return G(t,r.value);let e=i.resolve(r.value,["--opacity"]);return e?G(t,e):rt(r.value)?G(t,`${r.value}%`):null}function Z(t,r,i){let e=null;switch(t.value.value){case"inherit":{e="inherit";break}case"transparent":{e="transparent";break}case"current":{e="currentcolor";break}default:{e=r.resolve(t.value.value,i);break}}return e?Q(e,t.modifier,r):null}function vr(t){let r=new St;function i(o,f){let d=/(\d+)_(\d+)/g;function*x(N){for(let R of t.keysInNamespaces(N))yield R.replace(d,($,S,U)=>`${S}.${U}`)}let k=["1/2","1/3","2/3","1/4","2/4","3/4","1/5","2/5","3/5","4/5","1/6","2/6","3/6","4/6","5/6","1/12","2/12","3/12","4/12","5/12","6/12","7/12","8/12","9/12","10/12","11/12"];r.suggest(o,()=>{let N=[];for(let R of f()){if(typeof R=="string"){N.push({values:[R],modifiers:[]});continue}let $=[...R.values??[],...x(R.valueThemeKeys??[])],S=[...R.modifiers??[],...x(R.modifierThemeKeys??[])];R.supportsFractions&&$.push(...k),R.hasDefaultValue&&$.unshift(null),N.push({supportsNegative:R.supportsNegative,values:$,modifiers:S})}return N})}function e(o,f){r.static(o,()=>f.map(d=>typeof d=="function"?d():l(d[0],d[1])))}function n(o,f){function d({negative:x}){return k=>{let N=null,R=null;if(k.value)if(k.value.kind==="arbitrary"){if(k.modifier)return;N=k.value.value,R=k.value.dataType}else{if(N=t.resolve(k.value.fraction??k.value.value,f.themeKeys??[]),N===null&&f.supportsFractions&&k.value.fraction){let[$,S]=K(k.value.fraction,"/");if(!E($)||!E(S))return;N=`calc(${k.value.fraction} * 100%)`}if(N===null&&x&&f.handleNegativeBareValue){if(N=f.handleNegativeBareValue(k.value),!N?.includes("/")&&k.modifier)return;if(N!==null)return f.handle(N,null)}if(N===null&&f.handleBareValue&&(N=f.handleBareValue(k.value),!N?.includes("/")&&k.modifier))return}else{if(k.modifier)return;N=f.defaultValue!==void 0?f.defaultValue:t.resolve(null,f.themeKeys??[])}if(N!==null)return f.handle(x?`calc(${N} * -1)`:N,R)}}f.supportsNegative&&r.functional(`-${o}`,d({negative:!0})),r.functional(o,d({negative:!1})),i(o,()=>[{supportsNegative:f.supportsNegative,valueThemeKeys:f.themeKeys??[],hasDefaultValue:f.defaultValue!==void 0&&f.defaultValue!==null,supportsFractions:f.supportsFractions}])}function s(o,f){r.functional(o,d=>{if(!d.value)return;let x=null;if(d.value.kind==="arbitrary"?(x=d.value.value,x=Q(x,d.modifier,t)):x=Z(d,t,f.themeKeys),x!==null)return f.handle(x)}),i(o,()=>[{values:["current","inherit","transparent"],valueThemeKeys:f.themeKeys,modifiers:Array.from({length:21},(d,x)=>`${x*5}`)}])}function a(o,f,d,{supportsNegative:x=!1,supportsFractions:k=!1}={}){x&&r.static(`-${o}-px`,()=>d("-1px")),r.static(`${o}-px`,()=>d("1px")),n(o,{themeKeys:f,supportsFractions:k,supportsNegative:x,defaultValue:null,handleBareValue:({value:N})=>{let R=t.resolve(null,["--spacing"]);return!R||!ye(N)?null:`calc(${R} * ${N})`},handleNegativeBareValue:({value:N})=>{let R=t.resolve(null,["--spacing"]);return!R||!ye(N)?null:`calc(${R} * -${N})`},handle:d}),i(o,()=>[{values:t.get(["--spacing"])?nt:[],supportsNegative:x,supportsFractions:k,valueThemeKeys:f}])}e("sr-only",[["position","absolute"],["width","1px"],["height","1px"],["padding","0"],["margin","-1px"],["overflow","hidden"],["clip","rect(0, 0, 0, 0)"],["white-space","nowrap"],["border-width","0"]]),e("not-sr-only",[["position","static"],["width","auto"],["height","auto"],["padding","0"],["margin","0"],["overflow","visible"],["clip","auto"],["white-space","normal"]]),e("pointer-events-none",[["pointer-events","none"]]),e("pointer-events-auto",[["pointer-events","auto"]]),e("visible",[["visibility","visible"]]),e("invisible",[["visibility","hidden"]]),e("collapse",[["visibility","collapse"]]),e("static",[["position","static"]]),e("fixed",[["position","fixed"]]),e("absolute",[["position","absolute"]]),e("relative",[["position","relative"]]),e("sticky",[["position","sticky"]]);for(let[o,f]of[["inset","inset"],["inset-x","inset-inline"],["inset-y","inset-block"],["start","inset-inline-start"],["end","inset-inline-end"],["top","top"],["right","right"],["bottom","bottom"],["left","left"]])e(`${o}-auto`,[[f,"auto"]]),e(`${o}-full`,[[f,"100%"]]),e(`-${o}-full`,[[f,"-100%"]]),a(o,["--inset","--spacing"],d=>[l(f,d)],{supportsNegative:!0,supportsFractions:!0});e("isolate",[["isolation","isolate"]]),e("isolation-auto",[["isolation","auto"]]),e("z-auto",[["z-index","auto"]]),n("z",{supportsNegative:!0,handleBareValue:({value:o})=>E(o)?o:null,themeKeys:["--z-index"],handle:o=>[l("z-index",o)]}),i("z",()=>[{supportsNegative:!0,values:["0","10","20","30","40","50"],valueThemeKeys:["--z-index"]}]),e("order-first",[["order","-9999"]]),e("order-last",[["order","9999"]]),e("order-none",[["order","0"]]),n("order",{supportsNegative:!0,handleBareValue:({value:o})=>E(o)?o:null,themeKeys:["--order"],handle:o=>[l("order",o)]}),i("order",()=>[{supportsNegative:!0,values:Array.from({length:12},(o,f)=>`${f+1}`),valueThemeKeys:["--order"]}]),e("col-auto",[["grid-column","auto"]]),n("col",{supportsNegative:!0,handleBareValue:({value:o})=>E(o)?o:null,themeKeys:["--grid-column"],handle:o=>[l("grid-column",o)]}),e("col-span-full",[["grid-column","1 / -1"]]),n("col-span",{handleBareValue:({value:o})=>E(o)?o:null,handle:o=>[l("grid-column",`span ${o} / span ${o}`)]}),e("col-start-auto",[["grid-column-start","auto"]]),n("col-start",{supportsNegative:!0,handleBareValue:({value:o})=>E(o)?o:null,themeKeys:["--grid-column-start"],handle:o=>[l("grid-column-start",o)]}),e("col-end-auto",[["grid-column-end","auto"]]),n("col-end",{supportsNegative:!0,handleBareValue:({value:o})=>E(o)?o:null,themeKeys:["--grid-column-end"],handle:o=>[l("grid-column-end",o)]}),i("col-span",()=>[{values:Array.from({length:12},(o,f)=>`${f+1}`),valueThemeKeys:[]}]),i("col-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,f)=>`${f+1}`),valueThemeKeys:["--grid-column-start"]}]),i("col-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,f)=>`${f+1}`),valueThemeKeys:["--grid-column-end"]}]),e("row-auto",[["grid-row","auto"]]),n("row",{supportsNegative:!0,handleBareValue:({value:o})=>E(o)?o:null,themeKeys:["--grid-row"],handle:o=>[l("grid-row",o)]}),e("row-span-full",[["grid-row","1 / -1"]]),n("row-span",{themeKeys:[],handleBareValue:({value:o})=>E(o)?o:null,handle:o=>[l("grid-row",`span ${o} / span ${o}`)]}),e("row-start-auto",[["grid-row-start","auto"]]),n("row-start",{supportsNegative:!0,handleBareValue:({value:o})=>E(o)?o:null,themeKeys:["--grid-row-start"],handle:o=>[l("grid-row-start",o)]}),e("row-end-auto",[["grid-row-end","auto"]]),n("row-end",{supportsNegative:!0,handleBareValue:({value:o})=>E(o)?o:null,themeKeys:["--grid-row-end"],handle:o=>[l("grid-row-end",o)]}),i("row-span",()=>[{values:Array.from({length:12},(o,f)=>`${f+1}`),valueThemeKeys:[]}]),i("row-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,f)=>`${f+1}`),valueThemeKeys:["--grid-row-start"]}]),i("row-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,f)=>`${f+1}`),valueThemeKeys:["--grid-row-end"]}]),e("float-start",[["float","inline-start"]]),e("float-end",[["float","inline-end"]]),e("float-right",[["float","right"]]),e("float-left",[["float","left"]]),e("float-none",[["float","none"]]),e("clear-start",[["clear","inline-start"]]),e("clear-end",[["clear","inline-end"]]),e("clear-right",[["clear","right"]]),e("clear-left",[["clear","left"]]),e("clear-both",[["clear","both"]]),e("clear-none",[["clear","none"]]);for(let[o,f]of[["m","margin"],["mx","margin-inline"],["my","margin-block"],["ms","margin-inline-start"],["me","margin-inline-end"],["mt","margin-top"],["mr","margin-right"],["mb","margin-bottom"],["ml","margin-left"]])e(`${o}-auto`,[[f,"auto"]]),a(o,["--margin","--spacing"],d=>[l(f,d)],{supportsNegative:!0});e("box-border",[["box-sizing","border-box"]]),e("box-content",[["box-sizing","content-box"]]),e("line-clamp-none",[["overflow","visible"],["display","block"],["-webkit-box-orient","horizontal"],["-webkit-line-clamp","unset"]]),n("line-clamp",{themeKeys:["--line-clamp"],handleBareValue:({value:o})=>E(o)?o:null,handle:o=>[l("overflow","hidden"),l("display","-webkit-box"),l("-webkit-box-orient","vertical"),l("-webkit-line-clamp",o)]}),i("line-clamp",()=>[{values:["1","2","3","4","5","6"],valueThemeKeys:["--line-clamp"]}]),e("block",[["display","block"]]),e("inline-block",[["display","inline-block"]]),e("inline",[["display","inline"]]),e("hidden",[["display","none"]]),e("inline-flex",[["display","inline-flex"]]),e("table",[["display","table"]]),e("inline-table",[["display","inline-table"]]),e("table-caption",[["display","table-caption"]]),e("table-cell",[["display","table-cell"]]),e("table-column",[["display","table-column"]]),e("table-column-group",[["display","table-column-group"]]),e("table-footer-group",[["display","table-footer-group"]]),e("table-header-group",[["display","table-header-group"]]),e("table-row-group",[["display","table-row-group"]]),e("table-row",[["display","table-row"]]),e("flow-root",[["display","flow-root"]]),e("flex",[["display","flex"]]),e("grid",[["display","grid"]]),e("inline-grid",[["display","inline-grid"]]),e("contents",[["display","contents"]]),e("list-item",[["display","list-item"]]),e("field-sizing-content",[["field-sizing","content"]]),e("field-sizing-fixed",[["field-sizing","fixed"]]),e("aspect-auto",[["aspect-ratio","auto"]]),e("aspect-square",[["aspect-ratio","1 / 1"]]),n("aspect",{themeKeys:["--aspect"],handleBareValue:({fraction:o})=>{if(o===null)return null;let[f,d]=K(o,"/");return!E(f)||!E(d)?null:o},handle:o=>[l("aspect-ratio",o)]});for(let[o,f]of[["auto","auto"],["full","100%"],["svw","100svw"],["lvw","100lvw"],["dvw","100dvw"],["svh","100svh"],["lvh","100lvh"],["dvh","100dvh"],["min","min-content"],["max","max-content"],["fit","fit-content"]])e(`size-${o}`,[["--tw-sort","size"],["width",f],["height",f]]),e(`w-${o}`,[["width",f]]),e(`h-${o}`,[["height",f]]),e(`min-w-${o}`,[["min-width",f]]),e(`min-h-${o}`,[["min-height",f]]),o!=="auto"&&(e(`max-w-${o}`,[["max-width",f]]),e(`max-h-${o}`,[["max-height",f]]));e("w-screen",[["width","100vw"]]),e("min-w-screen",[["min-width","100vw"]]),e("max-w-screen",[["max-width","100vw"]]),e("h-screen",[["height","100vh"]]),e("min-h-screen",[["min-height","100vh"]]),e("max-h-screen",[["max-height","100vh"]]),e("max-w-none",[["max-width","none"]]),e("max-h-none",[["max-height","none"]]),a("size",["--size","--spacing"],o=>[l("--tw-sort","size"),l("width",o),l("height",o)],{supportsFractions:!0});for(let[o,f,d]of[["w",["--width","--spacing","--container"],"width"],["min-w",["--min-width","--spacing","--container"],"min-width"],["max-w",["--max-width","--spacing","--container"],"max-width"],["h",["--height","--spacing"],"height"],["min-h",["--min-height","--height","--spacing"],"min-height"],["max-h",["--max-height","--height","--spacing"],"max-height"]])a(o,f,x=>[l(d,x)],{supportsFractions:!0});r.static("container",()=>{let o=[...t.namespace("--breakpoint").values()];o.sort((d,x)=>be(d,x,"asc"));let f=[l("--tw-sort","--tw-container-component"),l("width","100%")];for(let d of o)f.push(I("@media",`(width >= ${d})`,[l("max-width",d)]));return f}),e("flex-auto",[["flex","auto"]]),e("flex-initial",[["flex","0 auto"]]),e("flex-none",[["flex","none"]]),r.functional("flex",o=>{if(o.value){if(o.value.kind==="arbitrary")return o.modifier?void 0:[l("flex",o.value.value)];if(o.value.fraction){let[f,d]=K(o.value.fraction,"/");return!E(f)||!E(d)?void 0:[l("flex",`calc(${o.value.fraction} * 100%)`)]}if(E(o.value.value))return o.modifier?void 0:[l("flex",o.value.value)]}}),i("flex",()=>[{supportsFractions:!0}]),n("shrink",{defaultValue:"1",handleBareValue:({value:o})=>E(o)?o:null,handle:o=>[l("flex-shrink",o)]}),n("grow",{defaultValue:"1",handleBareValue:({value:o})=>E(o)?o:null,handle:o=>[l("flex-grow",o)]}),i("shrink",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),i("grow",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),e("basis-auto",[["flex-basis","auto"]]),e("basis-full",[["flex-basis","100%"]]),a("basis",["--flex-basis","--spacing","--container"],o=>[l("flex-basis",o)],{supportsFractions:!0}),e("table-auto",[["table-layout","auto"]]),e("table-fixed",[["table-layout","fixed"]]),e("caption-top",[["caption-side","top"]]),e("caption-bottom",[["caption-side","bottom"]]),e("border-collapse",[["border-collapse","collapse"]]),e("border-separate",[["border-collapse","separate"]]);let p=()=>D([C("--tw-border-spacing-x","0","<length>"),C("--tw-border-spacing-y","0","<length>")]);a("border-spacing",["--border-spacing","--spacing"],o=>[p(),l("--tw-border-spacing-x",o),l("--tw-border-spacing-y",o),l("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),a("border-spacing-x",["--border-spacing","--spacing"],o=>[p(),l("--tw-border-spacing-x",o),l("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),a("border-spacing-y",["--border-spacing","--spacing"],o=>[p(),l("--tw-border-spacing-y",o),l("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),e("origin-center",[["transform-origin","center"]]),e("origin-top",[["transform-origin","top"]]),e("origin-top-right",[["transform-origin","top right"]]),e("origin-right",[["transform-origin","right"]]),e("origin-bottom-right",[["transform-origin","bottom right"]]),e("origin-bottom",[["transform-origin","bottom"]]),e("origin-bottom-left",[["transform-origin","bottom left"]]),e("origin-left",[["transform-origin","left"]]),e("origin-top-left",[["transform-origin","top left"]]),n("origin",{themeKeys:["--transform-origin"],handle:o=>[l("transform-origin",o)]}),e("perspective-origin-center",[["perspective-origin","center"]]),e("perspective-origin-top",[["perspective-origin","top"]]),e("perspective-origin-top-right",[["perspective-origin","top right"]]),e("perspective-origin-right",[["perspective-origin","right"]]),e("perspective-origin-bottom-right",[["perspective-origin","bottom right"]]),e("perspective-origin-bottom",[["perspective-origin","bottom"]]),e("perspective-origin-bottom-left",[["perspective-origin","bottom left"]]),e("perspective-origin-left",[["perspective-origin","left"]]),e("perspective-origin-top-left",[["perspective-origin","top left"]]),n("perspective-origin",{themeKeys:["--perspective-origin"],handle:o=>[l("perspective-origin",o)]}),e("perspective-none",[["perspective","none"]]),n("perspective",{themeKeys:["--perspective"],handle:o=>[l("perspective",o)]});let u=()=>D([C("--tw-translate-x","0"),C("--tw-translate-y","0"),C("--tw-translate-z","0")]);e("translate-none",[["translate","none"]]),e("-translate-full",[u,["--tw-translate-x","-100%"],["--tw-translate-y","-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),e("translate-full",[u,["--tw-translate-x","100%"],["--tw-translate-y","100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),a("translate",["--translate","--spacing"],o=>[u(),l("--tw-translate-x",o),l("--tw-translate-y",o),l("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});for(let o of["x","y"])e(`-translate-${o}-full`,[u,[`--tw-translate-${o}`,"-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),e(`translate-${o}-full`,[u,[`--tw-translate-${o}`,"100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),a(`translate-${o}`,["--translate","--spacing"],f=>[u(),l(`--tw-translate-${o}`,f),l("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});a("translate-z",["--translate","--spacing"],o=>[u(),l("--tw-translate-z",o),l("translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)")],{supportsNegative:!0}),e("translate-3d",[u,["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]);let c=()=>D([C("--tw-scale-x","1"),C("--tw-scale-y","1"),C("--tw-scale-z","1")]);e("scale-none",[["scale","none"]]);function m({negative:o}){return f=>{if(!f.value||f.modifier)return;let d;return f.value.kind==="arbitrary"?(d=f.value.value,[l("scale",d)]):(d=t.resolve(f.value.value,["--scale"]),!d&&E(f.value.value)&&(d=`${f.value.value}%`),d?(d=o?`calc(${d} * -1)`:d,[c(),l("--tw-scale-x",d),l("--tw-scale-y",d),l("--tw-scale-z",d),l("scale","var(--tw-scale-x) var(--tw-scale-y)")]):void 0)}}r.functional("-scale",m({negative:!0})),r.functional("scale",m({negative:!1})),i("scale",()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);for(let o of["x","y","z"])n(`scale-${o}`,{supportsNegative:!0,themeKeys:["--scale"],handleBareValue:({value:f})=>E(f)?`${f}%`:null,handle:f=>[c(),l(`--tw-scale-${o}`,f),l("scale",`var(--tw-scale-x) var(--tw-scale-y)${o==="z"?" var(--tw-scale-z)":""}`)]}),i(`scale-${o}`,()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);e("scale-3d",[c,["scale","var(--tw-scale-x) var(--tw-scale-y) var(--tw-scale-z)"]]),e("rotate-none",[["rotate","none"]]);function g({negative:o}){return f=>{if(!f.value||f.modifier)return;let d;if(f.value.kind==="arbitrary"){d=f.value.value;let x=f.value.dataType??q(d,["angle","vector"]);if(x==="vector")return[l("rotate",`${d} var(--tw-rotate)`)];if(x!=="angle")return[l("rotate",d)]}else if(d=t.resolve(f.value.value,["--rotate"]),!d&&E(f.value.value)&&(d=`${f.value.value}deg`),!d)return;return[l("rotate",o?`calc(${d} * -1)`:d)]}}r.functional("-rotate",g({negative:!0})),r.functional("rotate",g({negative:!1})),i("rotate",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);{let o=["var(--tw-rotate-x,)","var(--tw-rotate-y,)","var(--tw-rotate-z,)","var(--tw-skew-x,)","var(--tw-skew-y,)"].join(" "),f=()=>D([C("--tw-rotate-x"),C("--tw-rotate-y"),C("--tw-rotate-z"),C("--tw-skew-x"),C("--tw-skew-y")]);for(let d of["x","y","z"])n(`rotate-${d}`,{supportsNegative:!0,themeKeys:["--rotate"],handleBareValue:({value:x})=>E(x)?`${x}deg`:null,handle:x=>[f(),l(`--tw-rotate-${d}`,`rotate${d.toUpperCase()}(${x})`),l("transform",o)]}),i(`rotate-${d}`,()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);n("skew",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:d})=>E(d)?`${d}deg`:null,handle:d=>[f(),l("--tw-skew-x",`skewX(${d})`),l("--tw-skew-y",`skewY(${d})`),l("transform",o)]}),n("skew-x",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:d})=>E(d)?`${d}deg`:null,handle:d=>[f(),l("--tw-skew-x",`skewX(${d})`),l("transform",o)]}),n("skew-y",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:d})=>E(d)?`${d}deg`:null,handle:d=>[f(),l("--tw-skew-y",`skewY(${d})`),l("transform",o)]}),i("skew",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),i("skew-x",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),i("skew-y",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),r.functional("transform",d=>{if(d.modifier)return;let x=null;if(d.value?d.value.kind==="arbitrary"&&(x=d.value.value):x=o,x!==null)return[f(),l("transform",x)]}),i("transform",()=>[{hasDefaultValue:!0}]),e("transform-cpu",[["transform",o]]),e("transform-gpu",[["transform",`translateZ(0) ${o}`]]),e("transform-none",[["transform","none"]])}e("transform-flat",[["transform-style","flat"]]),e("transform-3d",[["transform-style","preserve-3d"]]),e("transform-content",[["transform-box","content-box"]]),e("transform-border",[["transform-box","border-box"]]),e("transform-fill",[["transform-box","fill-box"]]),e("transform-stroke",[["transform-box","stroke-box"]]),e("transform-view",[["transform-box","view-box"]]),e("backface-visible",[["backface-visibility","visible"]]),e("backface-hidden",[["backface-visibility","hidden"]]);for(let o of["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out"])e(`cursor-${o}`,[["cursor",o]]);n("cursor",{themeKeys:["--cursor"],handle:o=>[l("cursor",o)]});for(let o of["auto","none","manipulation"])e(`touch-${o}`,[["touch-action",o]]);let h=()=>D([C("--tw-pan-x"),C("--tw-pan-y"),C("--tw-pinch-zoom")]);for(let o of["x","left","right"])e(`touch-pan-${o}`,[h,["--tw-pan-x",`pan-${o}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let o of["y","up","down"])e(`touch-pan-${o}`,[h,["--tw-pan-y",`pan-${o}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);e("touch-pinch-zoom",[h,["--tw-pinch-zoom","pinch-zoom"],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let o of["none","text","all","auto"])e(`select-${o}`,[["-webkit-user-select",o],["user-select",o]]);e("resize-none",[["resize","none"]]),e("resize-x",[["resize","horizontal"]]),e("resize-y",[["resize","vertical"]]),e("resize",[["resize","both"]]),e("snap-none",[["scroll-snap-type","none"]]);let w=()=>D([C("--tw-scroll-snap-strictness","proximity","*")]);for(let o of["x","y","both"])e(`snap-${o}`,[w,["scroll-snap-type",`${o} var(--tw-scroll-snap-strictness)`]]);e("snap-mandatory",[w,["--tw-scroll-snap-strictness","mandatory"]]),e("snap-proximity",[w,["--tw-scroll-snap-strictness","proximity"]]),e("snap-align-none",[["scroll-snap-align","none"]]),e("snap-start",[["scroll-snap-align","start"]]),e("snap-end",[["scroll-snap-align","end"]]),e("snap-center",[["scroll-snap-align","center"]]),e("snap-normal",[["scroll-snap-stop","normal"]]),e("snap-always",[["scroll-snap-stop","always"]]);for(let[o,f]of[["scroll-m","scroll-margin"],["scroll-mx","scroll-margin-inline"],["scroll-my","scroll-margin-block"],["scroll-ms","scroll-margin-inline-start"],["scroll-me","scroll-margin-inline-end"],["scroll-mt","scroll-margin-top"],["scroll-mr","scroll-margin-right"],["scroll-mb","scroll-margin-bottom"],["scroll-ml","scroll-margin-left"]])a(o,["--scroll-margin","--spacing"],d=>[l(f,d)],{supportsNegative:!0});for(let[o,f]of[["scroll-p","scroll-padding"],["scroll-px","scroll-padding-inline"],["scroll-py","scroll-padding-block"],["scroll-ps","scroll-padding-inline-start"],["scroll-pe","scroll-padding-inline-end"],["scroll-pt","scroll-padding-top"],["scroll-pr","scroll-padding-right"],["scroll-pb","scroll-padding-bottom"],["scroll-pl","scroll-padding-left"]])a(o,["--scroll-padding","--spacing"],d=>[l(f,d)]);e("list-inside",[["list-style-position","inside"]]),e("list-outside",[["list-style-position","outside"]]),e("list-none",[["list-style-type","none"]]),e("list-disc",[["list-style-type","disc"]]),e("list-decimal",[["list-style-type","decimal"]]),n("list",{themeKeys:["--list-style-type"],handle:o=>[l("list-style-type",o)]}),e("list-image-none",[["list-style-image","none"]]),n("list-image",{themeKeys:["--list-style-image"],handle:o=>[l("list-style-image",o)]}),e("appearance-none",[["appearance","none"]]),e("appearance-auto",[["appearance","auto"]]),e("scheme-normal",[["color-scheme","normal"]]),e("scheme-dark",[["color-scheme","dark"]]),e("scheme-light",[["color-scheme","light"]]),e("scheme-light-dark",[["color-scheme","light dark"]]),e("scheme-only-dark",[["color-scheme","only dark"]]),e("scheme-only-light",[["color-scheme","only light"]]),e("columns-auto",[["columns","auto"]]),n("columns",{themeKeys:["--columns","--container"],handleBareValue:({value:o})=>E(o)?o:null,handle:o=>[l("columns",o)]}),i("columns",()=>[{values:Array.from({length:12},(o,f)=>`${f+1}`),valueThemeKeys:["--columns","--container"]}]);for(let o of["auto","avoid","all","avoid-page","page","left","right","column"])e(`break-before-${o}`,[["break-before",o]]);for(let o of["auto","avoid","avoid-page","avoid-column"])e(`break-inside-${o}`,[["break-inside",o]]);for(let o of["auto","avoid","all","avoid-page","page","left","right","column"])e(`break-after-${o}`,[["break-after",o]]);e("grid-flow-row",[["grid-auto-flow","row"]]),e("grid-flow-col",[["grid-auto-flow","column"]]),e("grid-flow-dense",[["grid-auto-flow","dense"]]),e("grid-flow-row-dense",[["grid-auto-flow","row dense"]]),e("grid-flow-col-dense",[["grid-auto-flow","column dense"]]),e("auto-cols-auto",[["grid-auto-columns","auto"]]),e("auto-cols-min",[["grid-auto-columns","min-content"]]),e("auto-cols-max",[["grid-auto-columns","max-content"]]),e("auto-cols-fr",[["grid-auto-columns","minmax(0, 1fr)"]]),n("auto-cols",{themeKeys:["--grid-auto-columns"],handle:o=>[l("grid-auto-columns",o)]}),e("auto-rows-auto",[["grid-auto-rows","auto"]]),e("auto-rows-min",[["grid-auto-rows","min-content"]]),e("auto-rows-max",[["grid-auto-rows","max-content"]]),e("auto-rows-fr",[["grid-auto-rows","minmax(0, 1fr)"]]),n("auto-rows",{themeKeys:["--grid-auto-rows"],handle:o=>[l("grid-auto-rows",o)]}),e("grid-cols-none",[["grid-template-columns","none"]]),e("grid-cols-subgrid",[["grid-template-columns","subgrid"]]),n("grid-cols",{themeKeys:["--grid-template-columns"],handleBareValue:({value:o})=>Vt(o)?`repeat(${o}, minmax(0, 1fr))`:null,handle:o=>[l("grid-template-columns",o)]}),e("grid-rows-none",[["grid-template-rows","none"]]),e("grid-rows-subgrid",[["grid-template-rows","subgrid"]]),n("grid-rows",{themeKeys:["--grid-template-rows"],handleBareValue:({value:o})=>Vt(o)?`repeat(${o}, minmax(0, 1fr))`:null,handle:o=>[l("grid-template-rows",o)]}),i("grid-cols",()=>[{values:Array.from({length:12},(o,f)=>`${f+1}`),valueThemeKeys:["--grid-template-columns"]}]),i("grid-rows",()=>[{values:Array.from({length:12},(o,f)=>`${f+1}`),valueThemeKeys:["--grid-template-rows"]}]),e("flex-row",[["flex-direction","row"]]),e("flex-row-reverse",[["flex-direction","row-reverse"]]),e("flex-col",[["flex-direction","column"]]),e("flex-col-reverse",[["flex-direction","column-reverse"]]),e("flex-wrap",[["flex-wrap","wrap"]]),e("flex-nowrap",[["flex-wrap","nowrap"]]),e("flex-wrap-reverse",[["flex-wrap","wrap-reverse"]]),e("place-content-center",[["place-content","center"]]),e("place-content-start",[["place-content","start"]]),e("place-content-end",[["place-content","end"]]),e("place-content-center-safe",[["place-content","safe center"]]),e("place-content-end-safe",[["place-content","safe end"]]),e("place-content-between",[["place-content","space-between"]]),e("place-content-around",[["place-content","space-around"]]),e("place-content-evenly",[["place-content","space-evenly"]]),e("place-content-baseline",[["place-content","baseline"]]),e("place-content-stretch",[["place-content","stretch"]]),e("place-items-center",[["place-items","center"]]),e("place-items-start",[["place-items","start"]]),e("place-items-end",[["place-items","end"]]),e("place-items-center-safe",[["place-items","safe center"]]),e("place-items-end-safe",[["place-items","safe end"]]),e("place-items-baseline",[["place-items","baseline"]]),e("place-items-stretch",[["place-items","stretch"]]),e("content-normal",[["align-content","normal"]]),e("content-center",[["align-content","center"]]),e("content-start",[["align-content","flex-start"]]),e("content-end",[["align-content","flex-end"]]),e("content-center-safe",[["align-content","safe center"]]),e("content-end-safe",[["align-content","safe flex-end"]]),e("content-between",[["align-content","space-between"]]),e("content-around",[["align-content","space-around"]]),e("content-evenly",[["align-content","space-evenly"]]),e("content-baseline",[["align-content","baseline"]]),e("content-stretch",[["align-content","stretch"]]),e("items-center",[["align-items","center"]]),e("items-start",[["align-items","flex-start"]]),e("items-end",[["align-items","flex-end"]]),e("items-center-safe",[["align-items","safe center"]]),e("items-end-safe",[["align-items","safe flex-end"]]),e("items-baseline",[["align-items","baseline"]]),e("items-baseline-last",[["align-items","last baseline"]]),e("items-stretch",[["align-items","stretch"]]),e("justify-normal",[["justify-content","normal"]]),e("justify-center",[["justify-content","center"]]),e("justify-start",[["justify-content","flex-start"]]),e("justify-end",[["justify-content","flex-end"]]),e("justify-center-safe",[["justify-content","safe center"]]),e("justify-end-safe",[["justify-content","safe flex-end"]]),e("justify-between",[["justify-content","space-between"]]),e("justify-around",[["justify-content","space-around"]]),e("justify-evenly",[["justify-content","space-evenly"]]),e("justify-baseline",[["justify-content","baseline"]]),e("justify-stretch",[["justify-content","stretch"]]),e("justify-items-normal",[["justify-items","normal"]]),e("justify-items-center",[["justify-items","center"]]),e("justify-items-start",[["justify-items","start"]]),e("justify-items-end",[["justify-items","end"]]),e("justify-items-center-safe",[["justify-items","safe center"]]),e("justify-items-end-safe",[["justify-items","safe end"]]),e("justify-items-stretch",[["justify-items","stretch"]]),a("gap",["--gap","--spacing"],o=>[l("gap",o)]),a("gap-x",["--gap","--spacing"],o=>[l("column-gap",o)]),a("gap-y",["--gap","--spacing"],o=>[l("row-gap",o)]),a("space-x",["--space","--spacing"],o=>[D([C("--tw-space-x-reverse","0")]),L(":where(& > :not(:last-child))",[l("--tw-sort","row-gap"),l("--tw-space-x-reverse","0"),l("margin-inline-start",`calc(${o} * var(--tw-space-x-reverse))`),l("margin-inline-end",`calc(${o} * calc(1 - var(--tw-space-x-reverse)))`)])],{supportsNegative:!0}),a("space-y",["--space","--spacing"],o=>[D([C("--tw-space-y-reverse","0")]),L(":where(& > :not(:last-child))",[l("--tw-sort","column-gap"),l("--tw-space-y-reverse","0"),l("margin-block-start",`calc(${o} * var(--tw-space-y-reverse))`),l("margin-block-end",`calc(${o} * calc(1 - var(--tw-space-y-reverse)))`)])],{supportsNegative:!0}),e("space-x-reverse",[()=>D([C("--tw-space-x-reverse","0")]),()=>L(":where(& > :not(:last-child))",[l("--tw-sort","row-gap"),l("--tw-space-x-reverse","1")])]),e("space-y-reverse",[()=>D([C("--tw-space-y-reverse","0")]),()=>L(":where(& > :not(:last-child))",[l("--tw-sort","column-gap"),l("--tw-space-y-reverse","1")])]),e("accent-auto",[["accent-color","auto"]]),s("accent",{themeKeys:["--accent-color","--color"],handle:o=>[l("accent-color",o)]}),s("caret",{themeKeys:["--caret-color","--color"],handle:o=>[l("caret-color",o)]}),s("divide",{themeKeys:["--divide-color","--color"],handle:o=>[L(":where(& > :not(:last-child))",[l("--tw-sort","divide-color"),l("border-color",o)])]}),e("place-self-auto",[["place-self","auto"]]),e("place-self-start",[["place-self","start"]]),e("place-self-end",[["place-self","end"]]),e("place-self-center",[["place-self","center"]]),e("place-self-end-safe",[["place-self","safe end"]]),e("place-self-center-safe",[["place-self","safe center"]]),e("place-self-stretch",[["place-self","stretch"]]),e("self-auto",[["align-self","auto"]]),e("self-start",[["align-self","flex-start"]]),e("self-end",[["align-self","flex-end"]]),e("self-center",[["align-self","center"]]),e("self-end-safe",[["align-self","safe flex-end"]]),e("self-center-safe",[["align-self","safe center"]]),e("self-stretch",[["align-self","stretch"]]),e("self-baseline",[["align-self","baseline"]]),e("self-baseline-last",[["align-self","last baseline"]]),e("justify-self-auto",[["justify-self","auto"]]),e("justify-self-start",[["justify-self","flex-start"]]),e("justify-self-end",[["justify-self","flex-end"]]),e("justify-self-center",[["justify-self","center"]]),e("justify-self-end-safe",[["justify-self","safe flex-end"]]),e("justify-self-center-safe",[["justify-self","safe center"]]),e("justify-self-stretch",[["justify-self","stretch"]]);for(let o of["auto","hidden","clip","visible","scroll"])e(`overflow-${o}`,[["overflow",o]]),e(`overflow-x-${o}`,[["overflow-x",o]]),e(`overflow-y-${o}`,[["overflow-y",o]]);for(let o of["auto","contain","none"])e(`overscroll-${o}`,[["overscroll-behavior",o]]),e(`overscroll-x-${o}`,[["overscroll-behavior-x",o]]),e(`overscroll-y-${o}`,[["overscroll-behavior-y",o]]);e("scroll-auto",[["scroll-behavior","auto"]]),e("scroll-smooth",[["scroll-behavior","smooth"]]),e("truncate",[["overflow","hidden"],["text-overflow","ellipsis"],["white-space","nowrap"]]),e("text-ellipsis",[["text-overflow","ellipsis"]]),e("text-clip",[["text-overflow","clip"]]),e("hyphens-none",[["-webkit-hyphens","none"],["hyphens","none"]]),e("hyphens-manual",[["-webkit-hyphens","manual"],["hyphens","manual"]]),e("hyphens-auto",[["-webkit-hyphens","auto"],["hyphens","auto"]]),e("whitespace-normal",[["white-space","normal"]]),e("whitespace-nowrap",[["white-space","nowrap"]]),e("whitespace-pre",[["white-space","pre"]]),e("whitespace-pre-line",[["white-space","pre-line"]]),e("whitespace-pre-wrap",[["white-space","pre-wrap"]]),e("whitespace-break-spaces",[["white-space","break-spaces"]]),e("text-wrap",[["text-wrap","wrap"]]),e("text-nowrap",[["text-wrap","nowrap"]]),e("text-balance",[["text-wrap","balance"]]),e("text-pretty",[["text-wrap","pretty"]]),e("break-normal",[["overflow-wrap","normal"],["word-break","normal"]]),e("break-words",[["overflow-wrap","break-word"]]),e("break-all",[["word-break","break-all"]]),e("break-keep",[["word-break","keep-all"]]),e("wrap-anywhere",[["overflow-wrap","anywhere"]]),e("wrap-break-word",[["overflow-wrap","break-word"]]),e("wrap-normal",[["overflow-wrap","normal"]]);for(let[o,f]of[["rounded",["border-radius"]],["rounded-s",["border-start-start-radius","border-end-start-radius"]],["rounded-e",["border-start-end-radius","border-end-end-radius"]],["rounded-t",["border-top-left-radius","border-top-right-radius"]],["rounded-r",["border-top-right-radius","border-bottom-right-radius"]],["rounded-b",["border-bottom-right-radius","border-bottom-left-radius"]],["rounded-l",["border-top-left-radius","border-bottom-left-radius"]],["rounded-ss",["border-start-start-radius"]],["rounded-se",["border-start-end-radius"]],["rounded-ee",["border-end-end-radius"]],["rounded-es",["border-end-start-radius"]],["rounded-tl",["border-top-left-radius"]],["rounded-tr",["border-top-right-radius"]],["rounded-br",["border-bottom-right-radius"]],["rounded-bl",["border-bottom-left-radius"]]])e(`${o}-none`,f.map(d=>[d,"0"])),e(`${o}-full`,f.map(d=>[d,"calc(infinity * 1px)"])),n(o,{themeKeys:["--radius"],handle:d=>f.map(x=>l(x,d))});e("border-solid",[["--tw-border-style","solid"],["border-style","solid"]]),e("border-dashed",[["--tw-border-style","dashed"],["border-style","dashed"]]),e("border-dotted",[["--tw-border-style","dotted"],["border-style","dotted"]]),e("border-double",[["--tw-border-style","double"],["border-style","double"]]),e("border-hidden",[["--tw-border-style","hidden"],["border-style","hidden"]]),e("border-none",[["--tw-border-style","none"],["border-style","none"]]);{let f=function(d,x){r.functional(d,k=>{if(!k.value){if(k.modifier)return;let N=t.get(["--default-border-width"])??"1px",R=x.width(N);return R?[o(),...R]:void 0}if(k.value.kind==="arbitrary"){let N=k.value.value;switch(k.value.dataType??q(N,["color","line-width","length"])){case"line-width":case"length":{if(k.modifier)return;let $=x.width(N);return $?[o(),...$]:void 0}default:return N=Q(N,k.modifier,t),N===null?void 0:x.color(N)}}{let N=Z(k,t,["--border-color","--color"]);if(N)return x.color(N)}{if(k.modifier)return;let N=t.resolve(k.value.value,["--border-width"]);if(N){let R=x.width(N);return R?[o(),...R]:void 0}if(E(k.value.value)){let R=x.width(`${k.value.value}px`);return R?[o(),...R]:void 0}}}),i(d,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--border-color","--color"],modifiers:Array.from({length:21},(k,N)=>`${N*5}`),hasDefaultValue:!0},{values:["0","2","4","8"],valueThemeKeys:["--border-width"]}])};var Y=f;let o=()=>D([C("--tw-border-style","solid")]);f("border",{width:d=>[l("border-style","var(--tw-border-style)"),l("border-width",d)],color:d=>[l("border-color",d)]}),f("border-x",{width:d=>[l("border-inline-style","var(--tw-border-style)"),l("border-inline-width",d)],color:d=>[l("border-inline-color",d)]}),f("border-y",{width:d=>[l("border-block-style","var(--tw-border-style)"),l("border-block-width",d)],color:d=>[l("border-block-color",d)]}),f("border-s",{width:d=>[l("border-inline-start-style","var(--tw-border-style)"),l("border-inline-start-width",d)],color:d=>[l("border-inline-start-color",d)]}),f("border-e",{width:d=>[l("border-inline-end-style","var(--tw-border-style)"),l("border-inline-end-width",d)],color:d=>[l("border-inline-end-color",d)]}),f("border-t",{width:d=>[l("border-top-style","var(--tw-border-style)"),l("border-top-width",d)],color:d=>[l("border-top-color",d)]}),f("border-r",{width:d=>[l("border-right-style","var(--tw-border-style)"),l("border-right-width",d)],color:d=>[l("border-right-color",d)]}),f("border-b",{width:d=>[l("border-bottom-style","var(--tw-border-style)"),l("border-bottom-width",d)],color:d=>[l("border-bottom-color",d)]}),f("border-l",{width:d=>[l("border-left-style","var(--tw-border-style)"),l("border-left-width",d)],color:d=>[l("border-left-color",d)]}),n("divide-x",{defaultValue:t.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:d})=>E(d)?`${d}px`:null,handle:d=>[D([C("--tw-divide-x-reverse","0")]),L(":where(& > :not(:last-child))",[l("--tw-sort","divide-x-width"),o(),l("--tw-divide-x-reverse","0"),l("border-inline-style","var(--tw-border-style)"),l("border-inline-start-width",`calc(${d} * var(--tw-divide-x-reverse))`),l("border-inline-end-width",`calc(${d} * calc(1 - var(--tw-divide-x-reverse)))`)])]}),n("divide-y",{defaultValue:t.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:d})=>E(d)?`${d}px`:null,handle:d=>[D([C("--tw-divide-y-reverse","0")]),L(":where(& > :not(:last-child))",[l("--tw-sort","divide-y-width"),o(),l("--tw-divide-y-reverse","0"),l("border-bottom-style","var(--tw-border-style)"),l("border-top-style","var(--tw-border-style)"),l("border-top-width",`calc(${d} * var(--tw-divide-y-reverse))`),l("border-bottom-width",`calc(${d} * calc(1 - var(--tw-divide-y-reverse)))`)])]}),i("divide-x",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),i("divide-y",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),e("divide-x-reverse",[()=>D([C("--tw-divide-x-reverse","0")]),()=>L(":where(& > :not(:last-child))",[l("--tw-divide-x-reverse","1")])]),e("divide-y-reverse",[()=>D([C("--tw-divide-y-reverse","0")]),()=>L(":where(& > :not(:last-child))",[l("--tw-divide-y-reverse","1")])]);for(let d of["solid","dashed","dotted","double","none"])e(`divide-${d}`,[()=>L(":where(& > :not(:last-child))",[l("--tw-sort","divide-style"),l("--tw-border-style",d),l("border-style",d)])])}e("bg-auto",[["background-size","auto"]]),e("bg-cover",[["background-size","cover"]]),e("bg-contain",[["background-size","contain"]]),n("bg-size",{handle(o){if(o)return[l("background-size",o)]}}),e("bg-fixed",[["background-attachment","fixed"]]),e("bg-local",[["background-attachment","local"]]),e("bg-scroll",[["background-attachment","scroll"]]),e("bg-top",[["background-position","top"]]),e("bg-top-left",[["background-position","left top"]]),e("bg-top-right",[["background-position","right top"]]),e("bg-bottom",[["background-position","bottom"]]),e("bg-bottom-left",[["background-position","left bottom"]]),e("bg-bottom-right",[["background-position","right bottom"]]),e("bg-left",[["background-position","left"]]),e("bg-right",[["background-position","right"]]),e("bg-center",[["background-position","center"]]),n("bg-position",{handle(o){if(o)return[l("background-position",o)]}}),e("bg-repeat",[["background-repeat","repeat"]]),e("bg-no-repeat",[["background-repeat","no-repeat"]]),e("bg-repeat-x",[["background-repeat","repeat-x"]]),e("bg-repeat-y",[["background-repeat","repeat-y"]]),e("bg-repeat-round",[["background-repeat","round"]]),e("bg-repeat-space",[["background-repeat","space"]]),e("bg-none",[["background-image","none"]]);{let d=function(N){let R="in oklab";if(N?.kind==="named")switch(N.value){case"longer":case"shorter":case"increasing":case"decreasing":R=`in oklch ${N.value} hue`;break;default:R=`in ${N.value}`}else N?.kind==="arbitrary"&&(R=N.value);return R},x=function({negative:N}){return R=>{if(!R.value)return;if(R.value.kind==="arbitrary"){if(R.modifier)return;let U=R.value.value;switch(R.value.dataType??q(U,["angle"])){case"angle":return U=N?`calc(${U} * -1)`:`${U}`,[l("--tw-gradient-position",U),l("background-image",`linear-gradient(var(--tw-gradient-stops,${U}))`)];default:return N?void 0:[l("--tw-gradient-position",U),l("background-image",`linear-gradient(var(--tw-gradient-stops,${U}))`)]}}let $=R.value.value;if(!N&&f.has($))$=f.get($);else if(E($))$=N?`calc(${$}deg * -1)`:`${$}deg`;else return;let S=d(R.modifier);return[l("--tw-gradient-position",`${$}`),W("@supports (background-image: linear-gradient(in lab, red, red))",[l("--tw-gradient-position",`${$} ${S}`)]),l("background-image","linear-gradient(var(--tw-gradient-stops))")]}},k=function({negative:N}){return R=>{if(R.value?.kind==="arbitrary"){if(R.modifier)return;let U=R.value.value;return[l("--tw-gradient-position",U),l("background-image",`conic-gradient(var(--tw-gradient-stops,${U}))`)]}let $=d(R.modifier);if(!R.value)return[l("--tw-gradient-position",$),l("background-image","conic-gradient(var(--tw-gradient-stops))")];let S=R.value.value;if(E(S))return S=N?`calc(${S}deg * -1)`:`${S}deg`,[l("--tw-gradient-position",`from ${S} ${$}`),l("background-image","conic-gradient(var(--tw-gradient-stops))")]}};var M=d,F=x,re=k;let o=["oklab","oklch","srgb","hsl","longer","shorter","increasing","decreasing"],f=new Map([["to-t","to top"],["to-tr","to top right"],["to-r","to right"],["to-br","to bottom right"],["to-b","to bottom"],["to-bl","to bottom left"],["to-l","to left"],["to-tl","to top left"]]);r.functional("-bg-linear",x({negative:!0})),r.functional("bg-linear",x({negative:!1})),i("bg-linear",()=>[{values:[...f.keys()],modifiers:o},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:o}]),r.functional("-bg-conic",k({negative:!0})),r.functional("bg-conic",k({negative:!1})),i("bg-conic",()=>[{hasDefaultValue:!0,modifiers:o},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:o}]),r.functional("bg-radial",N=>{if(!N.value){let R=d(N.modifier);return[l("--tw-gradient-position",R),l("background-image","radial-gradient(var(--tw-gradient-stops))")]}if(N.value.kind==="arbitrary"){if(N.modifier)return;let R=N.value.value;return[l("--tw-gradient-position",R),l("background-image",`radial-gradient(var(--tw-gradient-stops,${R}))`)]}}),i("bg-radial",()=>[{hasDefaultValue:!0,modifiers:o}])}r.functional("bg",o=>{if(o.value){if(o.value.kind==="arbitrary"){let f=o.value.value;switch(o.value.dataType??q(f,["image","color","percentage","position","bg-size","length","url"])){case"percentage":case"position":return o.modifier?void 0:[l("background-position",f)];case"bg-size":case"length":case"size":return o.modifier?void 0:[l("background-size",f)];case"image":case"url":return o.modifier?void 0:[l("background-image",f)];default:return f=Q(f,o.modifier,t),f===null?void 0:[l("background-color",f)]}}{let f=Z(o,t,["--background-color","--color"]);if(f)return[l("background-color",f)]}{if(o.modifier)return;let f=t.resolve(o.value.value,["--background-image"]);if(f)return[l("background-image",f)]}}}),i("bg",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(o,f)=>`${f*5}`)},{values:[],valueThemeKeys:["--background-image"]}]);let v=()=>D([C("--tw-gradient-position"),C("--tw-gradient-from","#0000","<color>"),C("--tw-gradient-via","#0000","<color>"),C("--tw-gradient-to","#0000","<color>"),C("--tw-gradient-stops"),C("--tw-gradient-via-stops"),C("--tw-gradient-from-position","0%","<length-percentage>"),C("--tw-gradient-via-position","50%","<length-percentage>"),C("--tw-gradient-to-position","100%","<length-percentage>")]);function A(o,f){r.functional(o,d=>{if(d.value){if(d.value.kind==="arbitrary"){let x=d.value.value;switch(d.value.dataType??q(x,["color","length","percentage"])){case"length":case"percentage":return d.modifier?void 0:f.position(x);default:return x=Q(x,d.modifier,t),x===null?void 0:f.color(x)}}{let x=Z(d,t,["--background-color","--color"]);if(x)return f.color(x)}{if(d.modifier)return;let x=t.resolve(d.value.value,["--gradient-color-stop-positions"]);if(x)return f.position(x);if(d.value.value[d.value.value.length-1]==="%"&&E(d.value.value.slice(0,-1)))return f.position(d.value.value)}}}),i(o,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(d,x)=>`${x*5}`)},{values:Array.from({length:21},(d,x)=>`${x*5}%`),valueThemeKeys:["--gradient-color-stop-positions"]}])}A("from",{color:o=>[v(),l("--tw-sort","--tw-gradient-from"),l("--tw-gradient-from",o),l("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:o=>[v(),l("--tw-gradient-from-position",o)]}),e("via-none",[["--tw-gradient-via-stops","initial"]]),A("via",{color:o=>[v(),l("--tw-sort","--tw-gradient-via"),l("--tw-gradient-via",o),l("--tw-gradient-via-stops","var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position)"),l("--tw-gradient-stops","var(--tw-gradient-via-stops)")],position:o=>[v(),l("--tw-gradient-via-position",o)]}),A("to",{color:o=>[v(),l("--tw-sort","--tw-gradient-to"),l("--tw-gradient-to",o),l("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:o=>[v(),l("--tw-gradient-to-position",o)]}),e("mask-none",[["mask-image","none"]]),r.functional("mask",o=>{if(!o.value||o.modifier||o.value.kind!=="arbitrary")return;let f=o.value.value;switch(o.value.dataType??q(f,["image","percentage","position","bg-size","length","url"])){case"percentage":case"position":return o.modifier?void 0:[l("mask-position",f)];case"bg-size":case"length":case"size":return[l("mask-size",f)];case"image":case"url":default:return[l("mask-image",f)]}}),e("mask-add",[["mask-composite","add"]]),e("mask-subtract",[["mask-composite","subtract"]]),e("mask-intersect",[["mask-composite","intersect"]]),e("mask-exclude",[["mask-composite","exclude"]]),e("mask-alpha",[["mask-mode","alpha"]]),e("mask-luminance",[["mask-mode","luminance"]]),e("mask-match",[["mask-mode","match-source"]]),e("mask-type-alpha",[["mask-type","alpha"]]),e("mask-type-luminance",[["mask-type","luminance"]]),e("mask-auto",[["mask-size","auto"]]),e("mask-cover",[["mask-size","cover"]]),e("mask-contain",[["mask-size","contain"]]),n("mask-size",{handle(o){if(o)return[l("mask-size",o)]}}),e("mask-top",[["mask-position","top"]]),e("mask-top-left",[["mask-position","left top"]]),e("mask-top-right",[["mask-position","right top"]]),e("mask-bottom",[["mask-position","bottom"]]),e("mask-bottom-left",[["mask-position","left bottom"]]),e("mask-bottom-right",[["mask-position","right bottom"]]),e("mask-left",[["mask-position","left"]]),e("mask-right",[["mask-position","right"]]),e("mask-center",[["mask-position","center"]]),n("mask-position",{handle(o){if(o)return[l("mask-position",o)]}}),e("mask-repeat",[["mask-repeat","repeat"]]),e("mask-no-repeat",[["mask-repeat","no-repeat"]]),e("mask-repeat-x",[["mask-repeat","repeat-x"]]),e("mask-repeat-y",[["mask-repeat","repeat-y"]]),e("mask-repeat-round",[["mask-repeat","round"]]),e("mask-repeat-space",[["mask-repeat","space"]]),e("mask-clip-border",[["mask-clip","border-box"]]),e("mask-clip-padding",[["mask-clip","padding-box"]]),e("mask-clip-content",[["mask-clip","content-box"]]),e("mask-clip-fill",[["mask-clip","fill-box"]]),e("mask-clip-stroke",[["mask-clip","stroke-box"]]),e("mask-clip-view",[["mask-clip","view-box"]]),e("mask-no-clip",[["mask-clip","no-clip"]]),e("mask-origin-border",[["mask-origin","border-box"]]),e("mask-origin-padding",[["mask-origin","padding-box"]]),e("mask-origin-content",[["mask-origin","content-box"]]),e("mask-origin-fill",[["mask-origin","fill-box"]]),e("mask-origin-stroke",[["mask-origin","stroke-box"]]),e("mask-origin-view",[["mask-origin","view-box"]]);let b=()=>D([C("--tw-mask-linear","linear-gradient(#fff, #fff)"),C("--tw-mask-radial","linear-gradient(#fff, #fff)"),C("--tw-mask-conic","linear-gradient(#fff, #fff)")]);function y(o,f){r.functional(o,d=>{if(d.value){if(d.value.kind==="arbitrary"){let x=d.value.value;switch(d.value.dataType??q(x,["length","percentage","color"])){case"color":return x=Q(x,d.modifier,t),x===null?void 0:f.color(x);case"percentage":return d.modifier||!E(x.slice(0,-1))?void 0:f.position(x);default:return d.modifier?void 0:f.position(x)}}{let x=Z(d,t,["--background-color","--color"]);if(x)return f.color(x)}{if(d.modifier)return;let x=q(d.value.value,["number","percentage"]);if(!x)return;switch(x){case"number":{let k=t.resolve(null,["--spacing"]);return!k||!ye(d.value.value)?void 0:f.position(`calc(${k} * ${d.value.value})`)}case"percentage":return E(d.value.value.slice(0,-1))?f.position(d.value.value):void 0;default:return}}}}),i(o,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(d,x)=>`${x*5}`)},{values:Array.from({length:21},(d,x)=>`${x*5}%`),valueThemeKeys:["--gradient-color-stop-positions"]}]),i(o,()=>[{values:Array.from({length:21},(d,x)=>`${x*5}%`)},{values:t.get(["--spacing"])?nt:[]},{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(d,x)=>`${x*5}`)}])}let V=()=>D([C("--tw-mask-left","linear-gradient(#fff, #fff)"),C("--tw-mask-right","linear-gradient(#fff, #fff)"),C("--tw-mask-bottom","linear-gradient(#fff, #fff)"),C("--tw-mask-top","linear-gradient(#fff, #fff)")]);function T(o,f,d){y(o,{color(x){let k=[b(),V(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear","var(--tw-mask-left), var(--tw-mask-right), var(--tw-mask-bottom), var(--tw-mask-top)")];for(let N of["top","right","bottom","left"])d[N]&&(k.push(l(`--tw-mask-${N}`,`linear-gradient(to ${N}, var(--tw-mask-${N}-from-color) var(--tw-mask-${N}-from-position), var(--tw-mask-${N}-to-color) var(--tw-mask-${N}-to-position))`)),k.push(D([C(`--tw-mask-${N}-from-position`,"0%"),C(`--tw-mask-${N}-to-position`,"100%"),C(`--tw-mask-${N}-from-color`,"black"),C(`--tw-mask-${N}-to-color`,"transparent")])),k.push(l(`--tw-mask-${N}-${f}-color`,x)));return k},position(x){let k=[b(),V(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear","var(--tw-mask-left), var(--tw-mask-right), var(--tw-mask-bottom), var(--tw-mask-top)")];for(let N of["top","right","bottom","left"])d[N]&&(k.push(l(`--tw-mask-${N}`,`linear-gradient(to ${N}, var(--tw-mask-${N}-from-color) var(--tw-mask-${N}-from-position), var(--tw-mask-${N}-to-color) var(--tw-mask-${N}-to-position))`)),k.push(D([C(`--tw-mask-${N}-from-position`,"0%"),C(`--tw-mask-${N}-to-position`,"100%"),C(`--tw-mask-${N}-from-color`,"black"),C(`--tw-mask-${N}-to-color`,"transparent")])),k.push(l(`--tw-mask-${N}-${f}-position`,x)));return k}})}T("mask-x-from","from",{top:!1,right:!0,bottom:!1,left:!0}),T("mask-x-to","to",{top:!1,right:!0,bottom:!1,left:!0}),T("mask-y-from","from",{top:!0,right:!1,bottom:!0,left:!1}),T("mask-y-to","to",{top:!0,right:!1,bottom:!0,left:!1}),T("mask-t-from","from",{top:!0,right:!1,bottom:!1,left:!1}),T("mask-t-to","to",{top:!0,right:!1,bottom:!1,left:!1}),T("mask-r-from","from",{top:!1,right:!0,bottom:!1,left:!1}),T("mask-r-to","to",{top:!1,right:!0,bottom:!1,left:!1}),T("mask-b-from","from",{top:!1,right:!1,bottom:!0,left:!1}),T("mask-b-to","to",{top:!1,right:!1,bottom:!0,left:!1}),T("mask-l-from","from",{top:!1,right:!1,bottom:!1,left:!0}),T("mask-l-to","to",{top:!1,right:!1,bottom:!1,left:!0});let O=()=>D([C("--tw-mask-linear-position","0deg"),C("--tw-mask-linear-from-position","0%"),C("--tw-mask-linear-to-position","100%"),C("--tw-mask-linear-from-color","black"),C("--tw-mask-linear-to-color","transparent")]);n("mask-linear",{defaultValue:null,supportsNegative:!0,supportsFractions:!1,handleBareValue(o){return E(o.value)?`calc(1deg * ${o.value})`:null},handleNegativeBareValue(o){return E(o.value)?`calc(1deg * -${o.value})`:null},handle:o=>[b(),O(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops, var(--tw-mask-linear-position)))"),l("--tw-mask-linear-position",o)]}),i("mask-linear",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"]}]),y("mask-linear-from",{color:o=>[b(),O(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),l("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),l("--tw-mask-linear-from-color",o)],position:o=>[b(),O(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),l("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),l("--tw-mask-linear-from-position",o)]}),y("mask-linear-to",{color:o=>[b(),O(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),l("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),l("--tw-mask-linear-to-color",o)],position:o=>[b(),O(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),l("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),l("--tw-mask-linear-to-position",o)]});let _=()=>D([C("--tw-mask-radial-from-position","0%"),C("--tw-mask-radial-to-position","100%"),C("--tw-mask-radial-from-color","black"),C("--tw-mask-radial-to-color","transparent"),C("--tw-mask-radial-shape","ellipse"),C("--tw-mask-radial-size","farthest-corner"),C("--tw-mask-radial-position","center")]);e("mask-circle",[["--tw-mask-radial-shape","circle"]]),e("mask-ellipse",[["--tw-mask-radial-shape","ellipse"]]),e("mask-radial-closest-side",[["--tw-mask-radial-size","closest-side"]]),e("mask-radial-farthest-side",[["--tw-mask-radial-size","farthest-side"]]),e("mask-radial-closest-corner",[["--tw-mask-radial-size","closest-corner"]]),e("mask-radial-farthest-corner",[["--tw-mask-radial-size","farthest-corner"]]),e("mask-radial-at-top",[["--tw-mask-radial-position","top"]]),e("mask-radial-at-top-left",[["--tw-mask-radial-position","top left"]]),e("mask-radial-at-top-right",[["--tw-mask-radial-position","top right"]]),e("mask-radial-at-bottom",[["--tw-mask-radial-position","bottom"]]),e("mask-radial-at-bottom-left",[["--tw-mask-radial-position","bottom left"]]),e("mask-radial-at-bottom-right",[["--tw-mask-radial-position","bottom right"]]),e("mask-radial-at-left",[["--tw-mask-radial-position","left"]]),e("mask-radial-at-right",[["--tw-mask-radial-position","right"]]),e("mask-radial-at-center",[["--tw-mask-radial-position","center"]]),n("mask-radial-at",{defaultValue:null,supportsNegative:!1,supportsFractions:!1,handle:o=>[l("--tw-mask-radial-position",o)]}),n("mask-radial",{defaultValue:null,supportsNegative:!1,supportsFractions:!1,handle:o=>[b(),_(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops, var(--tw-mask-radial-size)))"),l("--tw-mask-radial-size",o)]}),y("mask-radial-from",{color:o=>[b(),_(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),l("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),l("--tw-mask-radial-from-color",o)],position:o=>[b(),_(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),l("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),l("--tw-mask-radial-from-position",o)]}),y("mask-radial-to",{color:o=>[b(),_(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),l("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),l("--tw-mask-radial-to-color",o)],position:o=>[b(),_(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),l("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),l("--tw-mask-radial-to-position",o)]});let j=()=>D([C("--tw-mask-conic-position","0deg"),C("--tw-mask-conic-from-position","0%"),C("--tw-mask-conic-to-position","100%"),C("--tw-mask-conic-from-color","black"),C("--tw-mask-conic-to-color","transparent")]);n("mask-conic",{defaultValue:null,supportsNegative:!0,supportsFractions:!1,handleBareValue(o){return E(o.value)?`calc(1deg * ${o.value})`:null},handleNegativeBareValue(o){return E(o.value)?`calc(1deg * -${o.value})`:null},handle:o=>[b(),j(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops, var(--tw-mask-conic-position)))"),l("--tw-mask-conic-position",o)]}),i("mask-conic",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"]}]),y("mask-conic-from",{color:o=>[b(),j(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),l("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),l("--tw-mask-conic-from-color",o)],position:o=>[b(),j(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),l("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),l("--tw-mask-conic-from-position",o)]}),y("mask-conic-to",{color:o=>[b(),j(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),l("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),l("--tw-mask-conic-to-color",o)],position:o=>[b(),j(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),l("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),l("--tw-mask-conic-to-position",o)]}),e("box-decoration-slice",[["-webkit-box-decoration-break","slice"],["box-decoration-break","slice"]]),e("box-decoration-clone",[["-webkit-box-decoration-break","clone"],["box-decoration-break","clone"]]),e("bg-clip-text",[["background-clip","text"]]),e("bg-clip-border",[["background-clip","border-box"]]),e("bg-clip-padding",[["background-clip","padding-box"]]),e("bg-clip-content",[["background-clip","content-box"]]),e("bg-origin-border",[["background-origin","border-box"]]),e("bg-origin-padding",[["background-origin","padding-box"]]),e("bg-origin-content",[["background-origin","content-box"]]);for(let o of["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"])e(`bg-blend-${o}`,[["background-blend-mode",o]]),e(`mix-blend-${o}`,[["mix-blend-mode",o]]);e("mix-blend-plus-darker",[["mix-blend-mode","plus-darker"]]),e("mix-blend-plus-lighter",[["mix-blend-mode","plus-lighter"]]),e("fill-none",[["fill","none"]]),r.functional("fill",o=>{if(!o.value)return;if(o.value.kind==="arbitrary"){let d=Q(o.value.value,o.modifier,t);return d===null?void 0:[l("fill",d)]}let f=Z(o,t,["--fill","--color"]);if(f)return[l("fill",f)]}),i("fill",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--fill","--color"],modifiers:Array.from({length:21},(o,f)=>`${f*5}`)}]),e("stroke-none",[["stroke","none"]]),r.functional("stroke",o=>{if(o.value){if(o.value.kind==="arbitrary"){let f=o.value.value;switch(o.value.dataType??q(f,["color","number","length","percentage"])){case"number":case"length":case"percentage":return o.modifier?void 0:[l("stroke-width",f)];default:return f=Q(o.value.value,o.modifier,t),f===null?void 0:[l("stroke",f)]}}{let f=Z(o,t,["--stroke","--color"]);if(f)return[l("stroke",f)]}{let f=t.resolve(o.value.value,["--stroke-width"]);if(f)return[l("stroke-width",f)];if(E(o.value.value))return[l("stroke-width",o.value.value)]}}}),i("stroke",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--stroke","--color"],modifiers:Array.from({length:21},(o,f)=>`${f*5}`)},{values:["0","1","2","3"],valueThemeKeys:["--stroke-width"]}]),e("object-contain",[["object-fit","contain"]]),e("object-cover",[["object-fit","cover"]]),e("object-fill",[["object-fit","fill"]]),e("object-none",[["object-fit","none"]]),e("object-scale-down",[["object-fit","scale-down"]]),e("object-top",[["object-position","top"]]),e("object-top-left",[["object-position","left top"]]),e("object-top-right",[["object-position","right top"]]),e("object-bottom",[["object-position","bottom"]]),e("object-bottom-left",[["object-position","left bottom"]]),e("object-bottom-right",[["object-position","right bottom"]]),e("object-left",[["object-position","left"]]),e("object-right",[["object-position","right"]]),e("object-center",[["object-position","center"]]),n("object",{themeKeys:["--object-position"],handle:o=>[l("object-position",o)]});for(let[o,f]of[["p","padding"],["px","padding-inline"],["py","padding-block"],["ps","padding-inline-start"],["pe","padding-inline-end"],["pt","padding-top"],["pr","padding-right"],["pb","padding-bottom"],["pl","padding-left"]])a(o,["--padding","--spacing"],d=>[l(f,d)]);e("text-left",[["text-align","left"]]),e("text-center",[["text-align","center"]]),e("text-right",[["text-align","right"]]),e("text-justify",[["text-align","justify"]]),e("text-start",[["text-align","start"]]),e("text-end",[["text-align","end"]]),a("indent",["--text-indent","--spacing"],o=>[l("text-indent",o)],{supportsNegative:!0}),e("align-baseline",[["vertical-align","baseline"]]),e("align-top",[["vertical-align","top"]]),e("align-middle",[["vertical-align","middle"]]),e("align-bottom",[["vertical-align","bottom"]]),e("align-text-top",[["vertical-align","text-top"]]),e("align-text-bottom",[["vertical-align","text-bottom"]]),e("align-sub",[["vertical-align","sub"]]),e("align-super",[["vertical-align","super"]]),n("align",{themeKeys:[],handle:o=>[l("vertical-align",o)]}),r.functional("font",o=>{if(!(!o.value||o.modifier)){if(o.value.kind==="arbitrary"){let f=o.value.value;switch(o.value.dataType??q(f,["number","generic-name","family-name"])){case"generic-name":case"family-name":return[l("font-family",f)];default:return[D([C("--tw-font-weight")]),l("--tw-font-weight",f),l("font-weight",f)]}}{let f=t.resolveWith(o.value.value,["--font"],["--font-feature-settings","--font-variation-settings"]);if(f){let[d,x={}]=f;return[l("font-family",d),l("font-feature-settings",x["--font-feature-settings"]),l("font-variation-settings",x["--font-variation-settings"])]}}{let f=t.resolve(o.value.value,["--font-weight"]);if(f)return[D([C("--tw-font-weight")]),l("--tw-font-weight",f),l("font-weight",f)]}}}),i("font",()=>[{values:[],valueThemeKeys:["--font"]},{values:[],valueThemeKeys:["--font-weight"]}]),e("uppercase",[["text-transform","uppercase"]]),e("lowercase",[["text-transform","lowercase"]]),e("capitalize",[["text-transform","capitalize"]]),e("normal-case",[["text-transform","none"]]),e("italic",[["font-style","italic"]]),e("not-italic",[["font-style","normal"]]),e("underline",[["text-decoration-line","underline"]]),e("overline",[["text-decoration-line","overline"]]),e("line-through",[["text-decoration-line","line-through"]]),e("no-underline",[["text-decoration-line","none"]]),e("font-stretch-normal",[["font-stretch","normal"]]),e("font-stretch-ultra-condensed",[["font-stretch","ultra-condensed"]]),e("font-stretch-extra-condensed",[["font-stretch","extra-condensed"]]),e("font-stretch-condensed",[["font-stretch","condensed"]]),e("font-stretch-semi-condensed",[["font-stretch","semi-condensed"]]),e("font-stretch-semi-expanded",[["font-stretch","semi-expanded"]]),e("font-stretch-expanded",[["font-stretch","expanded"]]),e("font-stretch-extra-expanded",[["font-stretch","extra-expanded"]]),e("font-stretch-ultra-expanded",[["font-stretch","ultra-expanded"]]),n("font-stretch",{handleBareValue:({value:o})=>{if(!o.endsWith("%"))return null;let f=Number(o.slice(0,-1));return!E(f)||Number.isNaN(f)||f<50||f>200?null:o},handle:o=>[l("font-stretch",o)]}),i("font-stretch",()=>[{values:["50%","75%","90%","95%","100%","105%","110%","125%","150%","200%"]}]),s("placeholder",{themeKeys:["--background-color","--color"],handle:o=>[L("&::placeholder",[l("--tw-sort","placeholder-color"),l("color",o)])]}),e("decoration-solid",[["text-decoration-style","solid"]]),e("decoration-double",[["text-decoration-style","double"]]),e("decoration-dotted",[["text-decoration-style","dotted"]]),e("decoration-dashed",[["text-decoration-style","dashed"]]),e("decoration-wavy",[["text-decoration-style","wavy"]]),e("decoration-auto",[["text-decoration-thickness","auto"]]),e("decoration-from-font",[["text-decoration-thickness","from-font"]]),r.functional("decoration",o=>{if(o.value){if(o.value.kind==="arbitrary"){let f=o.value.value;switch(o.value.dataType??q(f,["color","length","percentage"])){case"length":case"percentage":return o.modifier?void 0:[l("text-decoration-thickness",f)];default:return f=Q(f,o.modifier,t),f===null?void 0:[l("text-decoration-color",f)]}}{let f=t.resolve(o.value.value,["--text-decoration-thickness"]);if(f)return o.modifier?void 0:[l("text-decoration-thickness",f)];if(E(o.value.value))return o.modifier?void 0:[l("text-decoration-thickness",`${o.value.value}px`)]}{let f=Z(o,t,["--text-decoration-color","--color"]);if(f)return[l("text-decoration-color",f)]}}}),i("decoration",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-decoration-color","--color"],modifiers:Array.from({length:21},(o,f)=>`${f*5}`)},{values:["0","1","2"],valueThemeKeys:["--text-decoration-thickness"]}]),e("animate-none",[["animation","none"]]),n("animate",{themeKeys:["--animate"],handle:o=>[l("animation",o)]});{let o=["var(--tw-blur,)","var(--tw-brightness,)","var(--tw-contrast,)","var(--tw-grayscale,)","var(--tw-hue-rotate,)","var(--tw-invert,)","var(--tw-saturate,)","var(--tw-sepia,)","var(--tw-drop-shadow,)"].join(" "),f=["var(--tw-backdrop-blur,)","var(--tw-backdrop-brightness,)","var(--tw-backdrop-contrast,)","var(--tw-backdrop-grayscale,)","var(--tw-backdrop-hue-rotate,)","var(--tw-backdrop-invert,)","var(--tw-backdrop-opacity,)","var(--tw-backdrop-saturate,)","var(--tw-backdrop-sepia,)"].join(" "),d=()=>D([C("--tw-blur"),C("--tw-brightness"),C("--tw-contrast"),C("--tw-grayscale"),C("--tw-hue-rotate"),C("--tw-invert"),C("--tw-opacity"),C("--tw-saturate"),C("--tw-sepia"),C("--tw-drop-shadow"),C("--tw-drop-shadow-color"),C("--tw-drop-shadow-alpha","100%","<percentage>"),C("--tw-drop-shadow-size")]),x=()=>D([C("--tw-backdrop-blur"),C("--tw-backdrop-brightness"),C("--tw-backdrop-contrast"),C("--tw-backdrop-grayscale"),C("--tw-backdrop-hue-rotate"),C("--tw-backdrop-invert"),C("--tw-backdrop-opacity"),C("--tw-backdrop-saturate"),C("--tw-backdrop-sepia")]);r.functional("filter",k=>{if(!k.modifier){if(k.value===null)return[d(),l("filter",o)];if(k.value.kind==="arbitrary")return[l("filter",k.value.value)];switch(k.value.value){case"none":return[l("filter","none")]}}}),r.functional("backdrop-filter",k=>{if(!k.modifier){if(k.value===null)return[x(),l("-webkit-backdrop-filter",f),l("backdrop-filter",f)];if(k.value.kind==="arbitrary")return[l("-webkit-backdrop-filter",k.value.value),l("backdrop-filter",k.value.value)];switch(k.value.value){case"none":return[l("-webkit-backdrop-filter","none"),l("backdrop-filter","none")]}}}),n("blur",{themeKeys:["--blur"],handle:k=>[d(),l("--tw-blur",`blur(${k})`),l("filter",o)]}),e("blur-none",[d,["--tw-blur"," "],["filter",o]]),n("backdrop-blur",{themeKeys:["--backdrop-blur","--blur"],handle:k=>[x(),l("--tw-backdrop-blur",`blur(${k})`),l("-webkit-backdrop-filter",f),l("backdrop-filter",f)]}),e("backdrop-blur-none",[x,["--tw-backdrop-blur"," "],["-webkit-backdrop-filter",f],["backdrop-filter",f]]),n("brightness",{themeKeys:["--brightness"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,handle:k=>[d(),l("--tw-brightness",`brightness(${k})`),l("filter",o)]}),n("backdrop-brightness",{themeKeys:["--backdrop-brightness","--brightness"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,handle:k=>[x(),l("--tw-backdrop-brightness",`brightness(${k})`),l("-webkit-backdrop-filter",f),l("backdrop-filter",f)]}),i("brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--brightness"]}]),i("backdrop-brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--backdrop-brightness","--brightness"]}]),n("contrast",{themeKeys:["--contrast"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,handle:k=>[d(),l("--tw-contrast",`contrast(${k})`),l("filter",o)]}),n("backdrop-contrast",{themeKeys:["--backdrop-contrast","--contrast"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,handle:k=>[x(),l("--tw-backdrop-contrast",`contrast(${k})`),l("-webkit-backdrop-filter",f),l("backdrop-filter",f)]}),i("contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--contrast"]}]),i("backdrop-contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--backdrop-contrast","--contrast"]}]),n("grayscale",{themeKeys:["--grayscale"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[d(),l("--tw-grayscale",`grayscale(${k})`),l("filter",o)]}),n("backdrop-grayscale",{themeKeys:["--backdrop-grayscale","--grayscale"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[x(),l("--tw-backdrop-grayscale",`grayscale(${k})`),l("-webkit-backdrop-filter",f),l("backdrop-filter",f)]}),i("grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--grayscale"],hasDefaultValue:!0}]),i("backdrop-grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-grayscale","--grayscale"],hasDefaultValue:!0}]),n("hue-rotate",{supportsNegative:!0,themeKeys:["--hue-rotate"],handleBareValue:({value:k})=>E(k)?`${k}deg`:null,handle:k=>[d(),l("--tw-hue-rotate",`hue-rotate(${k})`),l("filter",o)]}),n("backdrop-hue-rotate",{supportsNegative:!0,themeKeys:["--backdrop-hue-rotate","--hue-rotate"],handleBareValue:({value:k})=>E(k)?`${k}deg`:null,handle:k=>[x(),l("--tw-backdrop-hue-rotate",`hue-rotate(${k})`),l("-webkit-backdrop-filter",f),l("backdrop-filter",f)]}),i("hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--hue-rotate"]}]),i("backdrop-hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--backdrop-hue-rotate","--hue-rotate"]}]),n("invert",{themeKeys:["--invert"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[d(),l("--tw-invert",`invert(${k})`),l("filter",o)]}),n("backdrop-invert",{themeKeys:["--backdrop-invert","--invert"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[x(),l("--tw-backdrop-invert",`invert(${k})`),l("-webkit-backdrop-filter",f),l("backdrop-filter",f)]}),i("invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--invert"],hasDefaultValue:!0}]),i("backdrop-invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-invert","--invert"],hasDefaultValue:!0}]),n("saturate",{themeKeys:["--saturate"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,handle:k=>[d(),l("--tw-saturate",`saturate(${k})`),l("filter",o)]}),n("backdrop-saturate",{themeKeys:["--backdrop-saturate","--saturate"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,handle:k=>[x(),l("--tw-backdrop-saturate",`saturate(${k})`),l("-webkit-backdrop-filter",f),l("backdrop-filter",f)]}),i("saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--saturate"]}]),i("backdrop-saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--backdrop-saturate","--saturate"]}]),n("sepia",{themeKeys:["--sepia"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[d(),l("--tw-sepia",`sepia(${k})`),l("filter",o)]}),n("backdrop-sepia",{themeKeys:["--backdrop-sepia","--sepia"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[x(),l("--tw-backdrop-sepia",`sepia(${k})`),l("-webkit-backdrop-filter",f),l("backdrop-filter",f)]}),i("sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--sepia"],hasDefaultValue:!0}]),i("backdrop-sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--backdrop-sepia","--sepia"],hasDefaultValue:!0}]),e("drop-shadow-none",[d,["--tw-drop-shadow"," "],["filter",o]]),r.functional("drop-shadow",k=>{let N;if(k.modifier&&(k.modifier.kind==="arbitrary"?N=k.modifier.value:E(k.modifier.value)&&(N=`${k.modifier.value}%`)),!k.value){let R=t.get(["--drop-shadow"]),$=t.resolve(null,["--drop-shadow"]);return R===null||$===null?void 0:[d(),l("--tw-drop-shadow-alpha",N),...it("--tw-drop-shadow-size",R,N,S=>`var(--tw-drop-shadow-color, ${S})`),l("--tw-drop-shadow",K($,",").map(S=>`drop-shadow(${S})`).join(" ")),l("filter",o)]}if(k.value.kind==="arbitrary"){let R=k.value.value;switch(k.value.dataType??q(R,["color"])){case"color":return R=Q(R,k.modifier,t),R===null?void 0:[d(),l("--tw-drop-shadow-color",G(R,"var(--tw-drop-shadow-alpha)")),l("--tw-drop-shadow","var(--tw-drop-shadow-size)")];default:return k.modifier&&!N?void 0:[d(),l("--tw-drop-shadow-alpha",N),...it("--tw-drop-shadow-size",R,N,S=>`var(--tw-drop-shadow-color, ${S})`),l("--tw-drop-shadow","var(--tw-drop-shadow-size)"),l("filter",o)]}}{let R=t.get([`--drop-shadow-${k.value.value}`]),$=t.resolve(k.value.value,["--drop-shadow"]);if(R&&$)return k.modifier&&!N?void 0:N?[d(),l("--tw-drop-shadow-alpha",N),...it("--tw-drop-shadow-size",R,N,S=>`var(--tw-drop-shadow-color, ${S})`),l("--tw-drop-shadow","var(--tw-drop-shadow-size)"),l("filter",o)]:[d(),l("--tw-drop-shadow-alpha",N),...it("--tw-drop-shadow-size",R,N,S=>`var(--tw-drop-shadow-color, ${S})`),l("--tw-drop-shadow",K($,",").map(S=>`drop-shadow(${S})`).join(" ")),l("filter",o)]}{let R=Z(k,t,["--drop-shadow-color","--color"]);if(R)return R==="inherit"?[d(),l("--tw-drop-shadow-color","inherit"),l("--tw-drop-shadow","var(--tw-drop-shadow-size)")]:[d(),l("--tw-drop-shadow-color",G(R,"var(--tw-drop-shadow-alpha)")),l("--tw-drop-shadow","var(--tw-drop-shadow-size)")]}}),i("drop-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--drop-shadow-color","--color"],modifiers:Array.from({length:21},(k,N)=>`${N*5}`)},{valueThemeKeys:["--drop-shadow"]}]),n("backdrop-opacity",{themeKeys:["--backdrop-opacity","--opacity"],handleBareValue:({value:k})=>rt(k)?`${k}%`:null,handle:k=>[x(),l("--tw-backdrop-opacity",`opacity(${k})`),l("-webkit-backdrop-filter",f),l("backdrop-filter",f)]}),i("backdrop-opacity",()=>[{values:Array.from({length:21},(k,N)=>`${N*5}`),valueThemeKeys:["--backdrop-opacity","--opacity"]}])}{let o=`var(--tw-ease, ${t.resolve(null,["--default-transition-timing-function"])??"ease"})`,f=`var(--tw-duration, ${t.resolve(null,["--default-transition-duration"])??"0s"})`;e("transition-none",[["transition-property","none"]]),e("transition-all",[["transition-property","all"],["transition-timing-function",o],["transition-duration",f]]),e("transition-colors",[["transition-property","color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to"],["transition-timing-function",o],["transition-duration",f]]),e("transition-opacity",[["transition-property","opacity"],["transition-timing-function",o],["transition-duration",f]]),e("transition-shadow",[["transition-property","box-shadow"],["transition-timing-function",o],["transition-duration",f]]),e("transition-transform",[["transition-property","transform, translate, scale, rotate"],["transition-timing-function",o],["transition-duration",f]]),n("transition",{defaultValue:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter",themeKeys:["--transition-property"],handle:d=>[l("transition-property",d),l("transition-timing-function",o),l("transition-duration",f)]}),e("transition-discrete",[["transition-behavior","allow-discrete"]]),e("transition-normal",[["transition-behavior","normal"]]),n("delay",{handleBareValue:({value:d})=>E(d)?`${d}ms`:null,themeKeys:["--transition-delay"],handle:d=>[l("transition-delay",d)]});{let d=()=>D([C("--tw-duration")]);e("duration-initial",[d,["--tw-duration","initial"]]),r.functional("duration",x=>{if(x.modifier||!x.value)return;let k=null;if(x.value.kind==="arbitrary"?k=x.value.value:(k=t.resolve(x.value.fraction??x.value.value,["--transition-duration"]),k===null&&E(x.value.value)&&(k=`${x.value.value}ms`)),k!==null)return[d(),l("--tw-duration",k),l("transition-duration",k)]})}i("delay",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-delay"]}]),i("duration",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-duration"]}])}{let o=()=>D([C("--tw-ease")]);e("ease-initial",[o,["--tw-ease","initial"]]),e("ease-linear",[o,["--tw-ease","linear"],["transition-timing-function","linear"]]),n("ease",{themeKeys:["--ease"],handle:f=>[o(),l("--tw-ease",f),l("transition-timing-function",f)]})}e("will-change-auto",[["will-change","auto"]]),e("will-change-scroll",[["will-change","scroll-position"]]),e("will-change-contents",[["will-change","contents"]]),e("will-change-transform",[["will-change","transform"]]),n("will-change",{themeKeys:[],handle:o=>[l("will-change",o)]}),e("content-none",[["--tw-content","none"],["content","none"]]),n("content",{themeKeys:[],handle:o=>[D([C("--tw-content",'""')]),l("--tw-content",o),l("content","var(--tw-content)")]});{let o="var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,)",f=()=>D([C("--tw-contain-size"),C("--tw-contain-layout"),C("--tw-contain-paint"),C("--tw-contain-style")]);e("contain-none",[["contain","none"]]),e("contain-content",[["contain","content"]]),e("contain-strict",[["contain","strict"]]),e("contain-size",[f,["--tw-contain-size","size"],["contain",o]]),e("contain-inline-size",[f,["--tw-contain-size","inline-size"],["contain",o]]),e("contain-layout",[f,["--tw-contain-layout","layout"],["contain",o]]),e("contain-paint",[f,["--tw-contain-paint","paint"],["contain",o]]),e("contain-style",[f,["--tw-contain-style","style"],["contain",o]]),n("contain",{themeKeys:[],handle:d=>[l("contain",d)]})}e("forced-color-adjust-none",[["forced-color-adjust","none"]]),e("forced-color-adjust-auto",[["forced-color-adjust","auto"]]),e("leading-none",[()=>D([C("--tw-leading")]),["--tw-leading","1"],["line-height","1"]]),a("leading",["--leading","--spacing"],o=>[D([C("--tw-leading")]),l("--tw-leading",o),l("line-height",o)]),n("tracking",{supportsNegative:!0,themeKeys:["--tracking"],handle:o=>[D([C("--tw-tracking")]),l("--tw-tracking",o),l("letter-spacing",o)]}),e("antialiased",[["-webkit-font-smoothing","antialiased"],["-moz-osx-font-smoothing","grayscale"]]),e("subpixel-antialiased",[["-webkit-font-smoothing","auto"],["-moz-osx-font-smoothing","auto"]]);{let o="var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,)",f=()=>D([C("--tw-ordinal"),C("--tw-slashed-zero"),C("--tw-numeric-figure"),C("--tw-numeric-spacing"),C("--tw-numeric-fraction")]);e("normal-nums",[["font-variant-numeric","normal"]]),e("ordinal",[f,["--tw-ordinal","ordinal"],["font-variant-numeric",o]]),e("slashed-zero",[f,["--tw-slashed-zero","slashed-zero"],["font-variant-numeric",o]]),e("lining-nums",[f,["--tw-numeric-figure","lining-nums"],["font-variant-numeric",o]]),e("oldstyle-nums",[f,["--tw-numeric-figure","oldstyle-nums"],["font-variant-numeric",o]]),e("proportional-nums",[f,["--tw-numeric-spacing","proportional-nums"],["font-variant-numeric",o]]),e("tabular-nums",[f,["--tw-numeric-spacing","tabular-nums"],["font-variant-numeric",o]]),e("diagonal-fractions",[f,["--tw-numeric-fraction","diagonal-fractions"],["font-variant-numeric",o]]),e("stacked-fractions",[f,["--tw-numeric-fraction","stacked-fractions"],["font-variant-numeric",o]])}{let o=()=>D([C("--tw-outline-style","solid")]);r.static("outline-hidden",()=>[l("--tw-outline-style","none"),l("outline-style","none"),I("@media","(forced-colors: active)",[l("outline","2px solid transparent"),l("outline-offset","2px")])]),e("outline-none",[["--tw-outline-style","none"],["outline-style","none"]]),e("outline-solid",[["--tw-outline-style","solid"],["outline-style","solid"]]),e("outline-dashed",[["--tw-outline-style","dashed"],["outline-style","dashed"]]),e("outline-dotted",[["--tw-outline-style","dotted"],["outline-style","dotted"]]),e("outline-double",[["--tw-outline-style","double"],["outline-style","double"]]),r.functional("outline",f=>{if(f.value===null){if(f.modifier)return;let d=t.get(["--default-outline-width"])??"1px";return[o(),l("outline-style","var(--tw-outline-style)"),l("outline-width",d)]}if(f.value.kind==="arbitrary"){let d=f.value.value;switch(f.value.dataType??q(d,["color","length","number","percentage"])){case"length":case"number":case"percentage":return f.modifier?void 0:[o(),l("outline-style","var(--tw-outline-style)"),l("outline-width",d)];default:return d=Q(d,f.modifier,t),d===null?void 0:[l("outline-color",d)]}}{let d=Z(f,t,["--outline-color","--color"]);if(d)return[l("outline-color",d)]}{if(f.modifier)return;let d=t.resolve(f.value.value,["--outline-width"]);if(d)return[o(),l("outline-style","var(--tw-outline-style)"),l("outline-width",d)];if(E(f.value.value))return[o(),l("outline-style","var(--tw-outline-style)"),l("outline-width",`${f.value.value}px`)]}}),i("outline",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--outline-color","--color"],modifiers:Array.from({length:21},(f,d)=>`${d*5}`),hasDefaultValue:!0},{values:["0","1","2","4","8"],valueThemeKeys:["--outline-width"]}]),n("outline-offset",{supportsNegative:!0,themeKeys:["--outline-offset"],handleBareValue:({value:f})=>E(f)?`${f}px`:null,handle:f=>[l("outline-offset",f)]}),i("outline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--outline-offset"]}])}n("opacity",{themeKeys:["--opacity"],handleBareValue:({value:o})=>rt(o)?`${o}%`:null,handle:o=>[l("opacity",o)]}),i("opacity",()=>[{values:Array.from({length:21},(o,f)=>`${f*5}`),valueThemeKeys:["--opacity"]}]),e("underline-offset-auto",[["text-underline-offset","auto"]]),n("underline-offset",{supportsNegative:!0,themeKeys:["--text-underline-offset"],handleBareValue:({value:o})=>E(o)?`${o}px`:null,handle:o=>[l("text-underline-offset",o)]}),i("underline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--text-underline-offset"]}]),r.functional("text",o=>{if(o.value){if(o.value.kind==="arbitrary"){let f=o.value.value;switch(o.value.dataType??q(f,["color","length","percentage","absolute-size","relative-size"])){case"size":case"length":case"percentage":case"absolute-size":case"relative-size":{if(o.modifier){let x=o.modifier.kind==="arbitrary"?o.modifier.value:t.resolve(o.modifier.value,["--leading"]);if(!x&&ye(o.modifier.value)){let k=t.resolve(null,["--spacing"]);if(!k)return null;x=`calc(${k} * ${o.modifier.value})`}return!x&&o.modifier.value==="none"&&(x="1"),x?[l("font-size",f),l("line-height",x)]:null}return[l("font-size",f)]}default:return f=Q(f,o.modifier,t),f===null?void 0:[l("color",f)]}}{let f=Z(o,t,["--text-color","--color"]);if(f)return[l("color",f)]}{let f=t.resolveWith(o.value.value,["--text"],["--line-height","--letter-spacing","--font-weight"]);if(f){let[d,x={}]=Array.isArray(f)?f:[f];if(o.modifier){let k=o.modifier.kind==="arbitrary"?o.modifier.value:t.resolve(o.modifier.value,["--leading"]);if(!k&&ye(o.modifier.value)){let R=t.resolve(null,["--spacing"]);if(!R)return null;k=`calc(${R} * ${o.modifier.value})`}if(!k&&o.modifier.value==="none"&&(k="1"),!k)return null;let N=[l("font-size",d)];return k&&N.push(l("line-height",k)),N}return typeof x=="string"?[l("font-size",d),l("line-height",x)]:[l("font-size",d),l("line-height",x["--line-height"]?`var(--tw-leading, ${x["--line-height"]})`:void 0),l("letter-spacing",x["--letter-spacing"]?`var(--tw-tracking, ${x["--letter-spacing"]})`:void 0),l("font-weight",x["--font-weight"]?`var(--tw-font-weight, ${x["--font-weight"]})`:void 0)]}}}}),i("text",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-color","--color"],modifiers:Array.from({length:21},(o,f)=>`${f*5}`)},{values:[],valueThemeKeys:["--text"],modifiers:[],modifierThemeKeys:["--leading"]}]);let P=()=>D([C("--tw-text-shadow-color"),C("--tw-text-shadow-alpha","100%","<percentage>")]);e("text-shadow-initial",[P,["--tw-text-shadow-color","initial"]]),r.functional("text-shadow",o=>{let f;if(o.modifier&&(o.modifier.kind==="arbitrary"?f=o.modifier.value:E(o.modifier.value)&&(f=`${o.modifier.value}%`)),!o.value){let d=t.get(["--text-shadow"]);return d===null?void 0:[P(),l("--tw-text-shadow-alpha",f),...ce("text-shadow",d,f,x=>`var(--tw-text-shadow-color, ${x})`)]}if(o.value.kind==="arbitrary"){let d=o.value.value;switch(o.value.dataType??q(d,["color"])){case"color":return d=Q(d,o.modifier,t),d===null?void 0:[P(),l("--tw-text-shadow-color",G(d,"var(--tw-text-shadow-alpha)"))];default:return[P(),l("--tw-text-shadow-alpha",f),...ce("text-shadow",d,f,k=>`var(--tw-text-shadow-color, ${k})`)]}}switch(o.value.value){case"none":return o.modifier?void 0:[P(),l("text-shadow","none")];case"inherit":return o.modifier?void 0:[P(),l("--tw-text-shadow-color","inherit")]}{let d=t.get([`--text-shadow-${o.value.value}`]);if(d)return[P(),l("--tw-text-shadow-alpha",f),...ce("text-shadow",d,f,x=>`var(--tw-text-shadow-color, ${x})`)]}{let d=Z(o,t,["--text-shadow-color","--color"]);if(d)return[P(),l("--tw-text-shadow-color",G(d,"var(--tw-text-shadow-alpha)"))]}}),i("text-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-shadow-color","--color"],modifiers:Array.from({length:21},(o,f)=>`${f*5}`)},{values:["none"]},{valueThemeKeys:["--text-shadow"],modifiers:Array.from({length:21},(o,f)=>`${f*5}`),hasDefaultValue:!0}]);{let k=function($){return`var(--tw-ring-inset,) 0 0 0 calc(${$} + var(--tw-ring-offset-width)) var(--tw-ring-color, ${x})`},N=function($){return`inset 0 0 0 ${$} var(--tw-inset-ring-color, currentcolor)`};var X=k,se=N;let o=["var(--tw-inset-shadow)","var(--tw-inset-ring-shadow)","var(--tw-ring-offset-shadow)","var(--tw-ring-shadow)","var(--tw-shadow)"].join(", "),f="0 0 #0000",d=()=>D([C("--tw-shadow",f),C("--tw-shadow-color"),C("--tw-shadow-alpha","100%","<percentage>"),C("--tw-inset-shadow",f),C("--tw-inset-shadow-color"),C("--tw-inset-shadow-alpha","100%","<percentage>"),C("--tw-ring-color"),C("--tw-ring-shadow",f),C("--tw-inset-ring-color"),C("--tw-inset-ring-shadow",f),C("--tw-ring-inset"),C("--tw-ring-offset-width","0px","<length>"),C("--tw-ring-offset-color","#fff"),C("--tw-ring-offset-shadow",f)]);e("shadow-initial",[d,["--tw-shadow-color","initial"]]),r.functional("shadow",$=>{let S;if($.modifier&&($.modifier.kind==="arbitrary"?S=$.modifier.value:E($.modifier.value)&&(S=`${$.modifier.value}%`)),!$.value){let U=t.get(["--shadow"]);return U===null?void 0:[d(),l("--tw-shadow-alpha",S),...ce("--tw-shadow",U,S,oe=>`var(--tw-shadow-color, ${oe})`),l("box-shadow",o)]}if($.value.kind==="arbitrary"){let U=$.value.value;switch($.value.dataType??q(U,["color"])){case"color":return U=Q(U,$.modifier,t),U===null?void 0:[d(),l("--tw-shadow-color",G(U,"var(--tw-shadow-alpha)"))];default:return[d(),l("--tw-shadow-alpha",S),...ce("--tw-shadow",U,S,gt=>`var(--tw-shadow-color, ${gt})`),l("box-shadow",o)]}}switch($.value.value){case"none":return $.modifier?void 0:[d(),l("--tw-shadow",f),l("box-shadow",o)];case"inherit":return $.modifier?void 0:[d(),l("--tw-shadow-color","inherit")]}{let U=t.get([`--shadow-${$.value.value}`]);if(U)return[d(),l("--tw-shadow-alpha",S),...ce("--tw-shadow",U,S,oe=>`var(--tw-shadow-color, ${oe})`),l("box-shadow",o)]}{let U=Z($,t,["--box-shadow-color","--color"]);if(U)return[d(),l("--tw-shadow-color",G(U,"var(--tw-shadow-alpha)"))]}}),i("shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},($,S)=>`${S*5}`)},{values:["none"]},{valueThemeKeys:["--shadow"],modifiers:Array.from({length:21},($,S)=>`${S*5}`),hasDefaultValue:!0}]),e("inset-shadow-initial",[d,["--tw-inset-shadow-color","initial"]]),r.functional("inset-shadow",$=>{let S;if($.modifier&&($.modifier.kind==="arbitrary"?S=$.modifier.value:E($.modifier.value)&&(S=`${$.modifier.value}%`)),!$.value){let U=t.get(["--inset-shadow"]);return U===null?void 0:[d(),l("--tw-inset-shadow-alpha",S),...ce("--tw-inset-shadow",U,S,oe=>`var(--tw-inset-shadow-color, ${oe})`),l("box-shadow",o)]}if($.value.kind==="arbitrary"){let U=$.value.value;switch($.value.dataType??q(U,["color"])){case"color":return U=Q(U,$.modifier,t),U===null?void 0:[d(),l("--tw-inset-shadow-color",G(U,"var(--tw-inset-shadow-alpha)"))];default:return[d(),l("--tw-inset-shadow-alpha",S),...ce("--tw-inset-shadow",U,S,gt=>`var(--tw-inset-shadow-color, ${gt})`,"inset "),l("box-shadow",o)]}}switch($.value.value){case"none":return $.modifier?void 0:[d(),l("--tw-inset-shadow",f),l("box-shadow",o)];case"inherit":return $.modifier?void 0:[d(),l("--tw-inset-shadow-color","inherit")]}{let U=t.get([`--inset-shadow-${$.value.value}`]);if(U)return[d(),l("--tw-inset-shadow-alpha",S),...ce("--tw-inset-shadow",U,S,oe=>`var(--tw-inset-shadow-color, ${oe})`),l("box-shadow",o)]}{let U=Z($,t,["--box-shadow-color","--color"]);if(U)return[d(),l("--tw-inset-shadow-color",G(U,"var(--tw-inset-shadow-alpha)"))]}}),i("inset-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},($,S)=>`${S*5}`)},{values:["none"]},{valueThemeKeys:["--inset-shadow"],modifiers:Array.from({length:21},($,S)=>`${S*5}`),hasDefaultValue:!0}]),e("ring-inset",[d,["--tw-ring-inset","inset"]]);let x=t.get(["--default-ring-color"])??"currentcolor";r.functional("ring",$=>{if(!$.value){if($.modifier)return;let S=t.get(["--default-ring-width"])??"1px";return[d(),l("--tw-ring-shadow",k(S)),l("box-shadow",o)]}if($.value.kind==="arbitrary"){let S=$.value.value;switch($.value.dataType??q(S,["color","length"])){case"length":return $.modifier?void 0:[d(),l("--tw-ring-shadow",k(S)),l("box-shadow",o)];default:return S=Q(S,$.modifier,t),S===null?void 0:[l("--tw-ring-color",S)]}}{let S=Z($,t,["--ring-color","--color"]);if(S)return[l("--tw-ring-color",S)]}{if($.modifier)return;let S=t.resolve($.value.value,["--ring-width"]);if(S===null&&E($.value.value)&&(S=`${$.value.value}px`),S)return[d(),l("--tw-ring-shadow",k(S)),l("box-shadow",o)]}}),i("ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},($,S)=>`${S*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]),r.functional("inset-ring",$=>{if(!$.value)return $.modifier?void 0:[d(),l("--tw-inset-ring-shadow",N("1px")),l("box-shadow",o)];if($.value.kind==="arbitrary"){let S=$.value.value;switch($.value.dataType??q(S,["color","length"])){case"length":return $.modifier?void 0:[d(),l("--tw-inset-ring-shadow",N(S)),l("box-shadow",o)];default:return S=Q(S,$.modifier,t),S===null?void 0:[l("--tw-inset-ring-color",S)]}}{let S=Z($,t,["--ring-color","--color"]);if(S)return[l("--tw-inset-ring-color",S)]}{if($.modifier)return;let S=t.resolve($.value.value,["--ring-width"]);if(S===null&&E($.value.value)&&(S=`${$.value.value}px`),S)return[d(),l("--tw-inset-ring-shadow",N(S)),l("box-shadow",o)]}}),i("inset-ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},($,S)=>`${S*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]);let R="var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)";r.functional("ring-offset",$=>{if($.value){if($.value.kind==="arbitrary"){let S=$.value.value;switch($.value.dataType??q(S,["color","length"])){case"length":return $.modifier?void 0:[l("--tw-ring-offset-width",S),l("--tw-ring-offset-shadow",R)];default:return S=Q(S,$.modifier,t),S===null?void 0:[l("--tw-ring-offset-color",S)]}}{let S=t.resolve($.value.value,["--ring-offset-width"]);if(S)return $.modifier?void 0:[l("--tw-ring-offset-width",S),l("--tw-ring-offset-shadow",R)];if(E($.value.value))return $.modifier?void 0:[l("--tw-ring-offset-width",`${$.value.value}px`),l("--tw-ring-offset-shadow",R)]}{let S=Z($,t,["--ring-offset-color","--color"]);if(S)return[l("--tw-ring-offset-color",S)]}}})}return i("ring-offset",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-offset-color","--color"],modifiers:Array.from({length:21},(o,f)=>`${f*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-offset-width"]}]),r.functional("@container",o=>{let f=null;if(o.value===null?f="inline-size":o.value.kind==="arbitrary"?f=o.value.value:o.value.kind==="named"&&o.value.value==="normal"&&(f="normal"),f!==null)return o.modifier?[l("container-type",f),l("container-name",o.modifier.value)]:[l("container-type",f)]}),i("@container",()=>[{values:["normal"],valueThemeKeys:[],hasDefaultValue:!0}]),r}var Tt=["number","integer","ratio","percentage"];function wr(t){let r=t.params;return nn.test(r)?i=>{let e={"--value":{usedSpacingInteger:!1,usedSpacingNumber:!1,themeKeys:new Set,literals:new Set},"--modifier":{usedSpacingInteger:!1,usedSpacingNumber:!1,themeKeys:new Set,literals:new Set}};z(t.nodes,n=>{if(n.kind!=="declaration"||!n.value||!n.value.includes("--value(")&&!n.value.includes("--modifier("))return;let s=H(n.value);te(s,a=>{if(a.kind!=="function")return;if(a.value==="--spacing"&&!(e["--modifier"].usedSpacingNumber&&e["--value"].usedSpacingNumber))return te(a.nodes,u=>{if(u.kind!=="function"||u.value!=="--value"&&u.value!=="--modifier")return;let c=u.value;for(let m of u.nodes)if(m.kind==="word"){if(m.value==="integer")e[c].usedSpacingInteger||=!0;else if(m.value==="number"&&(e[c].usedSpacingNumber||=!0,e["--modifier"].usedSpacingNumber&&e["--value"].usedSpacingNumber))return 2}}),0;if(a.value!=="--value"&&a.value!=="--modifier")return;let p=K(J(a.nodes),",");for(let[u,c]of p.entries())c=c.replace(/\\\*/g,"*"),c=c.replace(/--(.*?)\s--(.*?)/g,"--$1-*--$2"),c=c.replace(/\s+/g,""),c=c.replace(/(-\*){2,}/g,"-*"),c[0]==="-"&&c[1]==="-"&&!c.includes("-*")&&(c+="-*"),p[u]=c;a.nodes=H(p.join(","));for(let u of a.nodes)if(u.kind==="word"&&(u.value[0]==='"'||u.value[0]==="'")&&u.value[0]===u.value[u.value.length-1]){let c=u.value.slice(1,-1);e[a.value].literals.add(c)}else if(u.kind==="word"&&u.value[0]==="-"&&u.value[1]==="-"){let c=u.value.replace(/-\*.*$/g,"");e[a.value].themeKeys.add(c)}else if(u.kind==="word"&&!(u.value[0]==="["&&u.value[u.value.length-1]==="]")&&!Tt.includes(u.value)){console.warn(`Unsupported bare value data type: "${u.value}".
Only valid data types are: ${Tt.map(A=>`"${A}"`).join(", ")}.
`);let c=u.value,m=structuredClone(a),g="\xB6";te(m.nodes,(A,{replaceWith:b})=>{A.kind==="word"&&A.value===c&&b({kind:"word",value:g})});let h="^".repeat(J([u]).length),w=J([m]).indexOf(g),v=["```css",J([a])," ".repeat(w)+h,"```"].join(`
`);console.warn(v)}}),n.value=J(s)}),i.utilities.functional(r.slice(0,-2),n=>{let s=structuredClone(t),a=n.value,p=n.modifier;if(a===null)return;let u=!1,c=!1,m=!1,g=!1,h=new Map,w=!1;if(z([s],(v,{parent:A,replaceWith:b})=>{if(A?.kind!=="rule"&&A?.kind!=="at-rule"||v.kind!=="declaration"||!v.value)return;let y=H(v.value);(te(y,(T,{replaceWith:O})=>{if(T.kind==="function"){if(T.value==="--value"){u=!0;let _=gr(a,T,i);return _?(c=!0,_.ratio?w=!0:h.set(v,A),O(_.nodes),1):(u||=!1,b([]),2)}else if(T.value==="--modifier"){if(p===null)return b([]),2;m=!0;let _=gr(p,T,i);return _?(g=!0,O(_.nodes),1):(m||=!1,b([]),2)}}})??0)===0&&(v.value=J(y))}),u&&!c||m&&!g||w&&g||p&&!w&&!g)return null;if(w)for(let[v,A]of h){let b=A.nodes.indexOf(v);b!==-1&&A.nodes.splice(b,1)}return s.nodes}),i.utilities.suggest(r.slice(0,-2),()=>{let n=[],s=[];for(let[a,{literals:p,usedSpacingNumber:u,usedSpacingInteger:c,themeKeys:m}]of[[n,e["--value"]],[s,e["--modifier"]]]){for(let g of p)a.push(g);if(u)a.push(...nt);else if(c)for(let g of nt)E(g)&&a.push(g);for(let g of i.theme.keysInNamespaces(m))a.push(g)}return[{values:n,modifiers:s}]})}:rn.test(r)?i=>{i.utilities.static(r,()=>structuredClone(t.nodes))}:null}function gr(t,r,i){for(let e of r.nodes){if(t.kind==="named"&&e.kind==="word"&&(e.value[0]==="'"||e.value[0]==='"')&&e.value[e.value.length-1]===e.value[0]&&e.value.slice(1,-1)===t.value)return{nodes:H(t.value)};if(t.kind==="named"&&e.kind==="word"&&e.value[0]==="-"&&e.value[1]==="-"){let n=e.value;if(n.endsWith("-*")){n=n.slice(0,-2);let s=i.theme.resolve(t.value,[n]);if(s)return{nodes:H(s)}}else{let s=n.split("-*");if(s.length<=1)continue;let a=[s.shift()],p=i.theme.resolveWith(t.value,a,s);if(p){let[,u={}]=p;{let c=u[s.pop()];if(c)return{nodes:H(c)}}}}}else if(t.kind==="named"&&e.kind==="word"){if(!Tt.includes(e.value))continue;let n=e.value==="ratio"&&"fraction"in t?t.fraction:t.value;if(!n)continue;let s=q(n,[e.value]);if(s===null)continue;if(s==="ratio"){let[a,p]=K(n,"/");if(!E(a)||!E(p))continue}else{if(s==="number"&&!ye(n))continue;if(s==="percentage"&&!E(n.slice(0,-1)))continue}return{nodes:H(n),ratio:s==="ratio"}}else if(t.kind==="arbitrary"&&e.kind==="word"&&e.value[0]==="["&&e.value[e.value.length-1]==="]"){let n=e.value.slice(1,-1);if(n==="*")return{nodes:H(t.value)};if("dataType"in t&&t.dataType&&t.dataType!==n)continue;if("dataType"in t&&t.dataType)return{nodes:H(t.value)};if(q(t.value,[n])!==null)return{nodes:H(t.value)}}}}function ce(t,r,i,e,n=""){let s=!1,a=Ke(r,u=>i==null?e(u):u.startsWith("current")?e(G(u,i)):((u.startsWith("var(")||i.startsWith("var("))&&(s=!0),e(hr(u,i))));function p(u){return n?K(u,",").map(c=>n+c).join(","):u}return s?[l(t,p(Ke(r,e))),W("@supports (color: lab(from red l a b))",[l(t,p(a))])]:[l(t,p(a))]}function it(t,r,i,e,n=""){let s=!1,a=K(r,",").map(p=>Ke(p,u=>i==null?e(u):u.startsWith("current")?e(G(u,i)):((u.startsWith("var(")||i.startsWith("var("))&&(s=!0),e(hr(u,i))))).map(p=>`drop-shadow(${p})`).join(" ");return s?[l(t,n+K(r,",").map(p=>`drop-shadow(${Ke(p,e)})`).join(" ")),W("@supports (color: lab(from red l a b))",[l(t,n+a)])]:[l(t,n+a)]}var Et={"--alpha":on,"--spacing":ln,"--theme":an,theme:sn};function on(t,r,i,...e){let[n,s]=K(i,"/").map(a=>a.trim());if(!n||!s)throw new Error(`The --alpha(\u2026) function requires a color and an alpha value, e.g.: \`--alpha(${n||"var(--my-color)"} / ${s||"50%"})\``);if(e.length>0)throw new Error(`The --alpha(\u2026) function only accepts one argument, e.g.: \`--alpha(${n||"var(--my-color)"} / ${s||"50%"})\``);return G(n,s)}function ln(t,r,i,...e){if(!i)throw new Error("The --spacing(\u2026) function requires an argument, but received none.");if(e.length>0)throw new Error(`The --spacing(\u2026) function only accepts a single argument, but received ${e.length+1}.`);let n=t.theme.resolve(null,["--spacing"]);if(!n)throw new Error("The --spacing(\u2026) function requires that the `--spacing` theme variable exists, but it was not found.");return`calc(${n} * ${i})`}function an(t,r,i,...e){if(!i.startsWith("--"))throw new Error("The --theme(\u2026) function can only be used with CSS variables from your theme.");let n=!1;i.endsWith(" inline")&&(n=!0,i=i.slice(0,-7)),r.kind==="at-rule"&&(n=!0);let s=t.resolveThemeValue(i,n);if(!s){if(e.length>0)return e.join(", ");throw new Error(`Could not resolve value for theme function: \`theme(${i})\`. Consider checking if the variable name is correct or provide a fallback value to silence this error.`)}if(e.length===0)return s;let a=e.join(", ");if(a==="initial")return s;if(s==="initial")return a;if(s.startsWith("var(")||s.startsWith("theme(")||s.startsWith("--theme(")){let p=H(s);return cn(p,a),J(p)}return s}function sn(t,r,i,...e){i=un(i);let n=t.resolveThemeValue(i);if(!n&&e.length>0)return e.join(", ");if(!n)throw new Error(`Could not resolve value for theme function: \`theme(${i})\`. Consider checking if the path is correct or provide a fallback value to silence this error.`);return n}var kr=new RegExp(Object.keys(Et).map(t=>`${t}\\(`).join("|"));function $e(t,r){let i=0;return z(t,e=>{if(e.kind==="declaration"&&e.value&&kr.test(e.value)){i|=8,e.value=br(e.value,e,r);return}e.kind==="at-rule"&&(e.name==="@media"||e.name==="@custom-media"||e.name==="@container"||e.name==="@supports")&&kr.test(e.params)&&(i|=8,e.params=br(e.params,e,r))}),i}function br(t,r,i){let e=H(t);return te(e,(n,{replaceWith:s})=>{if(n.kind==="function"&&n.value in Et){let a=K(J(n.nodes).trim(),",").map(u=>u.trim()),p=Et[n.value](i,r,...a);return s(H(p))}}),J(e)}function un(t){if(t[0]!=="'"&&t[0]!=='"')return t;let r="",i=t[0];for(let e=1;e<t.length-1;e++){let n=t[e],s=t[e+1];n==="\\"&&(s===i||s==="\\")?(r+=s,e++):r+=n}return r}function cn(t,r){te(t,i=>{if(i.kind==="function"&&!(i.value!=="var"&&i.value!=="theme"&&i.value!=="--theme"))if(i.nodes.length===1)i.nodes.push({kind:"word",value:`, ${r}`});else{let e=i.nodes[i.nodes.length-1];e.kind==="word"&&e.value==="initial"&&(e.value=r)}})}function ot(t,r){let i=t.length,e=r.length,n=i<e?i:e;for(let s=0;s<n;s++){let a=t.charCodeAt(s),p=r.charCodeAt(s);if(a>=48&&a<=57&&p>=48&&p<=57){let u=s,c=s+1,m=s,g=s+1;for(a=t.charCodeAt(c);a>=48&&a<=57;)a=t.charCodeAt(++c);for(p=r.charCodeAt(g);p>=48&&p<=57;)p=r.charCodeAt(++g);let h=t.slice(u,c),w=r.slice(m,g),v=Number(h)-Number(w);if(v)return v;if(h<w)return-1;if(h>w)return 1;continue}if(a!==p)return a-p}return t.length-r.length}var fn=/^\d+\/\d+$/;function yr(t){let r=[];for(let e of t.utilities.keys("static"))r.push({name:e,utility:e,fraction:!1,modifiers:[]});for(let e of t.utilities.keys("functional")){let n=t.utilities.getCompletions(e);for(let s of n)for(let a of s.values){let p=a!==null&&fn.test(a),u=a===null?e:`${e}-${a}`;r.push({name:u,utility:e,fraction:p,modifiers:s.modifiers}),s.supportsNegative&&r.push({name:`-${u}`,utility:`-${e}`,fraction:p,modifiers:s.modifiers})}}return r.length===0?[]:(r.sort((e,n)=>ot(e.name,n.name)),pn(r))}function pn(t){let r=[],i=null,e=new Map,n=new B(()=>[]);for(let a of t){let{utility:p,fraction:u}=a;i||(i={utility:p,items:[]},e.set(p,i)),p!==i.utility&&(r.push(i),i={utility:p,items:[]},e.set(p,i)),u?n.get(p).push(a):i.items.push(a)}i&&r[r.length-1]!==i&&r.push(i);for(let[a,p]of n){let u=e.get(a);u&&u.items.push(...p)}let s=[];for(let a of r)for(let p of a.items)s.push([p.name,{modifiers:p.modifiers}]);return s}function xr(t){let r=[];for(let[e,n]of t.variants.entries()){let p=function({value:u,modifier:c}={}){let m=e;u&&(m+=s?`-${u}`:u),c&&(m+=`/${c}`);let g=t.parseVariant(m);if(!g)return[];let h=L(".__placeholder__",[]);if(Se(h,g,t.variants)===null)return[];let w=[];return Je(h.nodes,(v,{path:A})=>{if(v.kind!=="rule"&&v.kind!=="at-rule"||v.nodes.length>0)return;A.sort((V,T)=>{let O=V.kind==="at-rule",_=T.kind==="at-rule";return O&&!_?-1:!O&&_?1:0});let b=A.flatMap(V=>V.kind==="rule"?V.selector==="&"?[]:[V.selector]:V.kind==="at-rule"?[`${V.name} ${V.params}`]:[]),y="";for(let V=b.length-1;V>=0;V--)y=y===""?b[V]:`${b[V]} { ${y} }`;w.push(y)}),w};var i=p;if(n.kind==="arbitrary")continue;let s=e!=="@",a=t.variants.getCompletions(e);switch(n.kind){case"static":{r.push({name:e,values:a,isArbitrary:!1,hasDash:s,selectors:p});break}case"functional":{r.push({name:e,values:a,isArbitrary:!0,hasDash:s,selectors:p});break}case"compound":{r.push({name:e,values:a,isArbitrary:!0,hasDash:s,selectors:p});break}}}return r}function Ar(t,r){let{astNodes:i,nodeSorting:e}=me(Array.from(r),t),n=new Map(r.map(a=>[a,null])),s=0n;for(let a of i){let p=e.get(a)?.candidate;p&&n.set(p,n.get(p)??s++)}return r.map(a=>[a,n.get(a)??null])}var lt=/^@?[a-zA-Z0-9_-]*$/;var Rt=class{compareFns=new Map;variants=new Map;completions=new Map;groupOrder=null;lastOrder=0;static(r,i,{compounds:e,order:n}={}){this.set(r,{kind:"static",applyFn:i,compoundsWith:0,compounds:e??2,order:n})}fromAst(r,i){let e=[];z(i,n=>{n.kind==="rule"?e.push(n.selector):n.kind==="at-rule"&&n.name!=="@slot"&&e.push(`${n.name} ${n.params}`)}),this.static(r,n=>{let s=structuredClone(i);Ot(s,n.nodes),n.nodes=s},{compounds:xe(e)})}functional(r,i,{compounds:e,order:n}={}){this.set(r,{kind:"functional",applyFn:i,compoundsWith:0,compounds:e??2,order:n})}compound(r,i,e,{compounds:n,order:s}={}){this.set(r,{kind:"compound",applyFn:e,compoundsWith:i,compounds:n??2,order:s})}group(r,i){this.groupOrder=this.nextOrder(),i&&this.compareFns.set(this.groupOrder,i),r(),this.groupOrder=null}has(r){return this.variants.has(r)}get(r){return this.variants.get(r)}kind(r){return this.variants.get(r)?.kind}compoundsWith(r,i){let e=this.variants.get(r),n=typeof i=="string"?this.variants.get(i):i.kind==="arbitrary"?{compounds:xe([i.selector])}:this.variants.get(i.root);return!(!e||!n||e.kind!=="compound"||n.compounds===0||e.compoundsWith===0||(e.compoundsWith&n.compounds)===0)}suggest(r,i){this.completions.set(r,i)}getCompletions(r){return this.completions.get(r)?.()??[]}compare(r,i){if(r===i)return 0;if(r===null)return-1;if(i===null)return 1;if(r.kind==="arbitrary"&&i.kind==="arbitrary")return r.selector<i.selector?-1:1;if(r.kind==="arbitrary")return 1;if(i.kind==="arbitrary")return-1;let e=this.variants.get(r.root).order,n=this.variants.get(i.root).order,s=e-n;if(s!==0)return s;if(r.kind==="compound"&&i.kind==="compound"){let c=this.compare(r.variant,i.variant);return c!==0?c:r.modifier&&i.modifier?r.modifier.value<i.modifier.value?-1:1:r.modifier?1:i.modifier?-1:0}let a=this.compareFns.get(e);if(a!==void 0)return a(r,i);if(r.root!==i.root)return r.root<i.root?-1:1;let p=r.value,u=i.value;return p===null?-1:u===null||p.kind==="arbitrary"&&u.kind!=="arbitrary"?1:p.kind!=="arbitrary"&&u.kind==="arbitrary"||p.value<u.value?-1:1}keys(){return this.variants.keys()}entries(){return this.variants.entries()}set(r,{kind:i,applyFn:e,compounds:n,compoundsWith:s,order:a}){let p=this.variants.get(r);p?Object.assign(p,{kind:i,applyFn:e,compounds:n}):(a===void 0&&(this.lastOrder=this.nextOrder(),a=this.lastOrder),this.variants.set(r,{kind:i,applyFn:e,order:a,compoundsWith:s,compounds:n}))}nextOrder(){return this.groupOrder??this.lastOrder+1}};function xe(t){let r=0;for(let i of t){if(i[0]==="@"){if(!i.startsWith("@media")&&!i.startsWith("@supports")&&!i.startsWith("@container"))return 0;r|=1;continue}if(i.includes("::"))return 0;r|=2}return r}function Nr(t){let r=new Rt;function i(c,m,{compounds:g}={}){g=g??xe(m),r.static(c,h=>{h.nodes=m.map(w=>W(w,h.nodes))},{compounds:g})}i("*",[":is(& > *)"],{compounds:0}),i("**",[":is(& *)"],{compounds:0});function e(c,m){return m.map(g=>{g=g.trim();let h=K(g," ");return h[0]==="not"?h.slice(1).join(" "):c==="@container"?h[0][0]==="("?`not ${g}`:h[1]==="not"?`${h[0]} ${h.slice(2).join(" ")}`:`${h[0]} not ${h.slice(1).join(" ")}`:`not ${g}`})}let n=["@media","@supports","@container"];function s(c){for(let m of n){if(m!==c.name)continue;let g=K(c.params,",");return g.length>1?null:(g=e(c.name,g),I(c.name,g.join(", ")))}return null}function a(c){return c.includes("::")?null:`&:not(${K(c,",").map(g=>(g=g.replaceAll("&","*"),g)).join(", ")})`}r.compound("not",3,(c,m)=>{if(m.variant.kind==="arbitrary"&&m.variant.relative||m.modifier)return null;let g=!1;if(z([c],(h,{path:w})=>{if(h.kind!=="rule"&&h.kind!=="at-rule")return 0;if(h.nodes.length>0)return 0;let v=[],A=[];for(let y of w)y.kind==="at-rule"?v.push(y):y.kind==="rule"&&A.push(y);if(v.length>1)return 2;if(A.length>1)return 2;let b=[];for(let y of A){let V=a(y.selector);if(!V)return g=!1,2;b.push(L(V,[]))}for(let y of v){let V=s(y);if(!V)return g=!1,2;b.push(V)}return Object.assign(c,L("&",b)),g=!0,1}),c.kind==="rule"&&c.selector==="&"&&c.nodes.length===1&&Object.assign(c,c.nodes[0]),!g)return null}),r.suggest("not",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("not",c))),r.compound("group",2,(c,m)=>{if(m.variant.kind==="arbitrary"&&m.variant.relative)return null;let g=m.modifier?`:where(.${t.prefix?`${t.prefix}\\:`:""}group\\/${m.modifier.value})`:`:where(.${t.prefix?`${t.prefix}\\:`:""}group)`,h=!1;if(z([c],(w,{path:v})=>{if(w.kind!=="rule")return 0;for(let b of v.slice(0,-1))if(b.kind==="rule")return h=!1,2;let A=w.selector.replaceAll("&",g);K(A,",").length>1&&(A=`:is(${A})`),w.selector=`&:is(${A} *)`,h=!0}),!h)return null}),r.suggest("group",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("group",c))),r.compound("peer",2,(c,m)=>{if(m.variant.kind==="arbitrary"&&m.variant.relative)return null;let g=m.modifier?`:where(.${t.prefix?`${t.prefix}\\:`:""}peer\\/${m.modifier.value})`:`:where(.${t.prefix?`${t.prefix}\\:`:""}peer)`,h=!1;if(z([c],(w,{path:v})=>{if(w.kind!=="rule")return 0;for(let b of v.slice(0,-1))if(b.kind==="rule")return h=!1,2;let A=w.selector.replaceAll("&",g);K(A,",").length>1&&(A=`:is(${A})`),w.selector=`&:is(${A} ~ *)`,h=!0}),!h)return null}),r.suggest("peer",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("peer",c))),i("first-letter",["&::first-letter"]),i("first-line",["&::first-line"]),i("marker",["& *::marker","&::marker","& *::-webkit-details-marker","&::-webkit-details-marker"]),i("selection",["& *::selection","&::selection"]),i("file",["&::file-selector-button"]),i("placeholder",["&::placeholder"]),i("backdrop",["&::backdrop"]),i("details-content",["&::details-content"]);{let c=function(){return D([I("@property","--tw-content",[l("syntax",'"*"'),l("initial-value",'""'),l("inherits","false")])])};var p=c;r.static("before",m=>{m.nodes=[L("&::before",[c(),l("content","var(--tw-content)"),...m.nodes])]},{compounds:0}),r.static("after",m=>{m.nodes=[L("&::after",[c(),l("content","var(--tw-content)"),...m.nodes])]},{compounds:0})}i("first",["&:first-child"]),i("last",["&:last-child"]),i("only",["&:only-child"]),i("odd",["&:nth-child(odd)"]),i("even",["&:nth-child(even)"]),i("first-of-type",["&:first-of-type"]),i("last-of-type",["&:last-of-type"]),i("only-of-type",["&:only-of-type"]),i("visited",["&:visited"]),i("target",["&:target"]),i("open",["&:is([open], :popover-open, :open)"]),i("default",["&:default"]),i("checked",["&:checked"]),i("indeterminate",["&:indeterminate"]),i("placeholder-shown",["&:placeholder-shown"]),i("autofill",["&:autofill"]),i("optional",["&:optional"]),i("required",["&:required"]),i("valid",["&:valid"]),i("invalid",["&:invalid"]),i("user-valid",["&:user-valid"]),i("user-invalid",["&:user-invalid"]),i("in-range",["&:in-range"]),i("out-of-range",["&:out-of-range"]),i("read-only",["&:read-only"]),i("empty",["&:empty"]),i("focus-within",["&:focus-within"]),r.static("hover",c=>{c.nodes=[L("&:hover",[I("@media","(hover: hover)",c.nodes)])]}),i("focus",["&:focus"]),i("focus-visible",["&:focus-visible"]),i("active",["&:active"]),i("enabled",["&:enabled"]),i("disabled",["&:disabled"]),i("inert",["&:is([inert], [inert] *)"]),r.compound("in",2,(c,m)=>{if(m.modifier)return null;let g=!1;if(z([c],(h,{path:w})=>{if(h.kind!=="rule")return 0;for(let v of w.slice(0,-1))if(v.kind==="rule")return g=!1,2;h.selector=`:where(${h.selector.replaceAll("&","*")}) &`,g=!0}),!g)return null}),r.suggest("in",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("in",c))),r.compound("has",2,(c,m)=>{if(m.modifier)return null;let g=!1;if(z([c],(h,{path:w})=>{if(h.kind!=="rule")return 0;for(let v of w.slice(0,-1))if(v.kind==="rule")return g=!1,2;h.selector=`&:has(${h.selector.replaceAll("&","*")})`,g=!0}),!g)return null}),r.suggest("has",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("has",c))),r.functional("aria",(c,m)=>{if(!m.value||m.modifier)return null;m.value.kind==="arbitrary"?c.nodes=[L(`&[aria-${Cr(m.value.value)}]`,c.nodes)]:c.nodes=[L(`&[aria-${m.value.value}="true"]`,c.nodes)]}),r.suggest("aria",()=>["busy","checked","disabled","expanded","hidden","pressed","readonly","required","selected"]),r.functional("data",(c,m)=>{if(!m.value||m.modifier)return null;c.nodes=[L(`&[data-${Cr(m.value.value)}]`,c.nodes)]}),r.functional("nth",(c,m)=>{if(!m.value||m.modifier||m.value.kind==="named"&&!E(m.value.value))return null;c.nodes=[L(`&:nth-child(${m.value.value})`,c.nodes)]}),r.functional("nth-last",(c,m)=>{if(!m.value||m.modifier||m.value.kind==="named"&&!E(m.value.value))return null;c.nodes=[L(`&:nth-last-child(${m.value.value})`,c.nodes)]}),r.functional("nth-of-type",(c,m)=>{if(!m.value||m.modifier||m.value.kind==="named"&&!E(m.value.value))return null;c.nodes=[L(`&:nth-of-type(${m.value.value})`,c.nodes)]}),r.functional("nth-last-of-type",(c,m)=>{if(!m.value||m.modifier||m.value.kind==="named"&&!E(m.value.value))return null;c.nodes=[L(`&:nth-last-of-type(${m.value.value})`,c.nodes)]}),r.functional("supports",(c,m)=>{if(!m.value||m.modifier)return null;let g=m.value.value;if(g===null)return null;if(/^[\w-]*\s*\(/.test(g)){let h=g.replace(/\b(and|or|not)\b/g," $1 ");c.nodes=[I("@supports",h,c.nodes)];return}g.includes(":")||(g=`${g}: var(--tw)`),(g[0]!=="("||g[g.length-1]!==")")&&(g=`(${g})`),c.nodes=[I("@supports",g,c.nodes)]},{compounds:1}),i("motion-safe",["@media (prefers-reduced-motion: no-preference)"]),i("motion-reduce",["@media (prefers-reduced-motion: reduce)"]),i("contrast-more",["@media (prefers-contrast: more)"]),i("contrast-less",["@media (prefers-contrast: less)"]);{let c=function(m,g,h,w){if(m===g)return 0;let v=w.get(m);if(v===null)return h==="asc"?-1:1;let A=w.get(g);return A===null?h==="asc"?1:-1:be(v,A,h)};var u=c;{let m=t.namespace("--breakpoint"),g=new B(h=>{switch(h.kind){case"static":return t.resolveValue(h.root,["--breakpoint"])??null;case"functional":{if(!h.value||h.modifier)return null;let w=null;return h.value.kind==="arbitrary"?w=h.value.value:h.value.kind==="named"&&(w=t.resolveValue(h.value.value,["--breakpoint"])),!w||w.includes("var(")?null:w}case"arbitrary":case"compound":return null}});r.group(()=>{r.functional("max",(h,w)=>{if(w.modifier)return null;let v=g.get(w);if(v===null)return null;h.nodes=[I("@media",`(width < ${v})`,h.nodes)]},{compounds:1})},(h,w)=>c(h,w,"desc",g)),r.suggest("max",()=>Array.from(m.keys()).filter(h=>h!==null)),r.group(()=>{for(let[h,w]of t.namespace("--breakpoint"))h!==null&&r.static(h,v=>{v.nodes=[I("@media",`(width >= ${w})`,v.nodes)]},{compounds:1});r.functional("min",(h,w)=>{if(w.modifier)return null;let v=g.get(w);if(v===null)return null;h.nodes=[I("@media",`(width >= ${v})`,h.nodes)]},{compounds:1})},(h,w)=>c(h,w,"asc",g)),r.suggest("min",()=>Array.from(m.keys()).filter(h=>h!==null))}{let m=t.namespace("--container"),g=new B(h=>{switch(h.kind){case"functional":{if(h.value===null)return null;let w=null;return h.value.kind==="arbitrary"?w=h.value.value:h.value.kind==="named"&&(w=t.resolveValue(h.value.value,["--container"])),!w||w.includes("var(")?null:w}case"static":case"arbitrary":case"compound":return null}});r.group(()=>{r.functional("@max",(h,w)=>{let v=g.get(w);if(v===null)return null;h.nodes=[I("@container",w.modifier?`${w.modifier.value} (width < ${v})`:`(width < ${v})`,h.nodes)]},{compounds:1})},(h,w)=>c(h,w,"desc",g)),r.suggest("@max",()=>Array.from(m.keys()).filter(h=>h!==null)),r.group(()=>{r.functional("@",(h,w)=>{let v=g.get(w);if(v===null)return null;h.nodes=[I("@container",w.modifier?`${w.modifier.value} (width >= ${v})`:`(width >= ${v})`,h.nodes)]},{compounds:1}),r.functional("@min",(h,w)=>{let v=g.get(w);if(v===null)return null;h.nodes=[I("@container",w.modifier?`${w.modifier.value} (width >= ${v})`:`(width >= ${v})`,h.nodes)]},{compounds:1})},(h,w)=>c(h,w,"asc",g)),r.suggest("@min",()=>Array.from(m.keys()).filter(h=>h!==null)),r.suggest("@",()=>Array.from(m.keys()).filter(h=>h!==null))}}return i("portrait",["@media (orientation: portrait)"]),i("landscape",["@media (orientation: landscape)"]),i("ltr",['&:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *)']),i("rtl",['&:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *)']),i("dark",["@media (prefers-color-scheme: dark)"]),i("starting",["@starting-style"]),i("print",["@media print"]),i("forced-colors",["@media (forced-colors: active)"]),i("inverted-colors",["@media (inverted-colors: inverted)"]),i("pointer-none",["@media (pointer: none)"]),i("pointer-coarse",["@media (pointer: coarse)"]),i("pointer-fine",["@media (pointer: fine)"]),i("any-pointer-none",["@media (any-pointer: none)"]),i("any-pointer-coarse",["@media (any-pointer: coarse)"]),i("any-pointer-fine",["@media (any-pointer: fine)"]),i("noscript",["@media (scripting: none)"]),r}function Cr(t){if(t.includes("=")){let[r,...i]=K(t,"="),e=i.join("=").trim();if(e[0]==="'"||e[0]==='"')return t;if(e.length>1){let n=e[e.length-1];if(e[e.length-2]===" "&&(n==="i"||n==="I"||n==="s"||n==="S"))return`${r}="${e.slice(0,-2)}" ${n}`}return`${r}="${e}"`}return t}function Ot(t,r){z(t,(i,{replaceWith:e})=>{if(i.kind==="at-rule"&&i.name==="@slot")e(r);else if(i.kind==="at-rule"&&(i.name==="@keyframes"||i.name==="@property"))return Object.assign(i,D([I(i.name,i.params,i.nodes)])),1})}function $r(t){let r=vr(t),i=Nr(t),e=new B(u=>sr(u,p)),n=new B(u=>Array.from(ar(u,p))),s=new B(u=>{let c=Vr(u,p);try{$e(c.map(({node:m})=>m),p)}catch{return[]}return c}),a=new B(u=>{for(let c of Ye(u))t.markUsedVariable(c)}),p={theme:t,utilities:r,variants:i,invalidCandidates:new Set,important:!1,candidatesToCss(u){let c=[];for(let m of u){let g=!1,{astNodes:h}=me([m],this,{onInvalidCandidate(){g=!0}});h=ke(h,p,0),h.length===0||g?c.push(null):c.push(ie(h))}return c},getClassOrder(u){return Ar(this,u)},getClassList(){return yr(this)},getVariants(){return xr(this)},parseCandidate(u){return n.get(u)},parseVariant(u){return e.get(u)},compileAstNodes(u){return s.get(u)},getVariantOrder(){let u=Array.from(e.values());u.sort((h,w)=>this.variants.compare(h,w));let c=new Map,m,g=0;for(let h of u)h!==null&&(m!==void 0&&this.variants.compare(m,h)!==0&&g++,c.set(h,g),m=h);return c},resolveThemeValue(u,c=!0){let m=u.lastIndexOf("/"),g=null;m!==-1&&(g=u.slice(m+1).trim(),u=u.slice(0,m).trim());let h=t.resolve(null,[u],c?1:0)??void 0;return g&&h?G(h,g):h},trackUsedVariables(u){a.get(u)}};return p}var Pt=["container-type","pointer-events","visibility","position","inset","inset-inline","inset-block","inset-inline-start","inset-inline-end","top","right","bottom","left","isolation","z-index","order","grid-column","grid-column-start","grid-column-end","grid-row","grid-row-start","grid-row-end","float","clear","--tw-container-component","margin","margin-inline","margin-block","margin-inline-start","margin-inline-end","margin-top","margin-right","margin-bottom","margin-left","box-sizing","display","field-sizing","aspect-ratio","height","max-height","min-height","width","max-width","min-width","flex","flex-shrink","flex-grow","flex-basis","table-layout","caption-side","border-collapse","border-spacing","transform-origin","translate","--tw-translate-x","--tw-translate-y","--tw-translate-z","scale","--tw-scale-x","--tw-scale-y","--tw-scale-z","rotate","--tw-rotate-x","--tw-rotate-y","--tw-rotate-z","--tw-skew-x","--tw-skew-y","transform","animation","cursor","touch-action","--tw-pan-x","--tw-pan-y","--tw-pinch-zoom","resize","scroll-snap-type","--tw-scroll-snap-strictness","scroll-snap-align","scroll-snap-stop","scroll-margin","scroll-margin-inline","scroll-margin-block","scroll-margin-inline-start","scroll-margin-inline-end","scroll-margin-top","scroll-margin-right","scroll-margin-bottom","scroll-margin-left","scroll-padding","scroll-padding-inline","scroll-padding-block","scroll-padding-inline-start","scroll-padding-inline-end","scroll-padding-top","scroll-padding-right","scroll-padding-bottom","scroll-padding-left","list-style-position","list-style-type","list-style-image","appearance","columns","break-before","break-inside","break-after","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-template-columns","grid-template-rows","flex-direction","flex-wrap","place-content","place-items","align-content","align-items","justify-content","justify-items","gap","column-gap","row-gap","--tw-space-x-reverse","--tw-space-y-reverse","divide-x-width","divide-y-width","--tw-divide-y-reverse","divide-style","divide-color","place-self","align-self","justify-self","overflow","overflow-x","overflow-y","overscroll-behavior","overscroll-behavior-x","overscroll-behavior-y","scroll-behavior","border-radius","border-start-radius","border-end-radius","border-top-radius","border-right-radius","border-bottom-radius","border-left-radius","border-start-start-radius","border-start-end-radius","border-end-end-radius","border-end-start-radius","border-top-left-radius","border-top-right-radius","border-bottom-right-radius","border-bottom-left-radius","border-width","border-inline-width","border-block-width","border-inline-start-width","border-inline-end-width","border-top-width","border-right-width","border-bottom-width","border-left-width","border-style","border-inline-style","border-block-style","border-inline-start-style","border-inline-end-style","border-top-style","border-right-style","border-bottom-style","border-left-style","border-color","border-inline-color","border-block-color","border-inline-start-color","border-inline-end-color","border-top-color","border-right-color","border-bottom-color","border-left-color","background-color","background-image","--tw-gradient-position","--tw-gradient-stops","--tw-gradient-via-stops","--tw-gradient-from","--tw-gradient-from-position","--tw-gradient-via","--tw-gradient-via-position","--tw-gradient-to","--tw-gradient-to-position","mask-image","--tw-mask-top","--tw-mask-top-from-color","--tw-mask-top-from-position","--tw-mask-top-to-color","--tw-mask-top-to-position","--tw-mask-right","--tw-mask-right-from-color","--tw-mask-right-from-position","--tw-mask-right-to-color","--tw-mask-right-to-position","--tw-mask-bottom","--tw-mask-bottom-from-color","--tw-mask-bottom-from-position","--tw-mask-bottom-to-color","--tw-mask-bottom-to-position","--tw-mask-left","--tw-mask-left-from-color","--tw-mask-left-from-position","--tw-mask-left-to-color","--tw-mask-left-to-position","--tw-mask-linear","--tw-mask-linear-position","--tw-mask-linear-from-color","--tw-mask-linear-from-position","--tw-mask-linear-to-color","--tw-mask-linear-to-position","--tw-mask-radial","--tw-mask-radial-shape","--tw-mask-radial-size","--tw-mask-radial-position","--tw-mask-radial-from-color","--tw-mask-radial-from-position","--tw-mask-radial-to-color","--tw-mask-radial-to-position","--tw-mask-conic","--tw-mask-conic-position","--tw-mask-conic-from-color","--tw-mask-conic-from-position","--tw-mask-conic-to-color","--tw-mask-conic-to-position","box-decoration-break","background-size","background-attachment","background-clip","background-position","background-repeat","background-origin","mask-composite","mask-mode","mask-type","mask-size","mask-clip","mask-position","mask-repeat","mask-origin","fill","stroke","stroke-width","object-fit","object-position","padding","padding-inline","padding-block","padding-inline-start","padding-inline-end","padding-top","padding-right","padding-bottom","padding-left","text-align","text-indent","vertical-align","font-family","font-size","line-height","font-weight","letter-spacing","text-wrap","overflow-wrap","word-break","text-overflow","hyphens","white-space","color","text-transform","font-style","font-stretch","font-variant-numeric","text-decoration-line","text-decoration-color","text-decoration-style","text-decoration-thickness","text-underline-offset","-webkit-font-smoothing","placeholder-color","caret-color","accent-color","color-scheme","opacity","background-blend-mode","mix-blend-mode","box-shadow","--tw-shadow","--tw-shadow-color","--tw-ring-shadow","--tw-ring-color","--tw-inset-shadow","--tw-inset-shadow-color","--tw-inset-ring-shadow","--tw-inset-ring-color","--tw-ring-offset-width","--tw-ring-offset-color","outline","outline-width","outline-offset","outline-color","--tw-blur","--tw-brightness","--tw-contrast","--tw-drop-shadow","--tw-grayscale","--tw-hue-rotate","--tw-invert","--tw-saturate","--tw-sepia","filter","--tw-backdrop-blur","--tw-backdrop-brightness","--tw-backdrop-contrast","--tw-backdrop-grayscale","--tw-backdrop-hue-rotate","--tw-backdrop-invert","--tw-backdrop-opacity","--tw-backdrop-saturate","--tw-backdrop-sepia","backdrop-filter","transition-property","transition-behavior","transition-delay","transition-duration","transition-timing-function","will-change","contain","content","forced-color-adjust"];function me(t,r,{onInvalidCandidate:i}={}){let e=new Map,n=[],s=new Map;for(let p of t){if(r.invalidCandidates.has(p)){i?.(p);continue}let u=r.parseCandidate(p);if(u.length===0){i?.(p);continue}s.set(p,u)}let a=r.getVariantOrder();for(let[p,u]of s){let c=!1;for(let m of u){let g=r.compileAstNodes(m);if(g.length!==0){c=!0;for(let{node:h,propertySort:w}of g){let v=0n;for(let A of m.variants)v|=1n<<BigInt(a.get(A));e.set(h,{properties:w,variants:v,candidate:p}),n.push(h)}}}c||i?.(p)}return n.sort((p,u)=>{let c=e.get(p),m=e.get(u);if(c.variants-m.variants!==0n)return Number(c.variants-m.variants);let g=0;for(;g<c.properties.order.length&&g<m.properties.order.length&&c.properties.order[g]===m.properties.order[g];)g+=1;return(c.properties.order[g]??1/0)-(m.properties.order[g]??1/0)||m.properties.count-c.properties.count||ot(c.candidate,m.candidate)}),{astNodes:n,nodeSorting:e}}function Vr(t,r){let i=dn(t,r);if(i.length===0)return[];let e=[],n=`.${fe(t.raw)}`;for(let s of i){let a=mn(s);(t.important||r.important)&&Tr(s);let p={kind:"rule",selector:n,nodes:s};for(let u of t.variants)if(Se(p,u,r.variants)===null)return[];e.push({node:p,propertySort:a})}return e}function Se(t,r,i,e=0){if(r.kind==="arbitrary"){if(r.relative&&e===0)return null;t.nodes=[W(r.selector,t.nodes)];return}let{applyFn:n}=i.get(r.root);if(r.kind==="compound"){let a=I("@slot");if(Se(a,r.variant,i,e+1)===null||r.root==="not"&&a.nodes.length>1)return null;for(let u of a.nodes)if(u.kind!=="rule"&&u.kind!=="at-rule"||n(u,r)===null)return null;z(a.nodes,u=>{if((u.kind==="rule"||u.kind==="at-rule")&&u.nodes.length<=0)return u.nodes=t.nodes,1}),t.nodes=a.nodes;return}if(n(t,r)===null)return null}function Sr(t){let r=t.options?.types??[];return r.length>1&&r.includes("any")}function dn(t,r){if(t.kind==="arbitrary"){let a=t.value;return t.modifier&&(a=Q(a,t.modifier,r.theme)),a===null?[]:[[l(t.property,a)]]}let i=r.utilities.get(t.root)??[],e=[],n=i.filter(a=>!Sr(a));for(let a of n){if(a.kind!==t.kind)continue;let p=a.compileFn(t);if(p!==void 0){if(p===null)return e;e.push(p)}}if(e.length>0)return e;let s=i.filter(a=>Sr(a));for(let a of s){if(a.kind!==t.kind)continue;let p=a.compileFn(t);if(p!==void 0){if(p===null)return e;e.push(p)}}return e}function Tr(t){for(let r of t)r.kind!=="at-root"&&(r.kind==="declaration"?r.important=!0:(r.kind==="rule"||r.kind==="at-rule")&&Tr(r.nodes))}function mn(t){let r=new Set,i=0,e=t.slice(),n=!1;for(;e.length>0;){let s=e.shift();if(s.kind==="declaration"){if(s.value===void 0||(i++,n))continue;if(s.property==="--tw-sort"){let p=Pt.indexOf(s.value??"");if(p!==-1){r.add(p),n=!0;continue}}let a=Pt.indexOf(s.property);a!==-1&&r.add(a)}else if(s.kind==="rule"||s.kind==="at-rule")for(let a of s.nodes)e.push(a)}return{order:Array.from(r).sort((s,a)=>s-a),count:i}}function De(t,r){let i=0,e=W("&",t),n=new Set,s=new B(()=>new Set),a=new B(()=>new Set);z([e],(g,{parent:h})=>{if(g.kind==="at-rule"){if(g.name==="@keyframes")return z(g.nodes,w=>{if(w.kind==="at-rule"&&w.name==="@apply")throw new Error("You cannot use `@apply` inside `@keyframes`.")}),1;if(g.name==="@utility"){let w=g.params.replace(/-\*$/,"");a.get(w).add(g),z(g.nodes,v=>{if(!(v.kind!=="at-rule"||v.name!=="@apply")){n.add(g);for(let A of Er(v,r))s.get(g).add(A)}});return}if(g.name==="@apply"){if(h===null)return;i|=1,n.add(h);for(let w of Er(g,r))s.get(h).add(w)}}});let p=new Set,u=[],c=new Set;function m(g,h=[]){if(!p.has(g)){if(c.has(g)){let w=h[(h.indexOf(g)+1)%h.length];throw g.kind==="at-rule"&&g.name==="@utility"&&w.kind==="at-rule"&&w.name==="@utility"&&z(g.nodes,v=>{if(v.kind!=="at-rule"||v.name!=="@apply")return;let A=v.params.split(/\s+/g);for(let b of A)for(let y of r.parseCandidate(b))switch(y.kind){case"arbitrary":break;case"static":case"functional":if(w.params.replace(/-\*$/,"")===y.root)throw new Error(`You cannot \`@apply\` the \`${b}\` utility here because it creates a circular dependency.`);break;default:}}),new Error(`Circular dependency detected:

${ie([g])}
Relies on:

${ie([w])}`)}c.add(g);for(let w of s.get(g))for(let v of a.get(w))h.push(g),m(v,h),h.pop();p.add(g),c.delete(g),u.push(g)}}for(let g of n)m(g);for(let g of u)if("nodes"in g)for(let h=0;h<g.nodes.length;h++){let w=g.nodes[h];if(w.kind!=="at-rule"||w.name!=="@apply")continue;let v=w.params.split(/\s+/g);{let A=me(v,r,{onInvalidCandidate:y=>{throw new Error(`Cannot apply unknown utility class: ${y}`)}}).astNodes,b=[];for(let y of A)if(y.kind==="rule")for(let V of y.nodes)b.push(V);else b.push(y);g.nodes.splice(h,1,...b)}}return i}function*Er(t,r){for(let i of t.params.split(/\s+/g))for(let e of r.parseCandidate(i))switch(e.kind){case"arbitrary":break;case"static":case"functional":yield e.root;break;default:}}async function _t(t,r,i,e=0){let n=0,s=[];return z(t,(a,{replaceWith:p})=>{if(a.kind==="at-rule"&&(a.name==="@import"||a.name==="@reference")){let u=gn(H(a.params));if(u===null)return;a.name==="@reference"&&(u.media="reference"),n|=2;let{uri:c,layer:m,media:g,supports:h}=u;if(c.startsWith("data:")||c.startsWith("http://")||c.startsWith("https://"))return;let w=le({},[]);return s.push((async()=>{if(e>100)throw new Error(`Exceeded maximum recursion depth while resolving \`${c}\` in \`${r}\`)`);let v=await i(c,r),A=ve(v.content);await _t(A,v.base,i,e+1),w.nodes=hn([le({base:v.base},A)],m,g,h)})()),p(w),1}}),s.length>0&&await Promise.all(s),n}function gn(t){let r,i=null,e=null,n=null;for(let s=0;s<t.length;s++){let a=t[s];if(a.kind!=="separator"){if(a.kind==="word"&&!r){if(!a.value||a.value[0]!=='"'&&a.value[0]!=="'")return null;r=a.value.slice(1,-1);continue}if(a.kind==="function"&&a.value.toLowerCase()==="url"||!r)return null;if((a.kind==="word"||a.kind==="function")&&a.value.toLowerCase()==="layer"){if(i)return null;if(n)throw new Error("`layer(\u2026)` in an `@import` should come before any other functions or conditions");"nodes"in a?i=J(a.nodes):i="";continue}if(a.kind==="function"&&a.value.toLowerCase()==="supports"){if(n)return null;n=J(a.nodes);continue}e=J(t.slice(s));break}}return r?{uri:r,layer:i,media:e,supports:n}:null}function hn(t,r,i,e){let n=t;return r!==null&&(n=[I("@layer",r,n)]),i!==null&&(n=[I("@media",i,n)]),e!==null&&(n=[I("@supports",e[0]==="("?e:`(${e})`,n)]),n}function Te(t,r=null){return Array.isArray(t)&&t.length===2&&typeof t[1]=="object"&&typeof t[1]!==null?r?t[1][r]??null:t[0]:Array.isArray(t)&&r===null?t.join(", "):typeof t=="string"&&r===null?t:null}function Rr(t,{theme:r},i){for(let e of i){let n=at([e]);n&&t.theme.clearNamespace(`--${n}`,4)}for(let[e,n]of vn(r)){if(typeof n!="string"&&typeof n!="number")continue;if(typeof n=="string"&&(n=n.replace(/<alpha-value>/g,"1")),e[0]==="opacity"&&(typeof n=="number"||typeof n=="string")){let a=typeof n=="string"?parseFloat(n):n;a>=0&&a<=1&&(n=a*100+"%")}let s=at(e);s&&t.theme.add(`--${s}`,""+n,7)}if(Object.hasOwn(r,"fontFamily")){let e=5;{let n=Te(r.fontFamily.sans);n&&t.theme.hasDefault("--font-sans")&&(t.theme.add("--default-font-family",n,e),t.theme.add("--default-font-feature-settings",Te(r.fontFamily.sans,"fontFeatureSettings")??"normal",e),t.theme.add("--default-font-variation-settings",Te(r.fontFamily.sans,"fontVariationSettings")??"normal",e))}{let n=Te(r.fontFamily.mono);n&&t.theme.hasDefault("--font-mono")&&(t.theme.add("--default-mono-font-family",n,e),t.theme.add("--default-mono-font-feature-settings",Te(r.fontFamily.mono,"fontFeatureSettings")??"normal",e),t.theme.add("--default-mono-font-variation-settings",Te(r.fontFamily.mono,"fontVariationSettings")??"normal",e))}}return r}function vn(t){let r=[];return Or(t,[],(i,e)=>{if(kn(i))return r.push([e,i]),1;if(bn(i)){r.push([e,i[0]]);for(let n of Reflect.ownKeys(i[1]))r.push([[...e,`-${n}`],i[1][n]]);return 1}if(Array.isArray(i)&&i.every(n=>typeof n=="string"))return e[0]==="fontSize"?(r.push([e,i[0]]),i.length>=2&&r.push([[...e,"-line-height"],i[1]])):r.push([e,i.join(", ")]),1}),r}var wn=/^[a-zA-Z0-9-_%/\.]+$/;function at(t){if(t[0]==="container")return null;t=structuredClone(t),t[0]==="animation"&&(t[0]="animate"),t[0]==="aspectRatio"&&(t[0]="aspect"),t[0]==="borderRadius"&&(t[0]="radius"),t[0]==="boxShadow"&&(t[0]="shadow"),t[0]==="colors"&&(t[0]="color"),t[0]==="containers"&&(t[0]="container"),t[0]==="fontFamily"&&(t[0]="font"),t[0]==="fontSize"&&(t[0]="text"),t[0]==="letterSpacing"&&(t[0]="tracking"),t[0]==="lineHeight"&&(t[0]="leading"),t[0]==="maxWidth"&&(t[0]="container"),t[0]==="screens"&&(t[0]="breakpoint"),t[0]==="transitionTimingFunction"&&(t[0]="ease");for(let r of t)if(!wn.test(r))return null;return t.map((r,i,e)=>r==="1"&&i!==e.length-1?"":r).map(r=>r.replaceAll(".","_").replace(/([a-z])([A-Z])/g,(i,e,n)=>`${e}-${n.toLowerCase()}`)).filter((r,i)=>r!=="DEFAULT"||i!==t.length-1).join("-")}function kn(t){return typeof t=="number"||typeof t=="string"}function bn(t){if(!Array.isArray(t)||t.length!==2||typeof t[0]!="string"&&typeof t[0]!="number"||t[1]===void 0||t[1]===null||typeof t[1]!="object")return!1;for(let r of Reflect.ownKeys(t[1]))if(typeof r!="string"||typeof t[1][r]!="string"&&typeof t[1][r]!="number")return!1;return!0}function Or(t,r=[],i){for(let e of Reflect.ownKeys(t)){let n=t[e];if(n==null)continue;let s=[...r,e],a=i(n,s)??0;if(a!==1){if(a===2)return 2;if(!(!Array.isArray(n)&&typeof n!="object")&&Or(n,s,i)===2)return 2}}}function st(t){let r=[];for(let i of K(t,".")){if(!i.includes("[")){r.push(i);continue}let e=0;for(;;){let n=i.indexOf("[",e),s=i.indexOf("]",n);if(n===-1||s===-1)break;n>e&&r.push(i.slice(e,n)),r.push(i.slice(n+1,s)),e=s+1}e<=i.length-1&&r.push(i.slice(e))}return r}function Ee(t){if(Object.prototype.toString.call(t)!=="[object Object]")return!1;let r=Object.getPrototypeOf(t);return r===null||Object.getPrototypeOf(r)===null}function je(t,r,i,e=[]){for(let n of r)if(n!=null)for(let s of Reflect.ownKeys(n)){e.push(s);let a=i(t[s],n[s],e);a!==void 0?t[s]=a:!Ee(t[s])||!Ee(n[s])?t[s]=n[s]:t[s]=je({},[t[s],n[s]],i,e),e.pop()}return t}function ut(t,r,i){return function(n,s){let a=n.lastIndexOf("/"),p=null;a!==-1&&(p=n.slice(a+1).trim(),n=n.slice(0,a).trim());let u=(()=>{let c=st(n),[m,g]=yn(t.theme,c),h=i(Pr(r()??{},c)??null);if(typeof h=="string"&&(h=h.replace("<alpha-value>","1")),typeof m!="object")return typeof g!="object"&&g&4?h??m:m;if(h!==null&&typeof h=="object"&&!Array.isArray(h)){let w=je({},[h],(v,A)=>A);if(m===null&&Object.hasOwn(h,"__CSS_VALUES__")){let v={};for(let A in h.__CSS_VALUES__)v[A]=h[A],delete w[A];m=v}for(let v in m)v!=="__CSS_VALUES__"&&(h?.__CSS_VALUES__?.[v]&4&&Pr(w,v.split("-"))!==void 0||(w[we(v)]=m[v]));return w}if(Array.isArray(m)&&Array.isArray(g)&&Array.isArray(h)){let w=m[0],v=m[1];g[0]&4&&(w=h[0]??w);for(let A of Object.keys(v))g[1][A]&4&&(v[A]=h[1][A]??v[A]);return[w,v]}return m??h})();return p&&typeof u=="string"&&(u=G(u,p)),u??s}}function yn(t,r){if(r.length===1&&r[0].startsWith("--"))return[t.get([r[0]]),t.getOptions(r[0])];let i=at(r),e=new Map,n=new B(()=>new Map),s=t.namespace(`--${i}`);if(s.size===0)return[null,0];let a=new Map;for(let[m,g]of s){if(!m||!m.includes("--")){e.set(m,g),a.set(m,t.getOptions(m?`--${i}-${m}`:`--${i}`));continue}let h=m.indexOf("--"),w=m.slice(0,h),v=m.slice(h+2);v=v.replace(/-([a-z])/g,(A,b)=>b.toUpperCase()),n.get(w===""?null:w).set(v,[g,t.getOptions(`--${i}${m}`)])}let p=t.getOptions(`--${i}`);for(let[m,g]of n){let h=e.get(m);if(typeof h!="string")continue;let w={},v={};for(let[A,[b,y]]of g)w[A]=b,v[A]=y;e.set(m,[h,w]),a.set(m,[p,v])}let u={},c={};for(let[m,g]of e)_r(u,[m??"DEFAULT"],g);for(let[m,g]of a)_r(c,[m??"DEFAULT"],g);return r[r.length-1]==="DEFAULT"?[u?.DEFAULT??null,c.DEFAULT??0]:"DEFAULT"in u&&Object.keys(u).length===1?[u.DEFAULT,c.DEFAULT??0]:(u.__CSS_VALUES__=c,[u,c])}function Pr(t,r){for(let i=0;i<r.length;++i){let e=r[i];if(t?.[e]===void 0){if(r[i+1]===void 0)return;r[i+1]=`${e}-${r[i+1]}`;continue}t=t[e]}return t}function _r(t,r,i){for(let e of r.slice(0,-1))t[e]===void 0&&(t[e]={}),t=t[e];t[r[r.length-1]]=i}function xn(t){return{kind:"combinator",value:t}}function An(t,r){return{kind:"function",value:t,nodes:r}}function Ie(t){return{kind:"selector",value:t}}function Cn(t){return{kind:"separator",value:t}}function Nn(t){return{kind:"value",value:t}}function ze(t,r,i=null){for(let e=0;e<t.length;e++){let n=t[e],s=!1,a=0,p=r(n,{parent:i,replaceWith(u){s||(s=!0,Array.isArray(u)?u.length===0?(t.splice(e,1),a=0):u.length===1?(t[e]=u[0],a=1):(t.splice(e,1,...u),a=u.length):(t[e]=u,a=1))}})??0;if(s){p===0?e--:e+=a-1;continue}if(p===2)return 2;if(p!==1&&n.kind==="function"&&ze(n.nodes,r,n)===2)return 2}}function Fe(t){let r="";for(let i of t)switch(i.kind){case"combinator":case"selector":case"separator":case"value":{r+=i.value;break}case"function":r+=i.value+"("+Fe(i.nodes)+")"}return r}var Kr=92,$n=93,Ur=41,Vn=58,Dr=44,Sn=34,Tn=46,jr=62,Ir=10,En=35,zr=91,Fr=40,Lr=43,Rn=39,Mr=32,Wr=9,Br=126;function ct(t){t=t.replaceAll(`\r
`,`
`);let r=[],i=[],e=null,n="",s;for(let a=0;a<t.length;a++){let p=t.charCodeAt(a);switch(p){case Dr:case jr:case Ir:case Mr:case Lr:case Wr:case Br:{if(n.length>0){let h=Ie(n);e?e.nodes.push(h):r.push(h),n=""}let u=a,c=a+1;for(;c<t.length&&(s=t.charCodeAt(c),!(s!==Dr&&s!==jr&&s!==Ir&&s!==Mr&&s!==Lr&&s!==Wr&&s!==Br));c++);a=c-1;let m=t.slice(u,c),g=m.trim()===","?Cn(m):xn(m);e?e.nodes.push(g):r.push(g);break}case Fr:{let u=An(n,[]);if(n="",u.value!==":not"&&u.value!==":where"&&u.value!==":has"&&u.value!==":is"){let c=a+1,m=0;for(let h=a+1;h<t.length;h++){if(s=t.charCodeAt(h),s===Fr){m++;continue}if(s===Ur){if(m===0){a=h;break}m--}}let g=a;u.nodes.push(Nn(t.slice(c,g))),n="",a=g,e?e.nodes.push(u):r.push(u);break}e?e.nodes.push(u):r.push(u),i.push(u),e=u;break}case Ur:{let u=i.pop();if(n.length>0){let c=Ie(n);u.nodes.push(c),n=""}i.length>0?e=i[i.length-1]:e=null;break}case Tn:case Vn:case En:{if(n.length>0){let u=Ie(n);e?e.nodes.push(u):r.push(u)}n=String.fromCharCode(p);break}case zr:{if(n.length>0){let m=Ie(n);e?e.nodes.push(m):r.push(m)}n="";let u=a,c=0;for(let m=a+1;m<t.length;m++){if(s=t.charCodeAt(m),s===zr){c++;continue}if(s===$n){if(c===0){a=m;break}c--}}n+=t.slice(u,a+1);break}case Rn:case Sn:{let u=a;for(let c=a+1;c<t.length;c++)if(s=t.charCodeAt(c),s===Kr)c+=1;else if(s===p){a=c;break}n+=t.slice(u,a+1);break}case Kr:{let u=t.charCodeAt(a+1);n+=String.fromCharCode(p)+String.fromCharCode(u),a+=1;break}default:n+=String.fromCharCode(p)}}return n.length>0&&r.push(Ie(n)),r}var qr=/^[a-z@][a-zA-Z0-9/%._-]*$/;function Kt({designSystem:t,ast:r,resolvedConfig:i,featuresRef:e,referenceMode:n}){let s={addBase(a){if(n)return;let p=ae(a);e.current|=$e(p,t),r.push(I("@layer","base",p))},addVariant(a,p){if(!lt.test(a))throw new Error(`\`addVariant('${a}')\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);typeof p=="string"||Array.isArray(p)?t.variants.static(a,u=>{u.nodes=Hr(p,u.nodes)},{compounds:xe(typeof p=="string"?[p]:p)}):typeof p=="object"&&t.variants.fromAst(a,ae(p))},matchVariant(a,p,u){function c(g,h,w){let v=p(g,{modifier:h?.value??null});return Hr(v,w)}let m=Object.keys(u?.values??{});t.variants.group(()=>{t.variants.functional(a,(g,h)=>{if(!h.value){if(u?.values&&"DEFAULT"in u.values){g.nodes=c(u.values.DEFAULT,h.modifier,g.nodes);return}return null}if(h.value.kind==="arbitrary")g.nodes=c(h.value.value,h.modifier,g.nodes);else if(h.value.kind==="named"&&u?.values){let w=u.values[h.value.value];if(typeof w!="string")return;g.nodes=c(w,h.modifier,g.nodes)}})},(g,h)=>{if(g.kind!=="functional"||h.kind!=="functional")return 0;let w=g.value?g.value.value:"DEFAULT",v=h.value?h.value.value:"DEFAULT",A=u?.values?.[w]??w,b=u?.values?.[v]??v;if(u&&typeof u.sort=="function")return u.sort({value:A,modifier:g.modifier?.value??null},{value:b,modifier:h.modifier?.value??null});let y=m.indexOf(w),V=m.indexOf(v);return y=y===-1?m.length:y,V=V===-1?m.length:V,y!==V?y-V:A<b?-1:1})},addUtilities(a){a=Array.isArray(a)?a:[a];let p=a.flatMap(c=>Object.entries(c));p=p.flatMap(([c,m])=>K(c,",").map(g=>[g.trim(),m]));let u=new B(()=>[]);for(let[c,m]of p){if(c.startsWith("@keyframes ")){n||r.push(W(c,ae(m)));continue}let g=ct(c),h=!1;if(ze(g,w=>{if(w.kind==="selector"&&w.value[0]==="."&&qr.test(w.value.slice(1))){let v=w.value;w.value="&";let A=Fe(g),b=v.slice(1),y=A==="&"?ae(m):[W(A,ae(m))];u.get(b).push(...y),h=!0,w.value=v;return}if(w.kind==="function"&&w.value===":not")return 1}),!h)throw new Error(`\`addUtilities({ '${c}' : \u2026 })\` defines an invalid utility selector. Utilities must be a single class name and start with a lowercase letter, eg. \`.scrollbar-none\`.`)}for(let[c,m]of u)t.theme.prefix&&z(m,g=>{if(g.kind==="rule"){let h=ct(g.selector);ze(h,w=>{w.kind==="selector"&&w.value[0]==="."&&(w.value=`.${t.theme.prefix}\\:${w.value.slice(1)}`)}),g.selector=Fe(h)}}),t.utilities.static(c,g=>{let h=structuredClone(m);return Gr(h,c,g.raw),e.current|=De(h,t),h})},matchUtilities(a,p){let u=p?.type?Array.isArray(p?.type)?p.type:[p.type]:["any"];for(let[m,g]of Object.entries(a)){let h=function({negative:w}){return v=>{if(v.value?.kind==="arbitrary"&&u.length>0&&!u.includes("any")&&(v.value.dataType&&!u.includes(v.value.dataType)||!v.value.dataType&&!q(v.value.value,u)))return;let A=u.includes("color"),b=null,y=!1;{let O=p?.values??{};A&&(O=Object.assign({inherit:"inherit",transparent:"transparent",current:"currentcolor"},O)),v.value?v.value.kind==="arbitrary"?b=v.value.value:v.value.fraction&&O[v.value.fraction]?(b=O[v.value.fraction],y=!0):O[v.value.value]?b=O[v.value.value]:O.__BARE_VALUE__&&(b=O.__BARE_VALUE__(v.value)??null,y=(v.value.fraction!==null&&b?.includes("/"))??!1):b=O.DEFAULT??null}if(b===null)return;let V;{let O=p?.modifiers??null;v.modifier?O==="any"||v.modifier.kind==="arbitrary"?V=v.modifier.value:O?.[v.modifier.value]?V=O[v.modifier.value]:A&&!Number.isNaN(Number(v.modifier.value))?V=`${v.modifier.value}%`:V=null:V=null}if(v.modifier&&V===null&&!y)return v.value?.kind==="arbitrary"?null:void 0;A&&V!==null&&(b=G(b,V)),w&&(b=`calc(${b} * -1)`);let T=ae(g(b,{modifier:V}));return Gr(T,m,v.raw),e.current|=De(T,t),T}};var c=h;if(!qr.test(m))throw new Error(`\`matchUtilities({ '${m}' : \u2026 })\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter, eg. \`scrollbar\`.`);p?.supportsNegativeValues&&t.utilities.functional(`-${m}`,h({negative:!0}),{types:u}),t.utilities.functional(m,h({negative:!1}),{types:u}),t.utilities.suggest(m,()=>{let w=p?.values??{},v=new Set(Object.keys(w));v.delete("__BARE_VALUE__"),v.has("DEFAULT")&&(v.delete("DEFAULT"),v.add(null));let A=p?.modifiers??{},b=A==="any"?[]:Object.keys(A);return[{supportsNegative:p?.supportsNegativeValues??!1,values:Array.from(v),modifiers:b}]})}},addComponents(a,p){this.addUtilities(a,p)},matchComponents(a,p){this.matchUtilities(a,p)},theme:ut(t,()=>i.theme??{},a=>a),prefix(a){return a},config(a,p){let u=i;if(!a)return u;let c=st(a);for(let m=0;m<c.length;++m){let g=c[m];if(u[g]===void 0)return p;u=u[g]}return u??p}};return s.addComponents=s.addComponents.bind(s),s.matchComponents=s.matchComponents.bind(s),s}function ae(t){let r=[];t=Array.isArray(t)?t:[t];let i=t.flatMap(e=>Object.entries(e));for(let[e,n]of i)if(typeof n!="object"){if(!e.startsWith("--")){if(n==="@slot"){r.push(W(e,[I("@slot")]));continue}e=e.replace(/([A-Z])/g,"-$1").toLowerCase()}r.push(l(e,String(n)))}else if(Array.isArray(n))for(let s of n)typeof s=="string"?r.push(l(e,s)):r.push(W(e,ae(s)));else n!==null&&r.push(W(e,ae(n)));return r}function Hr(t,r){return(typeof t=="string"?[t]:t).flatMap(e=>{if(e.trim().endsWith("}")){let n=e.replace("}","{@slot}}"),s=ve(n);return Ot(s,r),s}else return W(e,r)})}function Gr(t,r,i){z(t,e=>{if(e.kind==="rule"){let n=ct(e.selector);ze(n,s=>{s.kind==="selector"&&s.value===`.${r}`&&(s.value=`.${fe(i)}`)}),e.selector=Fe(n)}})}function Yr(t,r,i){for(let e of Pn(r))t.theme.addKeyframes(e)}function Pn(t){let r=[];if("keyframes"in t.theme)for(let[i,e]of Object.entries(t.theme.keyframes))r.push(I("@keyframes",i,ae(e)));return r}var ft={inherit:"inherit",current:"currentcolor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"oklch(98.4% 0.003 247.858)",100:"oklch(96.8% 0.007 247.896)",200:"oklch(92.9% 0.013 255.508)",300:"oklch(86.9% 0.022 252.894)",400:"oklch(70.4% 0.04 256.788)",500:"oklch(55.4% 0.046 257.417)",600:"oklch(44.6% 0.043 257.281)",700:"oklch(37.2% 0.044 257.287)",800:"oklch(27.9% 0.041 260.031)",900:"oklch(20.8% 0.042 265.755)",950:"oklch(12.9% 0.042 264.695)"},gray:{50:"oklch(98.5% 0.002 247.839)",100:"oklch(96.7% 0.003 264.542)",200:"oklch(92.8% 0.006 264.531)",300:"oklch(87.2% 0.01 258.338)",400:"oklch(70.7% 0.022 261.325)",500:"oklch(55.1% 0.027 264.364)",600:"oklch(44.6% 0.03 256.802)",700:"oklch(37.3% 0.034 259.733)",800:"oklch(27.8% 0.033 256.848)",900:"oklch(21% 0.034 264.665)",950:"oklch(13% 0.028 261.692)"},zinc:{50:"oklch(98.5% 0 0)",100:"oklch(96.7% 0.001 286.375)",200:"oklch(92% 0.004 286.32)",300:"oklch(87.1% 0.006 286.286)",400:"oklch(70.5% 0.015 286.067)",500:"oklch(55.2% 0.016 285.938)",600:"oklch(44.2% 0.017 285.786)",700:"oklch(37% 0.013 285.805)",800:"oklch(27.4% 0.006 286.033)",900:"oklch(21% 0.006 285.885)",950:"oklch(14.1% 0.005 285.823)"},neutral:{50:"oklch(98.5% 0 0)",100:"oklch(97% 0 0)",200:"oklch(92.2% 0 0)",300:"oklch(87% 0 0)",400:"oklch(70.8% 0 0)",500:"oklch(55.6% 0 0)",600:"oklch(43.9% 0 0)",700:"oklch(37.1% 0 0)",800:"oklch(26.9% 0 0)",900:"oklch(20.5% 0 0)",950:"oklch(14.5% 0 0)"},stone:{50:"oklch(98.5% 0.001 106.423)",100:"oklch(97% 0.001 106.424)",200:"oklch(92.3% 0.003 48.717)",300:"oklch(86.9% 0.005 56.366)",400:"oklch(70.9% 0.01 56.259)",500:"oklch(55.3% 0.013 58.071)",600:"oklch(44.4% 0.011 73.639)",700:"oklch(37.4% 0.01 67.558)",800:"oklch(26.8% 0.007 34.298)",900:"oklch(21.6% 0.006 56.043)",950:"oklch(14.7% 0.004 49.25)"},red:{50:"oklch(97.1% 0.013 17.38)",100:"oklch(93.6% 0.032 17.717)",200:"oklch(88.5% 0.062 18.334)",300:"oklch(80.8% 0.114 19.571)",400:"oklch(70.4% 0.191 22.216)",500:"oklch(63.7% 0.237 25.331)",600:"oklch(57.7% 0.245 27.325)",700:"oklch(50.5% 0.213 27.518)",800:"oklch(44.4% 0.177 26.899)",900:"oklch(39.6% 0.141 25.723)",950:"oklch(25.8% 0.092 26.042)"},orange:{50:"oklch(98% 0.016 73.684)",100:"oklch(95.4% 0.038 75.164)",200:"oklch(90.1% 0.076 70.697)",300:"oklch(83.7% 0.128 66.29)",400:"oklch(75% 0.183 55.934)",500:"oklch(70.5% 0.213 47.604)",600:"oklch(64.6% 0.222 41.116)",700:"oklch(55.3% 0.195 38.402)",800:"oklch(47% 0.157 37.304)",900:"oklch(40.8% 0.123 38.172)",950:"oklch(26.6% 0.079 36.259)"},amber:{50:"oklch(98.7% 0.022 95.277)",100:"oklch(96.2% 0.059 95.617)",200:"oklch(92.4% 0.12 95.746)",300:"oklch(87.9% 0.169 91.605)",400:"oklch(82.8% 0.189 84.429)",500:"oklch(76.9% 0.188 70.08)",600:"oklch(66.6% 0.179 58.318)",700:"oklch(55.5% 0.163 48.998)",800:"oklch(47.3% 0.137 46.201)",900:"oklch(41.4% 0.112 45.904)",950:"oklch(27.9% 0.077 45.635)"},yellow:{50:"oklch(98.7% 0.026 102.212)",100:"oklch(97.3% 0.071 103.193)",200:"oklch(94.5% 0.129 101.54)",300:"oklch(90.5% 0.182 98.111)",400:"oklch(85.2% 0.199 91.936)",500:"oklch(79.5% 0.184 86.047)",600:"oklch(68.1% 0.162 75.834)",700:"oklch(55.4% 0.135 66.442)",800:"oklch(47.6% 0.114 61.907)",900:"oklch(42.1% 0.095 57.708)",950:"oklch(28.6% 0.066 53.813)"},lime:{50:"oklch(98.6% 0.031 120.757)",100:"oklch(96.7% 0.067 122.328)",200:"oklch(93.8% 0.127 124.321)",300:"oklch(89.7% 0.196 126.665)",400:"oklch(84.1% 0.238 128.85)",500:"oklch(76.8% 0.233 130.85)",600:"oklch(64.8% 0.2 131.684)",700:"oklch(53.2% 0.157 131.589)",800:"oklch(45.3% 0.124 130.933)",900:"oklch(40.5% 0.101 131.063)",950:"oklch(27.4% 0.072 132.109)"},green:{50:"oklch(98.2% 0.018 155.826)",100:"oklch(96.2% 0.044 156.743)",200:"oklch(92.5% 0.084 155.995)",300:"oklch(87.1% 0.15 154.449)",400:"oklch(79.2% 0.209 151.711)",500:"oklch(72.3% 0.219 149.579)",600:"oklch(62.7% 0.194 149.214)",700:"oklch(52.7% 0.154 150.069)",800:"oklch(44.8% 0.119 151.328)",900:"oklch(39.3% 0.095 152.535)",950:"oklch(26.6% 0.065 152.934)"},emerald:{50:"oklch(97.9% 0.021 166.113)",100:"oklch(95% 0.052 163.051)",200:"oklch(90.5% 0.093 164.15)",300:"oklch(84.5% 0.143 164.978)",400:"oklch(76.5% 0.177 163.223)",500:"oklch(69.6% 0.17 162.48)",600:"oklch(59.6% 0.145 163.225)",700:"oklch(50.8% 0.118 165.612)",800:"oklch(43.2% 0.095 166.913)",900:"oklch(37.8% 0.077 168.94)",950:"oklch(26.2% 0.051 172.552)"},teal:{50:"oklch(98.4% 0.014 180.72)",100:"oklch(95.3% 0.051 180.801)",200:"oklch(91% 0.096 180.426)",300:"oklch(85.5% 0.138 181.071)",400:"oklch(77.7% 0.152 181.912)",500:"oklch(70.4% 0.14 182.503)",600:"oklch(60% 0.118 184.704)",700:"oklch(51.1% 0.096 186.391)",800:"oklch(43.7% 0.078 188.216)",900:"oklch(38.6% 0.063 188.416)",950:"oklch(27.7% 0.046 192.524)"},cyan:{50:"oklch(98.4% 0.019 200.873)",100:"oklch(95.6% 0.045 203.388)",200:"oklch(91.7% 0.08 205.041)",300:"oklch(86.5% 0.127 207.078)",400:"oklch(78.9% 0.154 211.53)",500:"oklch(71.5% 0.143 215.221)",600:"oklch(60.9% 0.126 221.723)",700:"oklch(52% 0.105 223.128)",800:"oklch(45% 0.085 224.283)",900:"oklch(39.8% 0.07 227.392)",950:"oklch(30.2% 0.056 229.695)"},sky:{50:"oklch(97.7% 0.013 236.62)",100:"oklch(95.1% 0.026 236.824)",200:"oklch(90.1% 0.058 230.902)",300:"oklch(82.8% 0.111 230.318)",400:"oklch(74.6% 0.16 232.661)",500:"oklch(68.5% 0.169 237.323)",600:"oklch(58.8% 0.158 241.966)",700:"oklch(50% 0.134 242.749)",800:"oklch(44.3% 0.11 240.79)",900:"oklch(39.1% 0.09 240.876)",950:"oklch(29.3% 0.066 243.157)"},blue:{50:"oklch(97% 0.014 254.604)",100:"oklch(93.2% 0.032 255.585)",200:"oklch(88.2% 0.059 254.128)",300:"oklch(80.9% 0.105 251.813)",400:"oklch(70.7% 0.165 254.624)",500:"oklch(62.3% 0.214 259.815)",600:"oklch(54.6% 0.245 262.881)",700:"oklch(48.8% 0.243 264.376)",800:"oklch(42.4% 0.199 265.638)",900:"oklch(37.9% 0.146 265.522)",950:"oklch(28.2% 0.091 267.935)"},indigo:{50:"oklch(96.2% 0.018 272.314)",100:"oklch(93% 0.034 272.788)",200:"oklch(87% 0.065 274.039)",300:"oklch(78.5% 0.115 274.713)",400:"oklch(67.3% 0.182 276.935)",500:"oklch(58.5% 0.233 277.117)",600:"oklch(51.1% 0.262 276.966)",700:"oklch(45.7% 0.24 277.023)",800:"oklch(39.8% 0.195 277.366)",900:"oklch(35.9% 0.144 278.697)",950:"oklch(25.7% 0.09 281.288)"},violet:{50:"oklch(96.9% 0.016 293.756)",100:"oklch(94.3% 0.029 294.588)",200:"oklch(89.4% 0.057 293.283)",300:"oklch(81.1% 0.111 293.571)",400:"oklch(70.2% 0.183 293.541)",500:"oklch(60.6% 0.25 292.717)",600:"oklch(54.1% 0.281 293.009)",700:"oklch(49.1% 0.27 292.581)",800:"oklch(43.2% 0.232 292.759)",900:"oklch(38% 0.189 293.745)",950:"oklch(28.3% 0.141 291.089)"},purple:{50:"oklch(97.7% 0.014 308.299)",100:"oklch(94.6% 0.033 307.174)",200:"oklch(90.2% 0.063 306.703)",300:"oklch(82.7% 0.119 306.383)",400:"oklch(71.4% 0.203 305.504)",500:"oklch(62.7% 0.265 303.9)",600:"oklch(55.8% 0.288 302.321)",700:"oklch(49.6% 0.265 301.924)",800:"oklch(43.8% 0.218 303.724)",900:"oklch(38.1% 0.176 304.987)",950:"oklch(29.1% 0.149 302.717)"},fuchsia:{50:"oklch(97.7% 0.017 320.058)",100:"oklch(95.2% 0.037 318.852)",200:"oklch(90.3% 0.076 319.62)",300:"oklch(83.3% 0.145 321.434)",400:"oklch(74% 0.238 322.16)",500:"oklch(66.7% 0.295 322.15)",600:"oklch(59.1% 0.293 322.896)",700:"oklch(51.8% 0.253 323.949)",800:"oklch(45.2% 0.211 324.591)",900:"oklch(40.1% 0.17 325.612)",950:"oklch(29.3% 0.136 325.661)"},pink:{50:"oklch(97.1% 0.014 343.198)",100:"oklch(94.8% 0.028 342.258)",200:"oklch(89.9% 0.061 343.231)",300:"oklch(82.3% 0.12 346.018)",400:"oklch(71.8% 0.202 349.761)",500:"oklch(65.6% 0.241 354.308)",600:"oklch(59.2% 0.249 0.584)",700:"oklch(52.5% 0.223 3.958)",800:"oklch(45.9% 0.187 3.815)",900:"oklch(40.8% 0.153 2.432)",950:"oklch(28.4% 0.109 3.907)"},rose:{50:"oklch(96.9% 0.015 12.422)",100:"oklch(94.1% 0.03 12.58)",200:"oklch(89.2% 0.058 10.001)",300:"oklch(81% 0.117 11.638)",400:"oklch(71.2% 0.194 13.428)",500:"oklch(64.5% 0.246 16.439)",600:"oklch(58.6% 0.253 17.585)",700:"oklch(51.4% 0.222 16.935)",800:"oklch(45.5% 0.188 13.697)",900:"oklch(41% 0.159 10.272)",950:"oklch(27.1% 0.105 12.094)"}};function Ae(t){return{__BARE_VALUE__:t}}var ne=Ae(t=>{if(E(t.value))return t.value}),ee=Ae(t=>{if(E(t.value))return`${t.value}%`}),ge=Ae(t=>{if(E(t.value))return`${t.value}px`}),Jr=Ae(t=>{if(E(t.value))return`${t.value}ms`}),pt=Ae(t=>{if(E(t.value))return`${t.value}deg`}),_n=Ae(t=>{if(t.fraction===null)return;let[r,i]=K(t.fraction,"/");if(!(!E(r)||!E(i)))return t.fraction}),Qr=Ae(t=>{if(E(Number(t.value)))return`repeat(${t.value}, minmax(0, 1fr))`}),Zr={accentColor:({theme:t})=>t("colors"),animation:{none:"none",spin:"spin 1s linear infinite",ping:"ping 1s cubic-bezier(0, 0, 0.2, 1) infinite",pulse:"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",bounce:"bounce 1s infinite"},aria:{busy:'busy="true"',checked:'checked="true"',disabled:'disabled="true"',expanded:'expanded="true"',hidden:'hidden="true"',pressed:'pressed="true"',readonly:'readonly="true"',required:'required="true"',selected:'selected="true"'},aspectRatio:{auto:"auto",square:"1 / 1",video:"16 / 9",..._n},backdropBlur:({theme:t})=>t("blur"),backdropBrightness:({theme:t})=>({...t("brightness"),...ee}),backdropContrast:({theme:t})=>({...t("contrast"),...ee}),backdropGrayscale:({theme:t})=>({...t("grayscale"),...ee}),backdropHueRotate:({theme:t})=>({...t("hueRotate"),...pt}),backdropInvert:({theme:t})=>({...t("invert"),...ee}),backdropOpacity:({theme:t})=>({...t("opacity"),...ee}),backdropSaturate:({theme:t})=>({...t("saturate"),...ee}),backdropSepia:({theme:t})=>({...t("sepia"),...ee}),backgroundColor:({theme:t})=>t("colors"),backgroundImage:{none:"none","gradient-to-t":"linear-gradient(to top, var(--tw-gradient-stops))","gradient-to-tr":"linear-gradient(to top right, var(--tw-gradient-stops))","gradient-to-r":"linear-gradient(to right, var(--tw-gradient-stops))","gradient-to-br":"linear-gradient(to bottom right, var(--tw-gradient-stops))","gradient-to-b":"linear-gradient(to bottom, var(--tw-gradient-stops))","gradient-to-bl":"linear-gradient(to bottom left, var(--tw-gradient-stops))","gradient-to-l":"linear-gradient(to left, var(--tw-gradient-stops))","gradient-to-tl":"linear-gradient(to top left, var(--tw-gradient-stops))"},backgroundOpacity:({theme:t})=>t("opacity"),backgroundPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},backgroundSize:{auto:"auto",cover:"cover",contain:"contain"},blur:{0:"0",none:"",sm:"4px",DEFAULT:"8px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},borderColor:({theme:t})=>({DEFAULT:"currentcolor",...t("colors")}),borderOpacity:({theme:t})=>t("opacity"),borderRadius:{none:"0px",sm:"0.125rem",DEFAULT:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},borderSpacing:({theme:t})=>t("spacing"),borderWidth:{DEFAULT:"1px",0:"0px",2:"2px",4:"4px",8:"8px",...ge},boxShadow:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",DEFAULT:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","2xl":"0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",none:"none"},boxShadowColor:({theme:t})=>t("colors"),brightness:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",200:"2",...ee},caretColor:({theme:t})=>t("colors"),colors:()=>({...ft}),columns:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12","3xs":"16rem","2xs":"18rem",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",...ne},container:{},content:{none:"none"},contrast:{0:"0",50:".5",75:".75",100:"1",125:"1.25",150:"1.5",200:"2",...ee},cursor:{auto:"auto",default:"default",pointer:"pointer",wait:"wait",text:"text",move:"move",help:"help","not-allowed":"not-allowed",none:"none","context-menu":"context-menu",progress:"progress",cell:"cell",crosshair:"crosshair","vertical-text":"vertical-text",alias:"alias",copy:"copy","no-drop":"no-drop",grab:"grab",grabbing:"grabbing","all-scroll":"all-scroll","col-resize":"col-resize","row-resize":"row-resize","n-resize":"n-resize","e-resize":"e-resize","s-resize":"s-resize","w-resize":"w-resize","ne-resize":"ne-resize","nw-resize":"nw-resize","se-resize":"se-resize","sw-resize":"sw-resize","ew-resize":"ew-resize","ns-resize":"ns-resize","nesw-resize":"nesw-resize","nwse-resize":"nwse-resize","zoom-in":"zoom-in","zoom-out":"zoom-out"},divideColor:({theme:t})=>t("borderColor"),divideOpacity:({theme:t})=>t("borderOpacity"),divideWidth:({theme:t})=>({...t("borderWidth"),...ge}),dropShadow:{sm:"0 1px 1px rgb(0 0 0 / 0.05)",DEFAULT:["0 1px 2px rgb(0 0 0 / 0.1)","0 1px 1px rgb(0 0 0 / 0.06)"],md:["0 4px 3px rgb(0 0 0 / 0.07)","0 2px 2px rgb(0 0 0 / 0.06)"],lg:["0 10px 8px rgb(0 0 0 / 0.04)","0 4px 3px rgb(0 0 0 / 0.1)"],xl:["0 20px 13px rgb(0 0 0 / 0.03)","0 8px 5px rgb(0 0 0 / 0.08)"],"2xl":"0 25px 25px rgb(0 0 0 / 0.15)",none:"0 0 #0000"},fill:({theme:t})=>t("colors"),flex:{1:"1 1 0%",auto:"1 1 auto",initial:"0 1 auto",none:"none"},flexBasis:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",...t("spacing")}),flexGrow:{0:"0",DEFAULT:"1",...ne},flexShrink:{0:"0",DEFAULT:"1",...ne},fontFamily:{sans:["ui-sans-serif","system-ui","sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"','"Noto Color Emoji"'],serif:["ui-serif","Georgia","Cambria",'"Times New Roman"',"Times","serif"],mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas",'"Liberation Mono"','"Courier New"',"monospace"]},fontSize:{xs:["0.75rem",{lineHeight:"1rem"}],sm:["0.875rem",{lineHeight:"1.25rem"}],base:["1rem",{lineHeight:"1.5rem"}],lg:["1.125rem",{lineHeight:"1.75rem"}],xl:["1.25rem",{lineHeight:"1.75rem"}],"2xl":["1.5rem",{lineHeight:"2rem"}],"3xl":["1.875rem",{lineHeight:"2.25rem"}],"4xl":["2.25rem",{lineHeight:"2.5rem"}],"5xl":["3rem",{lineHeight:"1"}],"6xl":["3.75rem",{lineHeight:"1"}],"7xl":["4.5rem",{lineHeight:"1"}],"8xl":["6rem",{lineHeight:"1"}],"9xl":["8rem",{lineHeight:"1"}]},fontWeight:{thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},gap:({theme:t})=>t("spacing"),gradientColorStops:({theme:t})=>t("colors"),gradientColorStopPositions:{"0%":"0%","5%":"5%","10%":"10%","15%":"15%","20%":"20%","25%":"25%","30%":"30%","35%":"35%","40%":"40%","45%":"45%","50%":"50%","55%":"55%","60%":"60%","65%":"65%","70%":"70%","75%":"75%","80%":"80%","85%":"85%","90%":"90%","95%":"95%","100%":"100%",...ee},grayscale:{0:"0",DEFAULT:"100%",...ee},gridAutoColumns:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridAutoRows:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridColumn:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridColumnEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...ne},gridColumnStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...ne},gridRow:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridRowEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...ne},gridRowStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...ne},gridTemplateColumns:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...Qr},gridTemplateRows:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...Qr},height:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),hueRotate:{0:"0deg",15:"15deg",30:"30deg",60:"60deg",90:"90deg",180:"180deg",...pt},inset:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...t("spacing")}),invert:{0:"0",DEFAULT:"100%",...ee},keyframes:{spin:{to:{transform:"rotate(360deg)"}},ping:{"75%, 100%":{transform:"scale(2)",opacity:"0"}},pulse:{"50%":{opacity:".5"}},bounce:{"0%, 100%":{transform:"translateY(-25%)",animationTimingFunction:"cubic-bezier(0.8,0,1,1)"},"50%":{transform:"none",animationTimingFunction:"cubic-bezier(0,0,0.2,1)"}}},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"},lineHeight:{none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2",3:".75rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem"},listStyleType:{none:"none",disc:"disc",decimal:"decimal"},listStyleImage:{none:"none"},margin:({theme:t})=>({auto:"auto",...t("spacing")}),lineClamp:{1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",...ne},maxHeight:({theme:t})=>({none:"none",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),maxWidth:({theme:t})=>({none:"none",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",prose:"65ch",...t("spacing")}),minHeight:({theme:t})=>({full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),minWidth:({theme:t})=>({full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),objectPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},opacity:{0:"0",5:"0.05",10:"0.1",15:"0.15",20:"0.2",25:"0.25",30:"0.3",35:"0.35",40:"0.4",45:"0.45",50:"0.5",55:"0.55",60:"0.6",65:"0.65",70:"0.7",75:"0.75",80:"0.8",85:"0.85",90:"0.9",95:"0.95",100:"1",...ee},order:{first:"-9999",last:"9999",none:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",...ne},outlineColor:({theme:t})=>t("colors"),outlineOffset:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...ge},outlineWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...ge},padding:({theme:t})=>t("spacing"),placeholderColor:({theme:t})=>t("colors"),placeholderOpacity:({theme:t})=>t("opacity"),ringColor:({theme:t})=>({DEFAULT:"currentcolor",...t("colors")}),ringOffsetColor:({theme:t})=>t("colors"),ringOffsetWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...ge},ringOpacity:({theme:t})=>({DEFAULT:"0.5",...t("opacity")}),ringWidth:{DEFAULT:"3px",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...ge},rotate:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",45:"45deg",90:"90deg",180:"180deg",...pt},saturate:{0:"0",50:".5",100:"1",150:"1.5",200:"2",...ee},scale:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",...ee},screens:{sm:"40rem",md:"48rem",lg:"64rem",xl:"80rem","2xl":"96rem"},scrollMargin:({theme:t})=>t("spacing"),scrollPadding:({theme:t})=>t("spacing"),sepia:{0:"0",DEFAULT:"100%",...ee},skew:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",...pt},space:({theme:t})=>t("spacing"),spacing:{px:"1px",0:"0px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},stroke:({theme:t})=>({none:"none",...t("colors")}),strokeWidth:{0:"0",1:"1",2:"2",...ne},supports:{},data:{},textColor:({theme:t})=>t("colors"),textDecorationColor:({theme:t})=>t("colors"),textDecorationThickness:{auto:"auto","from-font":"from-font",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...ge},textIndent:({theme:t})=>t("spacing"),textOpacity:({theme:t})=>t("opacity"),textUnderlineOffset:{auto:"auto",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...ge},transformOrigin:{center:"center",top:"top","top-right":"top right",right:"right","bottom-right":"bottom right",bottom:"bottom","bottom-left":"bottom left",left:"left","top-left":"top left"},transitionDelay:{0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...Jr},transitionDuration:{DEFAULT:"150ms",0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...Jr},transitionProperty:{none:"none",all:"all",DEFAULT:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter",colors:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke",opacity:"opacity",shadow:"box-shadow",transform:"transform"},transitionTimingFunction:{DEFAULT:"cubic-bezier(0.4, 0, 0.2, 1)",linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},translate:({theme:t})=>({"1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...t("spacing")}),size:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),width:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",screen:"100vw",svw:"100svw",lvw:"100lvw",dvw:"100dvw",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),willChange:{auto:"auto",scroll:"scroll-position",contents:"contents",transform:"transform"},zIndex:{auto:"auto",0:"0",10:"10",20:"20",30:"30",40:"40",50:"50",...ne}};function Xr(t){return{theme:{...Zr,colors:({theme:r})=>r("color",{}),extend:{fontSize:({theme:r})=>({...r("text",{})}),boxShadow:({theme:r})=>({...r("shadow",{})}),animation:({theme:r})=>({...r("animate",{})}),aspectRatio:({theme:r})=>({...r("aspect",{})}),borderRadius:({theme:r})=>({...r("radius",{})}),screens:({theme:r})=>({...r("breakpoint",{})}),letterSpacing:({theme:r})=>({...r("tracking",{})}),lineHeight:({theme:r})=>({...r("leading",{})}),transitionDuration:{DEFAULT:t.get(["--default-transition-duration"])??null},transitionTimingFunction:{DEFAULT:t.get(["--default-transition-timing-function"])??null},maxWidth:({theme:r})=>({...r("container",{})})}}}}var Kn={blocklist:[],future:{},prefix:"",important:!1,darkMode:null,theme:{},plugins:[],content:{files:[]}};function Dt(t,r){let i={design:t,configs:[],plugins:[],content:{files:[]},theme:{},extend:{},result:structuredClone(Kn)};for(let n of r)Ut(i,n);for(let n of i.configs)"darkMode"in n&&n.darkMode!==void 0&&(i.result.darkMode=n.darkMode??null),"prefix"in n&&n.prefix!==void 0&&(i.result.prefix=n.prefix??""),"blocklist"in n&&n.blocklist!==void 0&&(i.result.blocklist=n.blocklist??[]),"important"in n&&n.important!==void 0&&(i.result.important=n.important??!1);let e=Dn(i);return{resolvedConfig:{...i.result,content:i.content,theme:i.theme,plugins:i.plugins},replacedThemeKeys:e}}function Un(t,r){if(Array.isArray(t)&&Ee(t[0]))return t.concat(r);if(Array.isArray(r)&&Ee(r[0])&&Ee(t))return[t,...r];if(Array.isArray(r))return r}function Ut(t,{config:r,base:i,path:e,reference:n}){let s=[];for(let u of r.plugins??[])"__isOptionsFunction"in u?s.push({...u(),reference:n}):"handler"in u?s.push({...u,reference:n}):s.push({handler:u,reference:n});if(Array.isArray(r.presets)&&r.presets.length===0)throw new Error("Error in the config file/plugin/preset. An empty preset (`preset: []`) is not currently supported.");for(let u of r.presets??[])Ut(t,{path:e,base:i,config:u,reference:n});for(let u of s)t.plugins.push(u),u.config&&Ut(t,{path:e,base:i,config:u.config,reference:!!u.reference});let a=r.content??[],p=Array.isArray(a)?a:a.files;for(let u of p)t.content.files.push(typeof u=="object"?u:{base:i,pattern:u});t.configs.push(r)}function Dn(t){let r=new Set,i=ut(t.design,()=>t.theme,n),e=Object.assign(i,{theme:i,colors:ft});function n(s){return typeof s=="function"?s(e)??null:s??null}for(let s of t.configs){let a=s.theme??{},p=a.extend??{};for(let u in a)u!=="extend"&&r.add(u);Object.assign(t.theme,a);for(let u in p)t.extend[u]??=[],t.extend[u].push(p[u])}delete t.theme.extend;for(let s in t.extend){let a=[t.theme[s],...t.extend[s]];t.theme[s]=()=>{let p=a.map(n);return je({},p,Un)}}for(let s in t.theme)t.theme[s]=n(t.theme[s]);if(t.theme.screens&&typeof t.theme.screens=="object")for(let s of Object.keys(t.theme.screens)){let a=t.theme.screens[s];a&&typeof a=="object"&&("raw"in a||"max"in a||"min"in a&&(t.theme.screens[s]=a.min))}return r}function ei(t,r){let i=t.theme.container||{};if(typeof i!="object"||i===null)return;let e=jn(i,r);e.length!==0&&r.utilities.static("container",()=>structuredClone(e))}function jn({center:t,padding:r,screens:i},e){let n=[],s=null;if(t&&n.push(l("margin-inline","auto")),(typeof r=="string"||typeof r=="object"&&r!==null&&"DEFAULT"in r)&&n.push(l("padding-inline",typeof r=="string"?r:r.DEFAULT)),typeof i=="object"&&i!==null){s=new Map;let a=Array.from(e.theme.namespace("--breakpoint").entries());if(a.sort((p,u)=>be(p[1],u[1],"asc")),a.length>0){let[p]=a[0];n.push(I("@media",`(width >= --theme(--breakpoint-${p}))`,[l("max-width","none")]))}for(let[p,u]of Object.entries(i)){if(typeof u=="object")if("min"in u)u=u.min;else continue;s.set(p,I("@media",`(width >= ${u})`,[l("max-width",u)]))}}if(typeof r=="object"&&r!==null){let a=Object.entries(r).filter(([p])=>p!=="DEFAULT").map(([p,u])=>[p,e.theme.resolveValue(p,["--breakpoint"]),u]).filter(Boolean);a.sort((p,u)=>be(p[1],u[1],"asc"));for(let[p,,u]of a)if(s&&s.has(p))s.get(p).nodes.push(l("padding-inline",u));else{if(s)continue;n.push(I("@media",`(width >= theme(--breakpoint-${p}))`,[l("padding-inline",u)]))}}if(s)for(let[,a]of s)n.push(a);return n}function ti({addVariant:t,config:r}){let i=r("darkMode",null),[e,n=".dark"]=Array.isArray(i)?i:[i];if(e==="variant"){let s;if(Array.isArray(n)||typeof n=="function"?s=n:typeof n=="string"&&(s=[n]),Array.isArray(s))for(let a of s)a===".dark"?(e=!1,console.warn('When using `variant` for `darkMode`, you must provide a selector.\nExample: `darkMode: ["variant", ".your-selector &"]`')):a.includes("&")||(e=!1,console.warn('When using `variant` for `darkMode`, your selector must contain `&`.\nExample `darkMode: ["variant", ".your-selector &"]`'));n=s}e===null||(e==="selector"?t("dark",`&:where(${n}, ${n} *)`):e==="media"?t("dark","@media (prefers-color-scheme: dark)"):e==="variant"?t("dark",n):e==="class"&&t("dark",`&:is(${n} *)`))}function ri(t){for(let[r,i]of[["t","top"],["tr","top right"],["r","right"],["br","bottom right"],["b","bottom"],["bl","bottom left"],["l","left"],["tl","top left"]])t.utilities.static(`bg-gradient-to-${r}`,()=>[l("--tw-gradient-position",`to ${i} in oklab`),l("background-image","linear-gradient(var(--tw-gradient-stops))")]);t.utilities.static("bg-left-top",()=>[l("background-position","left top")]),t.utilities.static("bg-right-top",()=>[l("background-position","right top")]),t.utilities.static("bg-left-bottom",()=>[l("background-position","left bottom")]),t.utilities.static("bg-right-bottom",()=>[l("background-position","right bottom")]),t.utilities.static("object-left-top",()=>[l("object-position","left top")]),t.utilities.static("object-right-top",()=>[l("object-position","right top")]),t.utilities.static("object-left-bottom",()=>[l("object-position","left bottom")]),t.utilities.static("object-right-bottom",()=>[l("object-position","right bottom")]),t.utilities.functional("max-w-screen",r=>{if(!r.value||r.value.kind==="arbitrary")return;let i=t.theme.resolve(r.value.value,["--breakpoint"]);if(i)return[l("max-width",i)]}),t.utilities.static("overflow-ellipsis",()=>[l("text-overflow","ellipsis")]),t.utilities.static("decoration-slice",()=>[l("-webkit-box-decoration-break","slice"),l("box-decoration-break","slice")]),t.utilities.static("decoration-clone",()=>[l("-webkit-box-decoration-break","clone"),l("box-decoration-break","clone")]),t.utilities.functional("flex-shrink",r=>{if(!r.modifier){if(!r.value)return[l("flex-shrink","1")];if(r.value.kind==="arbitrary")return[l("flex-shrink",r.value.value)];if(E(r.value.value))return[l("flex-shrink",r.value.value)]}}),t.utilities.functional("flex-grow",r=>{if(!r.modifier){if(!r.value)return[l("flex-grow","1")];if(r.value.kind==="arbitrary")return[l("flex-grow",r.value.value)];if(E(r.value.value))return[l("flex-grow",r.value.value)]}})}function ii(t,r){let i=t.theme.screens||{},e=r.variants.get("min")?.order??0,n=[];for(let[a,p]of Object.entries(i)){let h=function(w){r.variants.static(a,v=>{v.nodes=[I("@media",g,v.nodes)]},{order:w})};var s=h;let u=r.variants.get(a),c=r.theme.resolveValue(a,["--breakpoint"]);if(u&&c&&!r.theme.hasDefault(`--breakpoint-${a}`))continue;let m=!0;typeof p=="string"&&(m=!1);let g=In(p);m?n.push(h):h(e)}if(n.length!==0){for(let[,a]of r.variants.variants)a.order>e&&(a.order+=n.length);r.variants.compareFns=new Map(Array.from(r.variants.compareFns).map(([a,p])=>(a>e&&(a+=n.length),[a,p])));for(let[a,p]of n.entries())p(e+a+1)}}function In(t){return(Array.isArray(t)?t:[t]).map(i=>typeof i=="string"?{min:i}:i&&typeof i=="object"?i:null).map(i=>{if(i===null)return null;if("raw"in i)return i.raw;let e="";return i.max!==void 0&&(e+=`${i.max} >= `),e+="width",i.min!==void 0&&(e+=` >= ${i.min}`),`(${e})`}).filter(Boolean).join(", ")}function ni(t,r){let i=t.theme.aria||{},e=t.theme.supports||{},n=t.theme.data||{};if(Object.keys(i).length>0){let s=r.variants.get("aria"),a=s?.applyFn,p=s?.compounds;r.variants.functional("aria",(u,c)=>{let m=c.value;return m&&m.kind==="named"&&m.value in i?a?.(u,{...c,value:{kind:"arbitrary",value:i[m.value]}}):a?.(u,c)},{compounds:p})}if(Object.keys(e).length>0){let s=r.variants.get("supports"),a=s?.applyFn,p=s?.compounds;r.variants.functional("supports",(u,c)=>{let m=c.value;return m&&m.kind==="named"&&m.value in e?a?.(u,{...c,value:{kind:"arbitrary",value:e[m.value]}}):a?.(u,c)},{compounds:p})}if(Object.keys(n).length>0){let s=r.variants.get("data"),a=s?.applyFn,p=s?.compounds;r.variants.functional("data",(u,c)=>{let m=c.value;return m&&m.kind==="named"&&m.value in n?a?.(u,{...c,value:{kind:"arbitrary",value:n[m.value]}}):a?.(u,c)},{compounds:p})}}var zn=/^[a-z]+$/;async function li({designSystem:t,base:r,ast:i,loadModule:e,sources:n}){let s=0,a=[],p=[];z(i,(g,{parent:h,replaceWith:w,context:v})=>{if(g.kind==="at-rule"){if(g.name==="@plugin"){if(h!==null)throw new Error("`@plugin` cannot be nested.");let A=g.params.slice(1,-1);if(A.length===0)throw new Error("`@plugin` must have a path.");let b={};for(let y of g.nodes??[]){if(y.kind!=="declaration")throw new Error(`Unexpected \`@plugin\` option:

${ie([y])}

\`@plugin\` options must be a flat list of declarations.`);if(y.value===void 0)continue;let V=y.value,T=K(V,",").map(O=>{if(O=O.trim(),O==="null")return null;if(O==="true")return!0;if(O==="false")return!1;if(Number.isNaN(Number(O))){if(O[0]==='"'&&O[O.length-1]==='"'||O[0]==="'"&&O[O.length-1]==="'")return O.slice(1,-1);if(O[0]==="{"&&O[O.length-1]==="}")throw new Error(`Unexpected \`@plugin\` option: Value of declaration \`${ie([y]).trim()}\` is not supported.

Using an object as a plugin option is currently only supported in JavaScript configuration files.`)}else return Number(O);return O});b[y.property]=T.length===1?T[0]:T}a.push([{id:A,base:v.base,reference:!!v.reference},Object.keys(b).length>0?b:null]),w([]),s|=4;return}if(g.name==="@config"){if(g.nodes.length>0)throw new Error("`@config` cannot have a body.");if(h!==null)throw new Error("`@config` cannot be nested.");p.push({id:g.params.slice(1,-1),base:v.base,reference:!!v.reference}),w([]),s|=4;return}}}),ri(t);let u=t.resolveThemeValue;if(t.resolveThemeValue=function(h,w){return h.startsWith("--")?u(h,w):(s|=oi({designSystem:t,base:r,ast:i,sources:n,configs:[],pluginDetails:[]}),t.resolveThemeValue(h,w))},!a.length&&!p.length)return 0;let[c,m]=await Promise.all([Promise.all(p.map(async({id:g,base:h,reference:w})=>{let v=await e(g,h,"config");return{path:g,base:v.base,config:v.module,reference:w}})),Promise.all(a.map(async([{id:g,base:h,reference:w},v])=>{let A=await e(g,h,"plugin");return{path:g,base:A.base,plugin:A.module,options:v,reference:w}}))]);return s|=oi({designSystem:t,base:r,ast:i,sources:n,configs:c,pluginDetails:m}),s}function oi({designSystem:t,base:r,ast:i,sources:e,configs:n,pluginDetails:s}){let a=0,u=[...s.map(b=>{if(!b.options)return{config:{plugins:[b.plugin]},base:b.base,reference:b.reference};if("__isOptionsFunction"in b.plugin)return{config:{plugins:[b.plugin(b.options)]},base:b.base,reference:b.reference};throw new Error(`The plugin "${b.path}" does not accept options`)}),...n],{resolvedConfig:c}=Dt(t,[{config:Xr(t.theme),base:r,reference:!0},...u,{config:{plugins:[ti]},base:r,reference:!0}]),{resolvedConfig:m,replacedThemeKeys:g}=Dt(t,u),h=t.resolveThemeValue;t.resolveThemeValue=function(y,V){if(y[0]==="-"&&y[1]==="-")return h(y,V);let T=v.theme(y,void 0);if(Array.isArray(T)&&T.length===2)return T[0];if(Array.isArray(T))return T.join(", ");if(typeof T=="string")return T};let w={designSystem:t,ast:i,resolvedConfig:c,featuresRef:{set current(b){a|=b}}},v=Kt({...w,referenceMode:!1}),A;for(let{handler:b,reference:y}of c.plugins)y?(A||=Kt({...w,referenceMode:!0}),b(A)):b(v);if(Rr(t,m,g),Yr(t,m,g),ni(m,t),ii(m,t),ei(m,t),!t.theme.prefix&&c.prefix){if(c.prefix.endsWith("-")&&(c.prefix=c.prefix.slice(0,-1),console.warn(`The prefix "${c.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only and is written as a variant before all utilities. We have fixed up the prefix for you. Remove the trailing \`-\` to silence this warning.`)),!zn.test(c.prefix))throw new Error(`The prefix "${c.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);t.theme.prefix=c.prefix}if(!t.important&&c.important===!0&&(t.important=!0),typeof c.important=="string"){let b=c.important;z(i,(y,{replaceWith:V,parent:T})=>{if(y.kind==="at-rule"&&!(y.name!=="@tailwind"||y.params!=="utilities"))return T?.kind==="rule"&&T.selector===b?2:(V(L(b,[y])),2)})}for(let b of c.blocklist)t.invalidCandidates.add(b);for(let b of c.content.files){if("raw"in b)throw new Error(`Error in the config file/plugin/preset. The \`content\` key contains a \`raw\` entry:

${JSON.stringify(b,null,2)}

This feature is not currently supported.`);let y=!1;b.pattern[0]=="!"&&(y=!0,b.pattern=b.pattern.slice(1)),e.push({...b,negated:y})}return a}var ai=/^(-?\d+)\.\.(-?\d+)(?:\.\.(-?\d+))?$/;function dt(t){let r=t.indexOf("{");if(r===-1)return[t];let i=[],e=t.slice(0,r),n=t.slice(r),s=0,a=n.lastIndexOf("}");for(let g=0;g<n.length;g++){let h=n[g];if(h==="{")s++;else if(h==="}"&&(s--,s===0)){a=g;break}}if(a===-1)throw new Error(`The pattern \`${t}\` is not balanced.`);let p=n.slice(1,a),u=n.slice(a+1),c;Fn(p)?c=Ln(p):c=K(p,","),c=c.flatMap(g=>dt(g));let m=dt(u);for(let g of m)for(let h of c)i.push(e+h+g);return i}function Fn(t){return ai.test(t)}function Ln(t){let r=t.match(ai);if(!r)return[t];let[,i,e,n]=r,s=n?parseInt(n,10):void 0,a=[];if(/^-?\d+$/.test(i)&&/^-?\d+$/.test(e)){let p=parseInt(i,10),u=parseInt(e,10);if(s===void 0&&(s=p<=u?1:-1),s===0)throw new Error("Step cannot be zero in sequence expansion.");let c=p<u;c&&s<0&&(s=-s),!c&&s>0&&(s=-s);for(let m=p;c?m<=u:m>=u;m+=s)a.push(m.toString())}return a}var Mn=/^[a-z]+$/,Ze=(n=>(n[n.None=0]="None",n[n.AtProperty=1]="AtProperty",n[n.ColorMix=2]="ColorMix",n[n.All=3]="All",n))(Ze||{});function Wn(){throw new Error("No `loadModule` function provided to `compile`")}function Bn(){throw new Error("No `loadStylesheet` function provided to `compile`")}function qn(t){let r=0,i=null;for(let e of K(t," "))e==="reference"?r|=2:e==="inline"?r|=1:e==="default"?r|=4:e==="static"?r|=8:e.startsWith("prefix(")&&e.endsWith(")")&&(i=e.slice(7,-1));return[r,i]}var Ve=(p=>(p[p.None=0]="None",p[p.AtApply=1]="AtApply",p[p.AtImport=2]="AtImport",p[p.JsPluginCompat=4]="JsPluginCompat",p[p.ThemeFunction=8]="ThemeFunction",p[p.Utilities=16]="Utilities",p[p.Variants=32]="Variants",p))(Ve||{});async function si(t,{base:r="",loadModule:i=Wn,loadStylesheet:e=Bn}={}){let n=0;t=[le({base:r},t)],n|=await _t(t,r,e);let s=null,a=new Ge,p=[],u=[],c=null,m=null,g=[],h=[],w=[],v=[],A=null;z(t,(y,{parent:V,replaceWith:T,context:O})=>{if(y.kind==="at-rule"){if(y.name==="@tailwind"&&(y.params==="utilities"||y.params.startsWith("utilities"))){if(m!==null){T([]);return}let _=K(y.params," ");for(let j of _)if(j.startsWith("source(")){let P=j.slice(7,-1);if(P==="none"){A=P;continue}if(P[0]==='"'&&P[P.length-1]!=='"'||P[0]==="'"&&P[P.length-1]!=="'"||P[0]!=="'"&&P[0]!=='"')throw new Error("`source(\u2026)` paths must be quoted.");A={base:O.sourceBase??O.base,pattern:P.slice(1,-1)}}m=y,n|=16}if(y.name==="@utility"){if(V!==null)throw new Error("`@utility` cannot be nested.");if(y.nodes.length===0)throw new Error(`\`@utility ${y.params}\` is empty. Utilities should include at least one property.`);let _=wr(y);if(_===null)throw new Error(`\`@utility ${y.params}\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter.`);u.push(_)}if(y.name==="@source"){if(y.nodes.length>0)throw new Error("`@source` cannot have a body.");if(V!==null)throw new Error("`@source` cannot be nested.");let _=!1,j=!1,P=y.params;if(P[0]==="n"&&P.startsWith("not ")&&(_=!0,P=P.slice(4)),P[0]==="i"&&P.startsWith("inline(")&&(j=!0,P=P.slice(7,-1)),P[0]==='"'&&P[P.length-1]!=='"'||P[0]==="'"&&P[P.length-1]!=="'"||P[0]!=="'"&&P[0]!=='"')throw new Error("`@source` paths must be quoted.");let Y=P.slice(1,-1);if(j){let M=_?v:w,F=K(Y," ");for(let re of F)for(let X of dt(re))M.push(X)}else h.push({base:O.base,pattern:Y,negated:_});T([]);return}if(y.name==="@variant"&&(V===null?y.nodes.length===0?y.name="@custom-variant":(z(y.nodes,_=>{if(_.kind==="at-rule"&&_.name==="@slot")return y.name="@custom-variant",2}),y.name==="@variant"&&g.push(y)):g.push(y)),y.name==="@custom-variant"){if(V!==null)throw new Error("`@custom-variant` cannot be nested.");T([]);let[_,j]=K(y.params," ");if(!lt.test(_))throw new Error(`\`@custom-variant ${_}\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);if(y.nodes.length>0&&j)throw new Error(`\`@custom-variant ${_}\` cannot have both a selector and a body.`);if(y.nodes.length===0){if(!j)throw new Error(`\`@custom-variant ${_}\` has no selector or body.`);let P=K(j.slice(1,-1),",");if(P.length===0||P.some(F=>F.trim()===""))throw new Error(`\`@custom-variant ${_} (${P.join(",")})\` selector is invalid.`);let Y=[],M=[];for(let F of P)F=F.trim(),F[0]==="@"?Y.push(F):M.push(F);p.push(F=>{F.variants.static(_,re=>{let X=[];M.length>0&&X.push(L(M.join(", "),re.nodes));for(let se of Y)X.push(W(se,re.nodes));re.nodes=X},{compounds:xe([...M,...Y])})});return}else{p.push(P=>{P.variants.fromAst(_,y.nodes)});return}}if(y.name==="@media"){let _=K(y.params," "),j=[];for(let P of _)if(P.startsWith("source(")){let Y=P.slice(7,-1);z(y.nodes,(M,{replaceWith:F})=>{if(M.kind==="at-rule"&&M.name==="@tailwind"&&M.params==="utilities")return M.params+=` source(${Y})`,F([le({sourceBase:O.base},[M])]),2})}else if(P.startsWith("theme(")){let Y=P.slice(6,-1),M=Y.includes("reference");z(y.nodes,F=>{if(F.kind!=="at-rule"){if(M)throw new Error('Files imported with `@import "\u2026" theme(reference)` must only contain `@theme` blocks.\nUse `@reference "\u2026";` instead.');return 0}if(F.name==="@theme")return F.params+=" "+Y,1})}else if(P.startsWith("prefix(")){let Y=P.slice(7,-1);z(y.nodes,M=>{if(M.kind==="at-rule"&&M.name==="@theme")return M.params+=` prefix(${Y})`,1})}else P==="important"?s=!0:P==="reference"?y.nodes=[le({reference:!0},y.nodes)]:j.push(P);j.length>0?y.params=j.join(" "):_.length>0&&T(y.nodes)}if(y.name==="@theme"){let[_,j]=qn(y.params);if(O.reference&&(_|=2),j){if(!Mn.test(j))throw new Error(`The prefix "${j}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);a.prefix=j}return z(y.nodes,P=>{if(P.kind==="at-rule"&&P.name==="@keyframes")return a.addKeyframes(P),1;if(P.kind==="comment")return;if(P.kind==="declaration"&&P.property.startsWith("--")){a.add(we(P.property),P.value??"",_);return}let Y=ie([I(y.name,y.params,[P])]).split(`
`).map((M,F,re)=>`${F===0||F>=re.length-2?" ":">"} ${M}`).join(`
`);throw new Error(`\`@theme\` blocks must only contain custom properties or \`@keyframes\`.

${Y}`)}),c?T([]):(c=L(":root, :host",[]),T([c])),1}}});let b=$r(a);if(s&&(b.important=s),v.length>0)for(let y of v)b.invalidCandidates.add(y);n|=await li({designSystem:b,base:r,ast:t,loadModule:i,sources:h});for(let y of p)y(b);for(let y of u)y(b);if(c){let y=[];for(let[T,O]of b.theme.entries())O.options&2||y.push(l(fe(T),O.value));let V=b.theme.getKeyframes();for(let T of V)t.push(le({theme:!0},[D([T])]));c.nodes=[le({theme:!0},y)]}if(m){let y=m;y.kind="context",y.context={}}if(g.length>0){for(let y of g){let V=L("&",y.nodes),T=y.params,O=b.parseVariant(T);if(O===null)throw new Error(`Cannot use \`@variant\` with unknown variant: ${T}`);if(Se(V,O,b.variants)===null)throw new Error(`Cannot use \`@variant\` with variant: ${T}`);Object.assign(y,V)}n|=32}return n|=$e(t,b),n|=De(t,b),z(t,(y,{replaceWith:V})=>{if(y.kind==="at-rule")return y.name==="@utility"&&V([]),1}),{designSystem:b,ast:t,sources:h,root:A,utilitiesNode:m,features:n,inlineCandidates:w}}async function ui(t,r={}){let{designSystem:i,ast:e,sources:n,root:s,utilitiesNode:a,features:p,inlineCandidates:u}=await si(t,r);e.unshift(He(`! tailwindcss v${jt} | MIT License | https://tailwindcss.com `));function c(v){i.invalidCandidates.add(v)}let m=new Set,g=null,h=0,w=!1;for(let v of u)i.invalidCandidates.has(v)||(m.add(v),w=!0);return{sources:n,root:s,features:p,build(v){if(p===0)return t;if(!a)return g??=ke(e,i,r.polyfills),g;let A=w,b=!1;w=!1;let y=m.size;for(let T of v)if(!i.invalidCandidates.has(T))if(T[0]==="-"&&T[1]==="-"){let O=i.theme.markUsedVariable(T);A||=O,b||=O}else m.add(T),A||=m.size!==y;if(!A)return g??=ke(e,i,r.polyfills),g;let V=me(m,i,{onInvalidCandidate:c}).astNodes;return!b&&h===V.length?(g??=ke(e,i,r.polyfills),g):(h=V.length,a.nodes=V,g=ke(e,i,r.polyfills),g)}}}async function Hn(t,r={}){let i=ve(t),e=await ui(i,r),n=i,s=t;return{...e,build(a){let p=e.build(a);return p===n||(s=ie(p),n=p),s}}}async function Gn(t,r={}){return(await si(ve(t),r)).designSystem}function Le(){throw new Error("It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.")}for(let t in mt)t!=="default"&&(Le[t]=mt[t]);module.exports=Le;
