x64:
  firstOrDefaultFilePatterns:
    - '!**/node_modules'
    - '!build{,/**/*}'
    - '!release{,/**/*}'
    - out/**/*
    - package.json
    - resources/**/*
    - '!**/.vscode/*'
    - '!src/*'
    - '!node_modules/*'
    - '!dist/*'
    - '!dist_old/*'
    - '!build-output/*'
    - '!Olllll/*'
    - '!public/*'
    - '!encrypted-assets/*'
    - '!scripts/*'
    - '!electron.vite.config.{js,ts,mjs,cjs}'
    - '!{.eslintcache,eslint.config.mjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
    - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
    - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
    - '!tailwind.config.js'
    - '!**/*.zip'
    - '!**/*.rar'
    - '!**/*.stackdump'
    - '!nul'
    - '!**/*.{iml,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,suo,xproj,cc,d.ts,mk,a,o,forge-meta,pdb}'
    - '!**/._*'
    - '!**/electron-builder.{yaml,yml,json,json5,toml,ts}'
    - '!**/{.git,.hg,.svn,CVS,RCS,SCCS,__pycache__,.DS_Store,thumbs.db,.gitignore,.gitkeep,.gitattributes,.npmignore,.idea,.vs,.flowconfig,.jshintrc,.eslintrc,.circleci,.yarn-integrity,.yarn-metadata.json,yarn-error.log,yarn.lock,package-lock.json,npm-debug.log,appveyor.yml,.travis.yml,circle.yml,.nyc_output,.husky,.github,electron-builder.env}'
    - '!.yarn{,/**/*}'
    - '!.editorconfig'
    - '!.yarnrc.yml'
  nodeModuleFilePatterns:
    - '**/*'
    - out/**/*
    - package.json
    - resources/**/*
    - '!**/.vscode/*'
    - '!src/*'
    - '!node_modules/*'
    - '!dist/*'
    - '!dist_old/*'
    - '!build-output/*'
    - '!Olllll/*'
    - '!public/*'
    - '!encrypted-assets/*'
    - '!scripts/*'
    - '!electron.vite.config.{js,ts,mjs,cjs}'
    - '!{.eslintcache,eslint.config.mjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
    - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
    - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
    - '!tailwind.config.js'
    - '!**/*.zip'
    - '!**/*.rar'
    - '!**/*.stackdump'
    - '!nul'
nsis:
  script: |-
    !include "F:\Timeless\v1-keno\display\node_modules\.pnpm\app-builder-lib@25.1.8_dmg-_1bb375569cbd8d6bc505988dd6df2f0f\node_modules\app-builder-lib\templates\nsis\include\StdUtils.nsh"
    !addincludedir "F:\Timeless\v1-keno\display\node_modules\.pnpm\app-builder-lib@25.1.8_dmg-_1bb375569cbd8d6bc505988dd6df2f0f\node_modules\app-builder-lib\templates\nsis\include"
    !macro _isUpdated _a _b _t _f
      ${StdUtils.TestParameter} $R9 "updated"
      StrCmp "$R9" "true" `${_t}` `${_f}`
    !macroend
    !define isUpdated `"" isUpdated ""`

    !macro _isForceRun _a _b _t _f
      ${StdUtils.TestParameter} $R9 "force-run"
      StrCmp "$R9" "true" `${_t}` `${_f}`
    !macroend
    !define isForceRun `"" isForceRun ""`

    !macro _isKeepShortcuts _a _b _t _f
      ${StdUtils.TestParameter} $R9 "keep-shortcuts"
      StrCmp "$R9" "true" `${_t}` `${_f}`
    !macroend
    !define isKeepShortcuts `"" isKeepShortcuts ""`

    !macro _isNoDesktopShortcut _a _b _t _f
      ${StdUtils.TestParameter} $R9 "no-desktop-shortcut"
      StrCmp "$R9" "true" `${_t}` `${_f}`
    !macroend
    !define isNoDesktopShortcut `"" isNoDesktopShortcut ""`

    !macro _isDeleteAppData _a _b _t _f
      ${StdUtils.TestParameter} $R9 "delete-app-data"
      StrCmp "$R9" "true" `${_t}` `${_f}`
    !macroend
    !define isDeleteAppData `"" isDeleteAppData ""`

    !macro _isForAllUsers _a _b _t _f
      ${StdUtils.TestParameter} $R9 "allusers"
      StrCmp "$R9" "true" `${_t}` `${_f}`
    !macroend
    !define isForAllUsers `"" isForAllUsers ""`

    !macro _isForCurrentUser _a _b _t _f
      ${StdUtils.TestParameter} $R9 "currentuser"
      StrCmp "$R9" "true" `${_t}` `${_f}`
    !macroend
    !define isForCurrentUser `"" isForCurrentUser ""`

    !macro addLangs
      !insertmacro MUI_LANGUAGE "English"
      !insertmacro MUI_LANGUAGE "German"
      !insertmacro MUI_LANGUAGE "French"
      !insertmacro MUI_LANGUAGE "SpanishInternational"
      !insertmacro MUI_LANGUAGE "SimpChinese"
      !insertmacro MUI_LANGUAGE "TradChinese"
      !insertmacro MUI_LANGUAGE "Japanese"
      !insertmacro MUI_LANGUAGE "Korean"
      !insertmacro MUI_LANGUAGE "Italian"
      !insertmacro MUI_LANGUAGE "Dutch"
      !insertmacro MUI_LANGUAGE "Danish"
      !insertmacro MUI_LANGUAGE "Swedish"
      !insertmacro MUI_LANGUAGE "Norwegian"
      !insertmacro MUI_LANGUAGE "Finnish"
      !insertmacro MUI_LANGUAGE "Russian"
      !insertmacro MUI_LANGUAGE "Portuguese"
      !insertmacro MUI_LANGUAGE "PortugueseBR"
      !insertmacro MUI_LANGUAGE "Polish"
      !insertmacro MUI_LANGUAGE "Ukrainian"
      !insertmacro MUI_LANGUAGE "Czech"
      !insertmacro MUI_LANGUAGE "Slovak"
      !insertmacro MUI_LANGUAGE "Hungarian"
      !insertmacro MUI_LANGUAGE "Arabic"
      !insertmacro MUI_LANGUAGE "Turkish"
      !insertmacro MUI_LANGUAGE "Thai"
      !insertmacro MUI_LANGUAGE "Vietnamese"
    !macroend

    !addplugindir /x86-unicode "C:\Users\<USER>\AppData\Local\electron-builder\Cache\nsis\nsis-resources-3.4.1\plugins\x86-unicode"
    !include "C:\Users\<USER>\AppData\Local\Temp\t-ab21R9\0-messages.nsh"
    !macro customFiles_x64
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\1.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\1.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\10.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\10.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\11.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\11.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\12.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\12.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\13.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\13.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\14.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\14.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\15.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\15.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\16.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\16.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\17.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\17.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\18.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\18.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\19.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\19.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\2.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\2.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\20.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\20.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\21.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\21.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\22.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\22.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\23.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\23.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\24.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\24.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\25.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\25.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\26.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\26.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\27.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\27.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\28.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\28.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\29.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\29.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\3.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\3.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\30.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\30.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\31.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\31.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\32.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\32.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\33.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\33.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\34.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\34.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\35.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\35.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\36.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\36.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\37.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\37.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\38.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\38.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\39.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\39.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\4.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\4.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\40.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\40.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\41.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\41.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\42.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\42.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\43.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\43.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\44.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\44.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\45.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\45.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\46.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\46.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\47.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\47.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\48.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\48.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\49.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\49.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\5.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\5.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\50.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\50.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\51.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\51.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\52.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\52.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\53.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\53.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\54.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\54.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\55.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\55.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\56.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\56.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\57.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\57.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\58.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\58.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\59.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\59.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\6.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\6.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\60.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\60.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\61.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\61.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\62.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\62.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\63.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\63.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\64.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\64.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\65.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\65.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\66.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\66.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\67.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\67.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\68.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\68.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\69.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\69.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\7.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\7.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\70.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\70.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\71.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\71.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\72.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\72.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\73.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\73.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\74.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\74.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\75.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\75.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\76.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\76.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\77.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\77.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\78.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\78.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\79.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\79.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\8.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\8.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\80.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\80.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\9.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\9.mp4"
      File "/oname=$INSTDIR\resources\app\out\renderer\videos\z4.mp4" "F:\Timeless\v1-keno\display\release\win-unpacked\resources\app\out\renderer\videos\z4.mp4"
    !macroend

    Var newStartMenuLink
    Var oldStartMenuLink
    Var newDesktopLink
    Var oldDesktopLink
    Var oldShortcutName
    Var oldMenuDirectory

    !include "common.nsh"
    !include "MUI2.nsh"
    !include "multiUser.nsh"
    !include "allowOnlyOneInstallerInstance.nsh"

    !ifdef INSTALL_MODE_PER_ALL_USERS
      !ifdef BUILD_UNINSTALLER
        RequestExecutionLevel user
      !else
        RequestExecutionLevel admin
      !endif
    !else
      RequestExecutionLevel user
    !endif

    !ifdef BUILD_UNINSTALLER
      SilentInstall silent
    !else
      Var appExe
      Var launchLink
    !endif

    !ifdef ONE_CLICK
      !include "oneClick.nsh"
    !else
      !include "assistedInstaller.nsh"
    !endif

    !insertmacro addLangs

    !ifmacrodef customHeader
      !insertmacro customHeader
    !endif

    Function .onInit
      Call setInstallSectionSpaceRequired

      SetOutPath $INSTDIR
      ${LogSet} on

      !ifmacrodef preInit
        !insertmacro preInit
      !endif

      !ifdef DISPLAY_LANG_SELECTOR
        !insertmacro MUI_LANGDLL_DISPLAY
      !endif

      !ifdef BUILD_UNINSTALLER
        WriteUninstaller "${UNINSTALLER_OUT_FILE}"
        !insertmacro quitSuccess
      !else
        !insertmacro check64BitAndSetRegView

        !ifdef ONE_CLICK
          !insertmacro ALLOW_ONLY_ONE_INSTALLER_INSTANCE
        !else
          ${IfNot} ${UAC_IsInnerInstance}
            !insertmacro ALLOW_ONLY_ONE_INSTALLER_INSTANCE
          ${EndIf}
        !endif

        !insertmacro initMultiUser

        !ifmacrodef customInit
          !insertmacro customInit
        !endif

        !ifmacrodef addLicenseFiles
          InitPluginsDir
          !insertmacro addLicenseFiles
        !endif
      !endif
    FunctionEnd

    !ifndef BUILD_UNINSTALLER
      !include "installUtil.nsh"
    !endif

    Section "install" INSTALL_SECTION_ID
      !ifndef BUILD_UNINSTALLER
        # If we're running a silent upgrade of a per-machine installation, elevate so extracting the new app will succeed.
        # For a non-silent install, the elevation will be triggered when the install mode is selected in the UI,
        # but that won't be executed when silent.
        !ifndef INSTALL_MODE_PER_ALL_USERS
          !ifndef ONE_CLICK
              ${if} $hasPerMachineInstallation == "1" # set in onInit by initMultiUser
              ${andIf} ${Silent}
                ${ifNot} ${UAC_IsAdmin}
                  ShowWindow $HWNDPARENT ${SW_HIDE}
                  !insertmacro UAC_RunElevated
                  ${Switch} $0
                    ${Case} 0
                      ${Break}
                    ${Case} 1223 ;user aborted
                      ${Break}
                    ${Default}
                      MessageBox mb_IconStop|mb_TopMost|mb_SetForeground "Unable to elevate, error $0"
                      ${Break}
                  ${EndSwitch}
                  Quit
                ${else}
                  !insertmacro setInstallModePerAllUsers
                ${endIf}
              ${endIf}
          !endif
        !endif
        !include "installSection.nsh"
      !endif
    SectionEnd

    Function setInstallSectionSpaceRequired
      !insertmacro setSpaceRequired ${INSTALL_SECTION_ID}
    FunctionEnd

    !ifdef BUILD_UNINSTALLER
      !include "uninstaller.nsh"
    !endif
