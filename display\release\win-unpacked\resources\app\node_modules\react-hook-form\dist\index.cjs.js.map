{"version": 3, "file": "index.cjs.js", "sources": ["../src/utils/isCheckBoxInput.ts", "../src/utils/isDateObject.ts", "../src/utils/isNullOrUndefined.ts", "../src/utils/isObject.ts", "../src/logic/getEventValue.ts", "../src/logic/isNameInFieldArray.ts", "../src/logic/getNodeParentName.ts", "../src/utils/isWeb.ts", "../src/utils/cloneObject.ts", "../src/utils/isPlainObject.ts", "../src/utils/compact.ts", "../src/utils/isUndefined.ts", "../src/utils/get.ts", "../src/utils/isBoolean.ts", "../src/utils/isKey.ts", "../src/utils/stringToPath.ts", "../src/utils/set.ts", "../src/constants.ts", "../src/useFormContext.tsx", "../src/logic/getProxyFormState.ts", "../src/utils/isPrimitive.ts", "../src/utils/deepEqual.ts", "../src/useDeepEqualEffect.ts", "../src/useFormState.ts", "../src/utils/isString.ts", "../src/logic/generateWatchOutput.ts", "../src/useWatch.ts", "../src/useController.ts", "../src/controller.tsx", "../src/utils/flatten.ts", "../src/form.tsx", "../src/logic/appendErrors.ts", "../src/utils/convertToArrayPayload.ts", "../src/utils/createSubject.ts", "../src/utils/isEmptyObject.ts", "../src/utils/isFileInput.ts", "../src/utils/isFunction.ts", "../src/utils/isHTMLElement.ts", "../src/utils/isMultipleSelect.ts", "../src/utils/isRadioInput.ts", "../src/utils/live.ts", "../src/utils/unset.ts", "../src/utils/objectHasFunction.ts", "../src/logic/getDirtyFields.ts", "../src/logic/getCheckboxValue.ts", "../src/logic/getFieldValueAs.ts", "../src/logic/getRadioValue.ts", "../src/logic/getFieldValue.ts", "../src/logic/getResolverOptions.ts", "../src/utils/isRegex.ts", "../src/logic/getRuleValue.ts", "../src/logic/getValidationModes.ts", "../src/logic/hasPromiseValidation.ts", "../src/logic/isWatched.ts", "../src/logic/iterateFieldsByAction.ts", "../src/logic/schemaErrorLookup.ts", "../src/logic/shouldRenderFormState.ts", "../src/logic/updateFieldArrayRootError.ts", "../src/utils/isMessage.ts", "../src/logic/getValidateError.ts", "../src/logic/getValueAndMessage.ts", "../src/logic/validateField.ts", "../src/logic/createFormControl.ts", "../src/logic/hasValidation.ts", "../src/logic/skipValidation.ts", "../src/logic/shouldSubscribeByName.ts", "../src/utils/isRadioOrCheckbox.ts", "../src/logic/unsetEmptyArray.ts", "../src/logic/generateId.ts", "../src/logic/getFocusFieldName.ts", "../src/utils/append.ts", "../src/utils/fillEmptyArray.ts", "../src/utils/insert.ts", "../src/utils/move.ts", "../src/utils/prepend.ts", "../src/utils/remove.ts", "../src/utils/swap.ts", "../src/utils/update.ts", "../src/useForm.ts", "../src/useFieldArray.ts"], "sourcesContent": ["import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'checkbox';\n", "export default (value: unknown): value is Date => value instanceof Date;\n", "export default (value: unknown): value is null | undefined => value == null;\n", "import isDateObject from './isDateObject';\nimport isNullOrUndefined from './isNullOrUndefined';\n\nexport const isObjectType = (value: unknown): value is object =>\n  typeof value === 'object';\n\nexport default <T extends object>(value: unknown): value is T =>\n  !isNullOrUndefined(value) &&\n  !Array.isArray(value) &&\n  isObjectType(value) &&\n  !isDateObject(value);\n", "import isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isObject from '../utils/isObject';\n\ntype Event = { target: any };\n\nexport default (event: unknown) =>\n  isObject(event) && (event as Event).target\n    ? isCheckBoxInput((event as Event).target)\n      ? (event as Event).target.checked\n      : (event as Event).target.value\n    : event;\n", "import { InternalFieldName } from '../types';\n\nimport getNodeParentName from './getNodeParentName';\n\nexport default (names: Set<InternalFieldName>, name: InternalFieldName) =>\n  names.has(getNodeParentName(name));\n", "export default (name: string) =>\n  name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n", "export default typeof window !== 'undefined' &&\n  typeof window.HTMLElement !== 'undefined' &&\n  typeof document !== 'undefined';\n", "import isObject from './isObject';\nimport isPlainObject from './isPlainObject';\nimport isWeb from './isWeb';\n\nexport default function cloneObject<T>(data: T): T {\n  let copy: any;\n  const isArray = Array.isArray(data);\n  const isFileListInstance =\n    typeof FileList !== 'undefined' ? data instanceof FileList : false;\n\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (data instanceof Set) {\n    copy = new Set(data);\n  } else if (\n    !(isWeb && (data instanceof Blob || isFileListInstance)) &&\n    (isArray || isObject(data))\n  ) {\n    copy = isArray ? [] : {};\n\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n\n  return copy;\n}\n", "import isObject from './isObject';\n\nexport default (tempObject: object) => {\n  const prototypeCopy =\n    tempObject.constructor && tempObject.constructor.prototype;\n\n  return (\n    isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf')\n  );\n};\n", "export default <TValue>(value: TValue[]) =>\n  Array.isArray(value) ? value.filter(Boolean) : [];\n", "export default (val: unknown): val is undefined => val === undefined;\n", "import compact from './compact';\nimport isNullOrUndefined from './isNullOrUndefined';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\n\nexport default <T>(\n  object: T,\n  path?: string | null,\n  defaultValue?: unknown,\n): any => {\n  if (!path || !isObject(object)) {\n    return defaultValue;\n  }\n\n  const result = compact(path.split(/[,[\\].]+?/)).reduce(\n    (result, key) =>\n      isNullOrUndefined(result) ? result : result[key as keyof {}],\n    object,\n  );\n\n  return isUndefined(result) || result === object\n    ? isUndefined(object[path as keyof T])\n      ? defaultValue\n      : object[path as keyof T]\n    : result;\n};\n", "export default (value: unknown): value is boolean => typeof value === 'boolean';\n", "export default (value: string) => /^\\w*$/.test(value);\n", "import compact from './compact';\n\nexport default (input: string): string[] =>\n  compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n", "import { FieldPath, FieldValues } from '../types';\n\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport stringToPath from './stringToPath';\n\nexport default (\n  object: FieldValues,\n  path: FieldPath<FieldValues>,\n  value?: unknown,\n) => {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue =\n        isObject(objValue) || Array.isArray(objValue)\n          ? objValue\n          : !isNaN(+tempPath[index + 1])\n            ? []\n            : {};\n    }\n\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return;\n    }\n\n    object[key] = newValue;\n    object = object[key];\n  }\n};\n", "export const EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change',\n} as const;\n\nexport const VALIDATION_MODE = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all',\n} as const;\n\nexport const INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate',\n} as const;\n", "import React from 'react';\n\nimport { FieldValues, FormProviderProps, UseFormReturn } from './types';\n\nconst HookFormContext = React.createContext<UseFormReturn | null>(null);\n\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const useFormContext = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(): UseFormReturn<TFieldValues, TContext, TTransformedValues> =>\n  React.useContext(HookFormContext) as UseFormReturn<\n    TFieldValues,\n    TContext,\n    TTransformedValues\n  >;\n\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const FormProvider = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: FormProviderProps<TFieldValues, TContext, TTransformedValues>,\n) => {\n  const { children, ...data } = props;\n  return (\n    <HookFormContext.Provider value={data as unknown as UseFormReturn}>\n      {children}\n    </HookFormContext.Provider>\n  );\n};\n", "import { VALIDATION_MODE } from '../constants';\nimport { Control, FieldValues, FormState, ReadFormState } from '../types';\n\nexport default <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  formState: FormState<TFieldValues>,\n  control: Control<TFieldValues, TContext, TTransformedValues>,\n  localProxyFormState?: ReadFormState,\n  isRoot = true,\n) => {\n  const result = {\n    defaultValues: control._defaultValues,\n  } as typeof formState;\n\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key as keyof FormState<TFieldValues> & keyof ReadFormState;\n\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      },\n    });\n  }\n\n  return result;\n};\n", "import { Primitive } from '../types';\n\nimport isNullOrUndefined from './isNullOrUndefined';\nimport { isObjectType } from './isObject';\n\nexport default (value: unknown): value is Primitive =>\n  isNullOrUndefined(value) || !isObjectType(value);\n", "import isObject from '../utils/isObject';\n\nimport isDateObject from './isDateObject';\nimport isPrimitive from './isPrimitive';\n\nexport default function deepEqual(object1: any, object2: any) {\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n\n  for (const key of keys1) {\n    const val1 = object1[key];\n\n    if (!keys2.includes(key)) {\n      return false;\n    }\n\n    if (key !== 'ref') {\n      const val2 = object2[key];\n\n      if (\n        (isDateObject(val1) && isDateObject(val2)) ||\n        (isObject(val1) && isObject(val2)) ||\n        (Array.isArray(val1) && Array.isArray(val2))\n          ? !deepEqual(val1, val2)\n          : val1 !== val2\n      ) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n", "import * as React from 'react';\n\nimport deepEqual from './utils/deepEqual';\n\nexport const useDeepEqualEffect = <T extends React.DependencyList>(\n  effect: React.EffectCallback,\n  deps: T,\n) => {\n  const ref = React.useRef<T>(deps);\n\n  if (!deepEqual(deps, ref.current)) {\n    ref.current = deps;\n  }\n\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  React.useEffect(effect, ref.current);\n};\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport {\n  FieldValues,\n  InternalFieldName,\n  UseFormStateProps,\n  UseFormStateReturn,\n} from './types';\nimport { useDeepEqualEffect } from './useDeepEqualEffect';\nimport { useFormContext } from './useFormContext';\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFormState<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n>(\n  props?: UseFormStateProps<TFieldValues, TTransformedValues>,\n): UseFormStateReturn<TFieldValues> {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const { control = methods.control, disabled, name, exact } = props || {};\n  const [formState, updateFormState] = React.useState(control._formState);\n  const _localProxyFormState = React.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    validatingFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  });\n\n  useDeepEqualEffect(\n    () =>\n      control._subscribe({\n        name: name as InternalFieldName,\n        formState: _localProxyFormState.current,\n        exact,\n        callback: (formState) => {\n          !disabled &&\n            updateFormState({\n              ...control._formState,\n              ...formState,\n            });\n        },\n      }),\n    [name, disabled, exact],\n  );\n\n  React.useEffect(() => {\n    _localProxyFormState.current.isValid && control._setValid(true);\n  }, [control]);\n\n  return React.useMemo(\n    () =>\n      getProxyFormState(\n        formState,\n        control,\n        _localProxyFormState.current,\n        false,\n      ),\n    [formState, control],\n  );\n}\n", "export default (value: unknown): value is string => typeof value === 'string';\n", "import { DeepPartial, FieldValues, Names } from '../types';\nimport get from '../utils/get';\nimport isString from '../utils/isString';\n\nexport default <T>(\n  names: string | string[] | undefined,\n  _names: Names,\n  formValues?: FieldValues,\n  isGlobal?: boolean,\n  defaultValue?: DeepPartial<T> | unknown,\n) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n\n  if (Array.isArray(names)) {\n    return names.map(\n      (fieldName) => (\n        isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)\n      ),\n    );\n  }\n\n  isGlobal && (_names.watchAll = true);\n\n  return formValues;\n};\n", "import React from 'react';\n\nimport generateWatchOutput from './logic/generateWatchOutput';\nimport {\n  Control,\n  DeepPartialSkipArrayKey,\n  FieldPath,\n  FieldPathValue,\n  FieldPathValues,\n  FieldValues,\n  InternalFieldName,\n  UseWatchProps,\n} from './types';\nimport { useDeepEqualEffect } from './useDeepEqualEffect';\nimport { useFormContext } from './useFormContext';\n\n/**\n * Subscribe to the entire form values change and re-render at the hook level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   defaultValue: {\n *     name: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n>(props: {\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: \"fieldA\",\n *   defaultValue: \"default value\",\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(props: {\n  name: TFieldName;\n  defaultValue?: FieldPathValue<TFieldValues, TFieldName>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValue<TFieldValues, TFieldName>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: [\"fieldA\", \"fieldB\"],\n *   defaultValue: {\n *     fieldA: \"data\",\n *     fieldB: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldNames extends\n    readonly FieldPath<TFieldValues>[] = readonly FieldPath<TFieldValues>[],\n  TTransformedValues = TFieldValues,\n>(props: {\n  name: readonly [...TFieldNames];\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValues<TFieldValues, TFieldNames>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * // can skip passing down the control into useWatch if the form is wrapped with the FormProvider\n * const values = useWatch()\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n>(): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nexport function useWatch<TFieldValues extends FieldValues>(\n  props?: UseWatchProps<TFieldValues>,\n) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact,\n  } = props || {};\n\n  const [value, updateValue] = React.useState(\n    control._getWatch(\n      name as InternalFieldName,\n      defaultValue as DeepPartialSkipArrayKey<TFieldValues>,\n    ),\n  );\n\n  useDeepEqualEffect(\n    () =>\n      control._subscribe({\n        name: name as InternalFieldName,\n        formState: {\n          values: true,\n        },\n        exact,\n        callback: (formState) =>\n          !disabled &&\n          updateValue(\n            generateWatchOutput(\n              name as InternalFieldName | InternalFieldName[],\n              control._names,\n              formState.values || control._formValues,\n              false,\n              defaultValue,\n            ),\n          ),\n      }),\n    [name, defaultValue, disabled, exact],\n  );\n\n  React.useEffect(() => control._removeUnmounted());\n\n  return value;\n}\n", "import React from 'react';\n\nimport getEventValue from './logic/getEventValue';\nimport isNameInFieldArray from './logic/isNameInFieldArray';\nimport cloneObject from './utils/cloneObject';\nimport get from './utils/get';\nimport isBoolean from './utils/isBoolean';\nimport isUndefined from './utils/isUndefined';\nimport set from './utils/set';\nimport { EVENTS } from './constants';\nimport {\n  ControllerFieldState,\n  Field,\n  FieldPath,\n  FieldPathValue,\n  FieldValues,\n  InternalFieldName,\n  UseControllerProps,\n  UseControllerReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useFormState } from './useFormState';\nimport { useWatch } from './useWatch';\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nexport function useController<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseControllerProps<TFieldValues, TName, TTransformedValues>,\n): UseControllerReturn<TFieldValues, TName> {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const { name, disabled, control = methods.control, shouldUnregister } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: get(\n      control._formValues,\n      name,\n      get(control._defaultValues, name, props.defaultValue),\n    ),\n    exact: true,\n  }) as FieldPathValue<TFieldValues, TName>;\n  const formState = useFormState({\n    control,\n    name,\n    exact: true,\n  });\n\n  const _props = React.useRef(props);\n  const _registerProps = React.useRef(\n    control.register(name, {\n      ...props.rules,\n      value,\n      ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }),\n  );\n\n  const fieldState = React.useMemo(\n    () =>\n      Object.defineProperties(\n        {},\n        {\n          invalid: {\n            enumerable: true,\n            get: () => !!get(formState.errors, name),\n          },\n          isDirty: {\n            enumerable: true,\n            get: () => !!get(formState.dirtyFields, name),\n          },\n          isTouched: {\n            enumerable: true,\n            get: () => !!get(formState.touchedFields, name),\n          },\n          isValidating: {\n            enumerable: true,\n            get: () => !!get(formState.validatingFields, name),\n          },\n          error: {\n            enumerable: true,\n            get: () => get(formState.errors, name),\n          },\n        },\n      ) as ControllerFieldState,\n    [formState, name],\n  );\n\n  const onChange = React.useCallback(\n    (event: any) =>\n      _registerProps.current.onChange({\n        target: {\n          value: getEventValue(event),\n          name: name as InternalFieldName,\n        },\n        type: EVENTS.CHANGE,\n      }),\n    [name],\n  );\n\n  const onBlur = React.useCallback(\n    () =>\n      _registerProps.current.onBlur({\n        target: {\n          value: get(control._formValues, name),\n          name: name as InternalFieldName,\n        },\n        type: EVENTS.BLUR,\n      }),\n    [name, control._formValues],\n  );\n\n  const ref = React.useCallback(\n    (elm: any) => {\n      const field = get(control._fields, name);\n\n      if (field && elm) {\n        field._f.ref = {\n          focus: () => elm.focus(),\n          select: () => elm.select(),\n          setCustomValidity: (message: string) =>\n            elm.setCustomValidity(message),\n          reportValidity: () => elm.reportValidity(),\n        };\n      }\n    },\n    [control._fields, name],\n  );\n\n  const field = React.useMemo(\n    () => ({\n      name,\n      value,\n      ...(isBoolean(disabled) || formState.disabled\n        ? { disabled: formState.disabled || disabled }\n        : {}),\n      onChange,\n      onBlur,\n      ref,\n    }),\n    [name, disabled, formState.disabled, onChange, onBlur, ref, value],\n  );\n\n  React.useEffect(() => {\n    const _shouldUnregisterField =\n      control._options.shouldUnregister || shouldUnregister;\n\n    control.register(name, {\n      ..._props.current.rules,\n      ...(isBoolean(_props.current.disabled)\n        ? { disabled: _props.current.disabled }\n        : {}),\n    });\n\n    const updateMounted = (name: InternalFieldName, value: boolean) => {\n      const field: Field = get(control._fields, name);\n\n      if (field && field._f) {\n        field._f.mount = value;\n      }\n    };\n\n    updateMounted(name, true);\n\n    if (_shouldUnregisterField) {\n      const value = cloneObject(get(control._options.defaultValues, name));\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n\n    !isArrayField && control.register(name);\n\n    return () => {\n      (\n        isArrayField\n          ? _shouldUnregisterField && !control._state.action\n          : _shouldUnregisterField\n      )\n        ? control.unregister(name)\n        : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n\n  React.useEffect(() => {\n    control._setDisabledField({\n      disabled,\n      name,\n    });\n  }, [disabled, name, control]);\n\n  return React.useMemo(\n    () => ({\n      field,\n      formState,\n      fieldState,\n    }),\n    [field, formState, fieldState],\n  );\n}\n", "import { ControllerProps, FieldPath, FieldValues } from './types';\nimport { useController } from './useController';\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(\n  props: ControllerProps<TFieldValues, TName, TTransformedValues>,\n) =>\n  props.render(useController<TFieldValues, TName, TTransformedValues>(props));\n\nexport { Controller };\n", "import { FieldValues } from '../types';\n\nimport { isObjectType } from './isObject';\n\nexport const flatten = (obj: FieldValues) => {\n  const output: FieldValues = {};\n\n  for (const key of Object.keys(obj)) {\n    if (isObjectType(obj[key]) && obj[key] !== null) {\n      const nested = flatten(obj[key]);\n\n      for (const nestedKey of Object.keys(nested)) {\n        output[`${key}.${nestedKey}`] = nested[nestedKey];\n      }\n    } else {\n      output[key] = obj[key];\n    }\n  }\n\n  return output;\n};\n", "import React from 'react';\n\nimport { flatten } from './utils/flatten';\nimport { FieldValues, FormProps } from './types';\nimport { useFormContext } from './useFormContext';\n\nconst POST_REQUEST = 'post';\n\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form<\n  TFieldValues extends FieldValues,\n  TTransformedValues = TFieldValues,\n>(props: FormProps<TFieldValues, TTransformedValues>) {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const [mounted, setMounted] = React.useState(false);\n  const {\n    control = methods.control,\n    onSubmit,\n    children,\n    action,\n    method = POST_REQUEST,\n    headers,\n    encType,\n    onError,\n    render,\n    onSuccess,\n    validateStatus,\n    ...rest\n  } = props;\n\n  const submit = async (event?: React.BaseSyntheticEvent) => {\n    let hasError = false;\n    let type = '';\n\n    await control.handleSubmit(async (data) => {\n      const formData = new FormData();\n      let formDataJson = '';\n\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch {}\n\n      const flattenFormValues = flatten(control._formValues);\n\n      for (const key in flattenFormValues) {\n        formData.append(key, flattenFormValues[key]);\n      }\n\n      if (onSubmit) {\n        await onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson,\n        });\n      }\n\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [\n            headers && headers['Content-Type'],\n            encType,\n          ].some((value) => value && value.includes('json'));\n\n          const response = await fetch(String(action), {\n            method,\n            headers: {\n              ...headers,\n              ...(encType ? { 'Content-Type': encType } : {}),\n            },\n            body: shouldStringifySubmissionData ? formDataJson : formData,\n          });\n\n          if (\n            response &&\n            (validateStatus\n              ? !validateStatus(response.status)\n              : response.status < 200 || response.status >= 300)\n          ) {\n            hasError = true;\n            onError && onError({ response });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({ response });\n          }\n        } catch (error: unknown) {\n          hasError = true;\n          onError && onError({ error });\n        }\n      }\n    })(event);\n\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false,\n      });\n      props.control.setError('root.server', {\n        type,\n      });\n    }\n  };\n\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return render ? (\n    <>\n      {render({\n        submit,\n      })}\n    </>\n  ) : (\n    <form\n      noValidate={mounted}\n      action={action}\n      method={method}\n      encType={encType}\n      onSubmit={submit}\n      {...rest}\n    >\n      {children}\n    </form>\n  );\n}\n\nexport { Form };\n", "import {\n  InternalFieldErrors,\n  InternalFieldName,\n  ValidateResult,\n} from '../types';\n\nexport default (\n  name: InternalFieldName,\n  validateAllFieldCriteria: boolean,\n  errors: InternalFieldErrors,\n  type: string,\n  message: ValidateResult,\n) =>\n  validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n          ...(errors[name] && errors[name]!.types ? errors[name]!.types : {}),\n          [type]: message || true,\n        },\n      }\n    : {};\n", "export default <T>(value: T) => (Array.isArray(value) ? value : [value]);\n", "import { Noop } from '../types';\n\nexport type Observer<T> = {\n  next: (value: T) => void;\n};\n\nexport type Subscription = {\n  unsubscribe: Noop;\n};\n\nexport type Subject<T> = {\n  readonly observers: Observer<T>[];\n  subscribe: (value: Observer<T>) => Subscription;\n  unsubscribe: Noop;\n} & Observer<T>;\n\nexport default <T>(): Subject<T> => {\n  let _observers: Observer<T>[] = [];\n\n  const next = (value: T) => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n\n  const subscribe = (observer: Observer<T>): Subscription => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter((o) => o !== observer);\n      },\n    };\n  };\n\n  const unsubscribe = () => {\n    _observers = [];\n  };\n\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe,\n  };\n};\n", "import { EmptyObject } from '../types';\n\nimport isObject from './isObject';\n\nexport default (value: unknown): value is EmptyObject =>\n  isObject(value) && !Object.keys(value).length;\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'file';\n", "export default (value: unknown): value is Function =>\n  typeof value === 'function';\n", "import isWeb from './isWeb';\n\nexport default (value: unknown): value is HTMLElement => {\n  if (!isWeb) {\n    return false;\n  }\n\n  const owner = value ? ((value as HTMLElement).ownerDocument as Document) : 0;\n  return (\n    value instanceof\n    (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement)\n  );\n};\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLSelectElement =>\n  element.type === `select-multiple`;\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'radio';\n", "import { Ref } from '../types';\n\nimport isHTMLElement from './isHTMLElement';\n\nexport default (ref: Ref) => isHTMLElement(ref) && ref.isConnected;\n", "import isEmptyObject from './isEmptyObject';\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nfunction baseGet(object: any, updatePath: (string | number)[]) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n\n  return object;\n}\n\nfunction isEmptyArray(obj: unknown[]) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport default function unset(object: any, path: string | (string | number)[]) {\n  const paths = Array.isArray(path)\n    ? path\n    : isKey(path)\n      ? [path]\n      : stringToPath(path);\n\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n\n  const index = paths.length - 1;\n  const key = paths[index];\n\n  if (childObject) {\n    delete childObject[key];\n  }\n\n  if (\n    index !== 0 &&\n    ((isObject(childObject) && isEmptyObject(childObject)) ||\n      (Array.isArray(childObject) && isEmptyArray(childObject)))\n  ) {\n    unset(object, paths.slice(0, -1));\n  }\n\n  return object;\n}\n", "import isFunction from './isFunction';\n\nexport default <T>(data: T): boolean => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\n", "import deepEqual from '../utils/deepEqual';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isPrimitive from '../utils/isPrimitive';\nimport isUndefined from '../utils/isUndefined';\nimport objectHasFunction from '../utils/objectHasFunction';\n\nfunction markFieldsDirty<T>(data: T, fields: Record<string, any> = {}) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        fields[key] = Array.isArray(data[key]) ? [] : {};\n        markFieldsDirty(data[key], fields[key]);\n      } else if (!isNullOrUndefined(data[key])) {\n        fields[key] = true;\n      }\n    }\n  }\n\n  return fields;\n}\n\nfunction getDirtyFieldsFromDefaultValues<T>(\n  data: T,\n  formValues: T,\n  dirtyFieldsFromValues: Record<\n    Extract<keyof T, string>,\n    ReturnType<typeof markFieldsDirty> | boolean\n  >,\n) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        if (\n          isUndefined(formValues) ||\n          isPrimitive(dirtyFieldsFromValues[key])\n        ) {\n          dirtyFieldsFromValues[key] = Array.isArray(data[key])\n            ? markFieldsDirty(data[key], [])\n            : { ...markFieldsDirty(data[key]) };\n        } else {\n          getDirtyFieldsFromDefaultValues(\n            data[key],\n            isNullOrUndefined(formValues) ? {} : formValues[key],\n            dirtyFieldsFromValues[key],\n          );\n        }\n      } else {\n        dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n      }\n    }\n  }\n\n  return dirtyFieldsFromValues;\n}\n\nexport default <T>(defaultValues: T, formValues: T) =>\n  getDirtyFieldsFromDefaultValues(\n    defaultValues,\n    formValues,\n    markFieldsDirty(formValues),\n  );\n", "import isUndefined from '../utils/isUndefined';\n\ntype CheckboxFieldResult = {\n  isValid: boolean;\n  value: string | string[] | boolean | undefined;\n};\n\nconst defaultResult: CheckboxFieldResult = {\n  value: false,\n  isValid: false,\n};\n\nconst validResult = { value: true, isValid: true };\n\nexport default (options?: HTMLInputElement[]): CheckboxFieldResult => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options\n        .filter((option) => option && option.checked && !option.disabled)\n        .map((option) => option.value);\n      return { value: values, isValid: !!values.length };\n    }\n\n    return options[0].checked && !options[0].disabled\n      ? // @ts-expect-error expected to work in the browser\n        options[0].attributes && !isUndefined(options[0].attributes.value)\n        ? isUndefined(options[0].value) || options[0].value === ''\n          ? validResult\n          : { value: options[0].value, isValid: true }\n        : validResult\n      : defaultResult;\n  }\n\n  return defaultResult;\n};\n", "import { Field, NativeFieldValue } from '../types';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends NativeFieldValue>(\n  value: T,\n  { valueAsNumber, valueAsDate, setValueAs }: Field['_f'],\n) =>\n  isUndefined(value)\n    ? value\n    : valueAsNumber\n      ? value === ''\n        ? NaN\n        : value\n          ? +value\n          : value\n      : valueAsDate && isString(value)\n        ? new Date(value)\n        : setValueAs\n          ? setValueAs(value)\n          : value;\n", "type RadioFieldResult = {\n  isValid: boolean;\n  value: number | string | null;\n};\n\nconst defaultReturn: RadioFieldResult = {\n  isValid: false,\n  value: null,\n};\n\nexport default (options?: HTMLInputElement[]): RadioFieldResult =>\n  Array.isArray(options)\n    ? options.reduce(\n        (previous, option): RadioFieldResult =>\n          option && option.checked && !option.disabled\n            ? {\n                isValid: true,\n                value: option.value,\n              }\n            : previous,\n        defaultReturn,\n      )\n    : defaultReturn;\n", "import { Field } from '../types';\nimport isCheckBox from '../utils/isCheckBoxInput';\nimport isFileInput from '../utils/isFileInput';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isRadioInput from '../utils/isRadioInput';\nimport isUndefined from '../utils/isUndefined';\n\nimport getCheckboxValue from './getCheckboxValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getRadioValue from './getRadioValue';\n\nexport default function getFieldValue(_f: Field['_f']) {\n  const ref = _f.ref;\n\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(({ value }) => value);\n  }\n\n  if (isCheckBox(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n", "import {\n  CriteriaMode,\n  Field,\n  FieldName,\n  FieldRefs,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport { get } from '../utils';\nimport set from '../utils/set';\n\nexport default <TFieldValues extends FieldValues>(\n  fieldsNames: Set<InternalFieldName> | InternalFieldName[],\n  _fields: FieldRefs,\n  criteriaMode?: CriteriaMode,\n  shouldUseNativeValidation?: boolean | undefined,\n) => {\n  const fields: Record<InternalFieldName, Field['_f']> = {};\n\n  for (const name of fieldsNames) {\n    const field: Field = get(_fields, name);\n\n    field && set(fields, name, field._f);\n  }\n\n  return {\n    criteriaMode,\n    names: [...fieldsNames] as FieldName<TFieldValues>[],\n    fields,\n    shouldUseNativeValidation,\n  };\n};\n", "export default (value: unknown): value is RegExp => value instanceof RegExp;\n", "import {\n  ValidationRule,\n  ValidationValue,\n  ValidationValueMessage,\n} from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends ValidationValue>(\n  rule?: ValidationRule<T> | ValidationValueMessage<T>,\n) =>\n  isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n      ? rule.source\n      : isObject(rule)\n        ? isRegex(rule.value)\n          ? rule.value.source\n          : rule.value\n        : rule;\n", "import { VALIDATION_MODE } from '../constants';\nimport { Mode, ValidationModeFlags } from '../types';\n\nexport default (mode?: Mode): ValidationModeFlags => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n", "import { Field, Validate } from '../types';\nimport isFunction from '../utils/isFunction';\nimport isObject from '../utils/isObject';\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\n\nexport default (fieldReference: Field['_f']) =>\n  !!fieldReference &&\n  !!fieldReference.validate &&\n  !!(\n    (isFunction(fieldReference.validate) &&\n      fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n    (isObject(fieldReference.validate) &&\n      Object.values(fieldReference.validate).find(\n        (validateFunction: Validate<unknown, unknown>) =>\n          validateFunction.constructor.name === ASYNC_FUNCTION,\n      ))\n  );\n", "import { InternalFieldName, Names } from '../types';\n\nexport default (\n  name: InternalFieldName,\n  _names: Names,\n  isBlurEvent?: boolean,\n) =>\n  !isBlurEvent &&\n  (_names.watchAll ||\n    _names.watch.has(name) ||\n    [..._names.watch].some(\n      (watchName) =>\n        name.startsWith(watchName) &&\n        /^\\.\\w+/.test(name.slice(watchName.length)),\n    ));\n", "import { FieldRefs, InternalFieldName, Ref } from '../types';\nimport { get } from '../utils';\nimport isObject from '../utils/isObject';\n\nconst iterateFieldsByAction = (\n  fields: FieldRefs,\n  action: (ref: Ref, name: string) => 1 | undefined | void,\n  fieldsNames?: Set<InternalFieldName> | InternalFieldName[] | 0,\n  abortEarly?: boolean,\n) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n\n    if (field) {\n      const { _f, ...currentField } = field;\n\n      if (_f) {\n        if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n          return true;\n        } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n          return true;\n        } else {\n          if (iterateFieldsByAction(currentField, action)) {\n            break;\n          }\n        }\n      } else if (isObject(currentField)) {\n        if (iterateFieldsByAction(currentField as FieldRefs, action)) {\n          break;\n        }\n      }\n    }\n  }\n  return;\n};\nexport default iterateFieldsByAction;\n", "import { FieldError, FieldErrors, FieldValues } from '../types';\nimport get from '../utils/get';\nimport isKey from '../utils/isKey';\n\nexport default function schemaErrorLookup<T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  _fields: FieldValues,\n  name: string,\n): {\n  error?: FieldError;\n  name: string;\n} {\n  const error = get(errors, name);\n\n  if (error || isKey(name)) {\n    return {\n      error,\n      name,\n    };\n  }\n\n  const names = name.split('.');\n\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return { name };\n    }\n\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError,\n      };\n    }\n\n    names.pop();\n  }\n\n  return {\n    name,\n  };\n}\n", "import { VALIDATION_MODE } from '../constants';\nimport {\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  ReadFormState,\n} from '../types';\nimport isEmptyObject from '../utils/isEmptyObject';\n\nexport default <T extends FieldValues, K extends ReadFormState>(\n  formStateData: Partial<FormState<T>> & {\n    name?: InternalFieldName;\n    values?: T;\n  },\n  _proxyFormState: K,\n  updateFormState: (formState: Partial<FormState<T>>) => void,\n  isRoot?: boolean,\n) => {\n  updateFormState(formStateData);\n  const { name, ...formState } = formStateData;\n\n  return (\n    isEmptyObject(formState) ||\n    Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n    Object.keys(formState).find(\n      (key) =>\n        _proxyFormState[key as keyof ReadFormState] ===\n        (!isRoot || VALIDATION_MODE.all),\n    )\n  );\n};\n", "import {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport get from '../utils/get';\nimport set from '../utils/set';\n\nexport default <T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  error: Partial<Record<string, FieldError>>,\n  name: InternalFieldName,\n): FieldErrors<T> => {\n  const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\n", "import { Message } from '../types';\nimport isString from '../utils/isString';\n\nexport default (value: unknown): value is Message => isString(value);\n", "import { FieldError, Ref, ValidateResult } from '../types';\nimport isBoolean from '../utils/isBoolean';\nimport isMessage from '../utils/isMessage';\n\nexport default function getValidateError(\n  result: ValidateResult,\n  ref: Ref,\n  type = 'validate',\n): FieldError | void {\n  if (\n    isMessage(result) ||\n    (Array.isArray(result) && result.every(isMessage)) ||\n    (isBoolean(result) && !result)\n  ) {\n    return {\n      type,\n      message: isMessage(result) ? result : '',\n      ref,\n    };\n  }\n}\n", "import { ValidationRule } from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\n\nexport default (validationData?: ValidationRule) =>\n  isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n      };\n", "import { INPUT_VALIDATION_RULES } from '../constants';\nimport {\n  Field,\n  FieldError,\n  FieldValues,\n  InternalFieldErrors,\n  InternalNameSet,\n  MaxType,\n  Message,\n  MinType,\n  NativeFieldValue,\n} from '../types';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMessage from '../utils/isMessage';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioInput from '../utils/isRadioInput';\nimport isRegex from '../utils/isRegex';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nimport appendErrors from './appendErrors';\nimport getCheckboxValue from './getCheckboxValue';\nimport getRadioValue from './getRadioValue';\nimport getValidateError from './getValidateError';\nimport getValueAndMessage from './getValueAndMessage';\n\nexport default async <T extends FieldValues>(\n  field: Field,\n  disabledFieldNames: InternalNameSet,\n  formValues: T,\n  validateAllFieldCriteria: boolean,\n  shouldUseNativeValidation?: boolean,\n  isFieldArray?: boolean,\n): Promise<InternalFieldErrors> => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount,\n  } = field._f;\n  const inputValue: NativeFieldValue = get(formValues, name);\n  if (!mount || disabledFieldNames.has(name)) {\n    return {};\n  }\n  const inputRef: HTMLInputElement = refs ? refs[0] : (ref as HTMLInputElement);\n  const setCustomValidity = (message?: string | boolean) => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error: InternalFieldErrors = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty =\n    ((valueAsNumber || isFileInput(ref)) &&\n      isUndefined(ref.value) &&\n      isUndefined(inputValue)) ||\n    (isHTMLElement(ref) && ref.value === '') ||\n    inputValue === '' ||\n    (Array.isArray(inputValue) && !inputValue.length);\n  const appendErrorsCurry = appendErrors.bind(\n    null,\n    name,\n    validateAllFieldCriteria,\n    error,\n  );\n  const getMinMaxMessage = (\n    exceedMax: boolean,\n    maxLengthMessage: Message,\n    minLengthMessage: Message,\n    maxType: MaxType = INPUT_VALIDATION_RULES.maxLength,\n    minType: MinType = INPUT_VALIDATION_RULES.minLength,\n  ) => {\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = {\n      type: exceedMax ? maxType : minType,\n      message,\n      ref,\n      ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n    };\n  };\n\n  if (\n    isFieldArray\n      ? !Array.isArray(inputValue) || !inputValue.length\n      : required &&\n        ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n          (isBoolean(inputValue) && !inputValue) ||\n          (isCheckBox && !getCheckboxValue(refs).isValid) ||\n          (isRadio && !getRadioValue(refs).isValid))\n  ) {\n    const { value, message } = isMessage(required)\n      ? { value: !!required, message: required }\n      : getValueAndMessage(required);\n\n    if (value) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue as number)) {\n      const valueNumber =\n        (ref as HTMLInputElement).valueAsNumber ||\n        (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate =\n        (ref as HTMLInputElement).valueAsDate || new Date(inputValue as string);\n      const convertTimeToDate = (time: unknown) =>\n        new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime\n          ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n          : isWeek\n            ? inputValue > maxOutput.value\n            : valueDate > new Date(maxOutput.value);\n      }\n\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime\n          ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n          : isWeek\n            ? inputValue < minOutput.value\n            : valueDate < new Date(minOutput.value);\n      }\n    }\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        !!exceedMax,\n        maxOutput.message,\n        minOutput.message,\n        INPUT_VALIDATION_RULES.max,\n        INPUT_VALIDATION_RULES.min,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (\n    (maxLength || minLength) &&\n    !isEmpty &&\n    (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))\n  ) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax =\n      !isNullOrUndefined(maxLengthOutput.value) &&\n      inputValue.length > +maxLengthOutput.value;\n    const exceedMin =\n      !isNullOrUndefined(minLengthOutput.value) &&\n      inputValue.length < +minLengthOutput.value;\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        exceedMax,\n        maxLengthOutput.message,\n        minLengthOutput.message,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const { value: patternValue, message } = getValueAndMessage(pattern);\n\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n\n      if (validateError) {\n        error[name] = {\n          ...validateError,\n          ...appendErrorsCurry(\n            INPUT_VALIDATION_RULES.validate,\n            validateError.message,\n          ),\n        };\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {} as FieldError;\n\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n\n        const validateError = getValidateError(\n          await validate[key](inputValue, formValues),\n          inputRef,\n          key,\n        );\n\n        if (validateError) {\n          validationResult = {\n            ...validateError,\n            ...appendErrorsCurry(key, validateError.message),\n          };\n\n          setCustomValidity(validateError.message);\n\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n\n      if (!isEmptyObject(validationResult)) {\n        error[name] = {\n          ref: inputRef,\n          ...validationResult,\n        };\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n\n  setCustomValidity(true);\n  return error;\n};\n", "import { EVENTS, VALIDATION_MODE } from '../constants';\nimport {\n  BatchFieldArrayUpdate,\n  ChangeHandler,\n  Control,\n  DeepPartial,\n  DelayCallback,\n  EventType,\n  Field,\n  FieldError,\n  FieldErrors,\n  FieldNamesMarkedBoolean,\n  FieldPath,\n  FieldRefs,\n  FieldValues,\n  FormState,\n  FromSubscribe,\n  GetIsDirty,\n  InternalFieldName,\n  Names,\n  Path,\n  ReadFormState,\n  Ref,\n  SetFieldValue,\n  SetValueConfig,\n  Subjects,\n  UseFormClearErrors,\n  UseFormGetFieldState,\n  UseFormGetValues,\n  UseFormHandleSubmit,\n  UseFormProps,\n  UseFormRegister,\n  UseFormReset,\n  UseFormResetField,\n  UseFormReturn,\n  UseFormSetError,\n  UseFormSetFocus,\n  UseFormSetValue,\n  UseFormTrigger,\n  UseFormUnregister,\n  UseFormWatch,\n  UseFromSubscribe,\n  WatchInternal,\n  WatchObserver,\n} from '../types';\nimport cloneObject from '../utils/cloneObject';\nimport compact from '../utils/compact';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport createSubject from '../utils/createSubject';\nimport deepEqual from '../utils/deepEqual';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isDateObject from '../utils/isDateObject';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioOrCheckbox from '../utils/isRadioOrCheckbox';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\nimport isWeb from '../utils/isWeb';\nimport live from '../utils/live';\nimport set from '../utils/set';\nimport unset from '../utils/unset';\n\nimport generateWatchOutput from './generateWatchOutput';\nimport getDirtyFields from './getDirtyFields';\nimport getEventValue from './getEventValue';\nimport getFieldValue from './getFieldValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getResolverOptions from './getResolverOptions';\nimport getRuleValue from './getRuleValue';\nimport getValidationModes from './getValidationModes';\nimport hasPromiseValidation from './hasPromiseValidation';\nimport hasValidation from './hasValidation';\nimport isNameInFieldArray from './isNameInFieldArray';\nimport isWatched from './isWatched';\nimport iterateFieldsByAction from './iterateFieldsByAction';\nimport schemaErrorLookup from './schemaErrorLookup';\nimport shouldRenderFormState from './shouldRenderFormState';\nimport shouldSubscribeByName from './shouldSubscribeByName';\nimport skipValidation from './skipValidation';\nimport unsetEmptyArray from './unsetEmptyArray';\nimport updateFieldArrayRootError from './updateFieldArrayRootError';\nimport validateField from './validateField';\n\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true,\n} as const;\n\nexport function createFormControl<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFormProps<TFieldValues, TContext, TTransformedValues> = {},\n): Omit<\n  UseFormReturn<TFieldValues, TContext, TTransformedValues>,\n  'formState'\n> & {\n  formControl: Omit<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues>,\n    'formState'\n  >;\n} {\n  let _options = {\n    ...defaultOptions,\n    ...props,\n  };\n  let _formState: FormState<TFieldValues> = {\n    submitCount: 0,\n    isDirty: false,\n    isReady: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    validatingFields: {},\n    errors: _options.errors || {},\n    disabled: _options.disabled || false,\n  };\n  const _fields: FieldRefs = {};\n  let _defaultValues =\n    isObject(_options.defaultValues) || isObject(_options.values)\n      ? cloneObject(_options.values || _options.defaultValues) || {}\n      : {};\n  let _formValues = _options.shouldUnregister\n    ? ({} as TFieldValues)\n    : (cloneObject(_defaultValues) as TFieldValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false,\n  };\n  let _names: Names = {\n    mount: new Set(),\n    disabled: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set(),\n  };\n  let delayErrorCallback: DelayCallback | null;\n  let timer = 0;\n  const _proxyFormState: ReadFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    validatingFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  };\n  let _proxySubscribeFormState = {\n    ..._proxyFormState,\n  };\n  const _subjects: Subjects<TFieldValues> = {\n    array: createSubject(),\n    state: createSubject(),\n  };\n  const validationModeBeforeSubmit = getValidationModes(_options.mode);\n  const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n  const shouldDisplayAllAssociatedErrors =\n    _options.criteriaMode === VALIDATION_MODE.all;\n\n  const debounce =\n    <T extends Function>(callback: T) =>\n    (wait: number) => {\n      clearTimeout(timer);\n      timer = setTimeout(callback, wait);\n    };\n\n  const _setValid = async (shouldUpdateValid?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValid ||\n        _proxySubscribeFormState.isValid ||\n        shouldUpdateValid)\n    ) {\n      const isValid = _options.resolver\n        ? isEmptyObject((await _runSchema()).errors)\n        : await executeBuiltInValidation(_fields, true);\n\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid,\n        });\n      }\n    }\n  };\n\n  const _updateIsValidating = (names?: string[], isValidating?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValidating ||\n        _proxyFormState.validatingFields ||\n        _proxySubscribeFormState.isValidating ||\n        _proxySubscribeFormState.validatingFields)\n    ) {\n      (names || Array.from(_names.mount)).forEach((name) => {\n        if (name) {\n          isValidating\n            ? set(_formState.validatingFields, name, isValidating)\n            : unset(_formState.validatingFields, name);\n        }\n      });\n\n      _subjects.state.next({\n        validatingFields: _formState.validatingFields,\n        isValidating: !isEmptyObject(_formState.validatingFields),\n      });\n    }\n  };\n\n  const _setFieldArray: BatchFieldArrayUpdate = (\n    name,\n    values = [],\n    method,\n    args,\n    shouldSetValues = true,\n    shouldUpdateFieldsAndState = true,\n  ) => {\n    if (args && method && !_options.disabled) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n\n      if (\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.errors, name))\n      ) {\n        const errors = method(\n          get(_formState.errors, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n\n      if (\n        (_proxyFormState.touchedFields ||\n          _proxySubscribeFormState.touchedFields) &&\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.touchedFields, name))\n      ) {\n        const touchedFields = method(\n          get(_formState.touchedFields, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n\n      if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid,\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n\n  const updateErrors = (name: InternalFieldName, error: FieldError) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors,\n    });\n  };\n\n  const _setErrors = (errors: FieldErrors<TFieldValues>) => {\n    _formState.errors = errors;\n    _subjects.state.next({\n      errors: _formState.errors,\n      isValid: false,\n    });\n  };\n\n  const updateValidAndValue = (\n    name: InternalFieldName,\n    shouldSkipSetValueAs: boolean,\n    value?: unknown,\n    ref?: Ref,\n  ) => {\n    const field: Field = get(_fields, name);\n\n    if (field) {\n      const defaultValue = get(\n        _formValues,\n        name,\n        isUndefined(value) ? get(_defaultValues, name) : value,\n      );\n\n      isUndefined(defaultValue) ||\n      (ref && (ref as HTMLInputElement).defaultChecked) ||\n      shouldSkipSetValueAs\n        ? set(\n            _formValues,\n            name,\n            shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f),\n          )\n        : setFieldValue(name, defaultValue);\n\n      _state.mount && _setValid();\n    }\n  };\n\n  const updateTouchAndDirty = (\n    name: InternalFieldName,\n    fieldValue: unknown,\n    isBlurEvent?: boolean,\n    shouldDirty?: boolean,\n    shouldRender?: boolean,\n  ): Partial<\n    Pick<FormState<TFieldValues>, 'dirtyFields' | 'isDirty' | 'touchedFields'>\n  > => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output: Partial<FormState<TFieldValues>> & { name: string } = {\n      name,\n    };\n\n    if (!_options.disabled) {\n      if (!isBlurEvent || shouldDirty) {\n        if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n          isPreviousDirty = _formState.isDirty;\n          _formState.isDirty = output.isDirty = _getDirty();\n          shouldUpdateField = isPreviousDirty !== output.isDirty;\n        }\n\n        const isCurrentFieldPristine = deepEqual(\n          get(_defaultValues, name),\n          fieldValue,\n        );\n\n        isPreviousDirty = !!get(_formState.dirtyFields, name);\n        isCurrentFieldPristine\n          ? unset(_formState.dirtyFields, name)\n          : set(_formState.dirtyFields, name, true);\n        output.dirtyFields = _formState.dirtyFields;\n        shouldUpdateField =\n          shouldUpdateField ||\n          ((_proxyFormState.dirtyFields ||\n            _proxySubscribeFormState.dirtyFields) &&\n            isPreviousDirty !== !isCurrentFieldPristine);\n      }\n\n      if (isBlurEvent) {\n        const isPreviousFieldTouched = get(_formState.touchedFields, name);\n\n        if (!isPreviousFieldTouched) {\n          set(_formState.touchedFields, name, isBlurEvent);\n          output.touchedFields = _formState.touchedFields;\n          shouldUpdateField =\n            shouldUpdateField ||\n            ((_proxyFormState.touchedFields ||\n              _proxySubscribeFormState.touchedFields) &&\n              isPreviousFieldTouched !== isBlurEvent);\n        }\n      }\n\n      shouldUpdateField && shouldRender && _subjects.state.next(output);\n    }\n\n    return shouldUpdateField ? output : {};\n  };\n\n  const shouldRenderByError = (\n    name: InternalFieldName,\n    isValid?: boolean,\n    error?: FieldError,\n    fieldState?: {\n      dirty?: FieldNamesMarkedBoolean<TFieldValues>;\n      isDirty?: boolean;\n      touched?: FieldNamesMarkedBoolean<TFieldValues>;\n    },\n  ) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid =\n      (_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n      isBoolean(isValid) &&\n      _formState.isValid !== isValid;\n\n    if (_options.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(_options.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error\n        ? set(_formState.errors, name, error)\n        : unset(_formState.errors, name);\n    }\n\n    if (\n      (error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n      !isEmptyObject(fieldState) ||\n      shouldUpdateValid\n    ) {\n      const updatedFormState = {\n        ...fieldState,\n        ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n        errors: _formState.errors,\n        name,\n      };\n\n      _formState = {\n        ..._formState,\n        ...updatedFormState,\n      };\n\n      _subjects.state.next(updatedFormState);\n    }\n  };\n\n  const _runSchema = async (name?: InternalFieldName[]) => {\n    _updateIsValidating(name, true);\n    const result = await _options.resolver!(\n      _formValues as TFieldValues,\n      _options.context,\n      getResolverOptions(\n        name || _names.mount,\n        _fields,\n        _options.criteriaMode,\n        _options.shouldUseNativeValidation,\n      ),\n    );\n    _updateIsValidating(name);\n    return result;\n  };\n\n  const executeSchemaAndUpdateState = async (names?: InternalFieldName[]) => {\n    const { errors } = await _runSchema(names);\n\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error\n          ? set(_formState.errors, name, error)\n          : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n\n    return errors;\n  };\n\n  const executeBuiltInValidation = async (\n    fields: FieldRefs,\n    shouldOnlyCheckValid?: boolean,\n    context: {\n      valid: boolean;\n    } = {\n      valid: true,\n    },\n  ) => {\n    for (const name in fields) {\n      const field = fields[name];\n\n      if (field) {\n        const { _f, ...fieldValue } = field as Field;\n\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const isPromiseFunction =\n            field._f && hasPromiseValidation((field as Field)._f);\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name], true);\n          }\n\n          const fieldError = await validateField(\n            field as Field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation && !shouldOnlyCheckValid,\n            isFieldArrayRoot,\n          );\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name]);\n          }\n\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n\n          !shouldOnlyCheckValid &&\n            (get(fieldError, _f.name)\n              ? isFieldArrayRoot\n                ? updateFieldArrayRootError(\n                    _formState.errors,\n                    fieldError,\n                    _f.name,\n                  )\n                : set(_formState.errors, _f.name, fieldError[_f.name])\n              : unset(_formState.errors, _f.name));\n        }\n\n        !isEmptyObject(fieldValue) &&\n          (await executeBuiltInValidation(\n            fieldValue,\n            shouldOnlyCheckValid,\n            context,\n          ));\n      }\n    }\n\n    return context.valid;\n  };\n\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field: Field = get(_fields, name);\n\n      field &&\n        (field._f.refs\n          ? field._f.refs.every((ref) => !live(ref))\n          : !live(field._f.ref)) &&\n        unregister(name as FieldPath<TFieldValues>);\n    }\n\n    _names.unMount = new Set();\n  };\n\n  const _getDirty: GetIsDirty = (name, data) =>\n    !_options.disabled &&\n    (name && data && set(_formValues, name, data),\n    !deepEqual(getValues(), _defaultValues));\n\n  const _getWatch: WatchInternal<TFieldValues> = (\n    names,\n    defaultValue,\n    isGlobal,\n  ) =>\n    generateWatchOutput(\n      names,\n      _names,\n      {\n        ...(_state.mount\n          ? _formValues\n          : isUndefined(defaultValue)\n            ? _defaultValues\n            : isString(names)\n              ? { [names]: defaultValue }\n              : defaultValue),\n      },\n      isGlobal,\n      defaultValue,\n    );\n\n  const _getFieldArray = <TFieldArrayValues>(\n    name: InternalFieldName,\n  ): Partial<TFieldArrayValues>[] =>\n    compact(\n      get(\n        _state.mount ? _formValues : _defaultValues,\n        name,\n        _options.shouldUnregister ? get(_defaultValues, name, []) : [],\n      ),\n    );\n\n  const setFieldValue = (\n    name: InternalFieldName,\n    value: SetFieldValue<TFieldValues>,\n    options: SetValueConfig = {},\n  ) => {\n    const field: Field = get(_fields, name);\n    let fieldValue: unknown = value;\n\n    if (field) {\n      const fieldReference = field._f;\n\n      if (fieldReference) {\n        !fieldReference.disabled &&\n          set(_formValues, name, getFieldValueAs(value, fieldReference));\n\n        fieldValue =\n          isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n            ? ''\n            : value;\n\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(\n            (optionRef) =>\n              (optionRef.selected = (\n                fieldValue as InternalFieldName[]\n              ).includes(optionRef.value)),\n          );\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.length > 1\n              ? fieldReference.refs.forEach(\n                  (checkboxRef) =>\n                    (!checkboxRef.defaultChecked || !checkboxRef.disabled) &&\n                    (checkboxRef.checked = Array.isArray(fieldValue)\n                      ? !!(fieldValue as []).find(\n                          (data: string) => data === checkboxRef.value,\n                        )\n                      : fieldValue === checkboxRef.value),\n                )\n              : fieldReference.refs[0] &&\n                (fieldReference.refs[0].checked = !!fieldValue);\n          } else {\n            fieldReference.refs.forEach(\n              (radioRef: HTMLInputElement) =>\n                (radioRef.checked = radioRef.value === fieldValue),\n            );\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n\n          if (!fieldReference.ref.type) {\n            _subjects.state.next({\n              name,\n              values: cloneObject(_formValues),\n            });\n          }\n        }\n      }\n    }\n\n    (options.shouldDirty || options.shouldTouch) &&\n      updateTouchAndDirty(\n        name,\n        fieldValue,\n        options.shouldTouch,\n        options.shouldDirty,\n        true,\n      );\n\n    options.shouldValidate && trigger(name as Path<TFieldValues>);\n  };\n\n  const setValues = <\n    T extends InternalFieldName,\n    K extends SetFieldValue<TFieldValues>,\n    U extends SetValueConfig,\n  >(\n    name: T,\n    value: K,\n    options: U,\n  ) => {\n    for (const fieldKey in value) {\n      const fieldValue = value[fieldKey];\n      const fieldName = `${name}.${fieldKey}`;\n      const field = get(_fields, fieldName);\n\n      (_names.array.has(name) ||\n        isObject(fieldValue) ||\n        (field && !field._f)) &&\n      !isDateObject(fieldValue)\n        ? setValues(fieldName, fieldValue, options)\n        : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n\n  const setValue: UseFormSetValue<TFieldValues> = (\n    name,\n    value,\n    options = {},\n  ) => {\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n\n    set(_formValues, name, cloneValue);\n\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: cloneObject(_formValues),\n      });\n\n      if (\n        (_proxyFormState.isDirty ||\n          _proxyFormState.dirtyFields ||\n          _proxySubscribeFormState.isDirty ||\n          _proxySubscribeFormState.dirtyFields) &&\n        options.shouldDirty\n      ) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue),\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue)\n        ? setValues(name, cloneValue, options)\n        : setFieldValue(name, cloneValue, options);\n    }\n\n    isWatched(name, _names) && _subjects.state.next({ ..._formState });\n    _subjects.state.next({\n      name: _state.mount ? name : undefined,\n      values: cloneObject(_formValues),\n    });\n  };\n\n  const onChange: ChangeHandler = async (event) => {\n    _state.mount = true;\n    const target = event.target;\n    let name: string = target.name;\n    let isFieldValueUpdated = true;\n    const field: Field = get(_fields, name);\n    const _updateIsFieldValueUpdated = (fieldValue: unknown) => {\n      isFieldValueUpdated =\n        Number.isNaN(fieldValue) ||\n        (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n        deepEqual(fieldValue, get(_formValues, name, fieldValue));\n    };\n\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = target.type\n        ? getFieldValue(field._f)\n        : getEventValue(event);\n      const isBlurEvent =\n        event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation =\n        (!hasValidation(field._f) &&\n          !_options.resolver &&\n          !get(_formState.errors, name) &&\n          !field._f.deps) ||\n        skipValidation(\n          isBlurEvent,\n          get(_formState.touchedFields, name),\n          _formState.isSubmitted,\n          validationModeAfterSubmit,\n          validationModeBeforeSubmit,\n        );\n      const watched = isWatched(name, _names, isBlurEvent);\n\n      set(_formValues, name, fieldValue);\n\n      if (isBlurEvent) {\n        field._f.onBlur && field._f.onBlur(event);\n        delayErrorCallback && delayErrorCallback(0);\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n\n      const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n\n      !isBlurEvent &&\n        _subjects.state.next({\n          name,\n          type: event.type,\n          values: cloneObject(_formValues),\n        });\n\n      if (shouldSkipValidation) {\n        if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n          if (_options.mode === 'onBlur') {\n            if (isBlurEvent) {\n              _setValid();\n            }\n          } else if (!isBlurEvent) {\n            _setValid();\n          }\n        }\n\n        return (\n          shouldRender &&\n          _subjects.state.next({ name, ...(watched ? {} : fieldState) })\n        );\n      }\n\n      !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n\n      if (_options.resolver) {\n        const { errors } = await _runSchema([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          const previousErrorLookupResult = schemaErrorLookup(\n            _formState.errors,\n            _fields,\n            name,\n          );\n          const errorLookupResult = schemaErrorLookup(\n            errors,\n            _fields,\n            previousErrorLookupResult.name || name,\n          );\n\n          error = errorLookupResult.error;\n          name = errorLookupResult.name;\n\n          isValid = isEmptyObject(errors);\n        }\n      } else {\n        _updateIsValidating([name], true);\n        error = (\n          await validateField(\n            field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation,\n          )\n        )[name];\n        _updateIsValidating([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (\n            _proxyFormState.isValid ||\n            _proxySubscribeFormState.isValid\n          ) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n\n      if (isFieldValueUpdated) {\n        field._f.deps &&\n          trigger(\n            field._f.deps as\n              | FieldPath<TFieldValues>\n              | FieldPath<TFieldValues>[],\n          );\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n\n  const _focusInput = (ref: Ref, key: string) => {\n    if (get(_formState.errors, key) && ref.focus) {\n      ref.focus();\n      return 1;\n    }\n    return;\n  };\n\n  const trigger: UseFormTrigger<TFieldValues> = async (name, options = {}) => {\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name) as InternalFieldName[];\n\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(\n        isUndefined(name) ? name : fieldNames,\n      );\n\n      isValid = isEmptyObject(errors);\n      validationResult = name\n        ? !fieldNames.some((name) => get(errors, name))\n        : isValid;\n    } else if (name) {\n      validationResult = (\n        await Promise.all(\n          fieldNames.map(async (fieldName) => {\n            const field = get(_fields, fieldName);\n            return await executeBuiltInValidation(\n              field && field._f ? { [fieldName]: field } : field,\n            );\n          }),\n        )\n      ).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _setValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n\n    _subjects.state.next({\n      ...(!isString(name) ||\n      ((_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n        isValid !== _formState.isValid)\n        ? {}\n        : { name }),\n      ...(_options.resolver || !name ? { isValid } : {}),\n      errors: _formState.errors,\n    });\n\n    options.shouldFocus &&\n      !validationResult &&\n      iterateFieldsByAction(\n        _fields,\n        _focusInput,\n        name ? fieldNames : _names.mount,\n      );\n\n    return validationResult;\n  };\n\n  const getValues: UseFormGetValues<TFieldValues> = (\n    fieldNames?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>,\n  ) => {\n    const values = {\n      ...(_state.mount ? _formValues : _defaultValues),\n    };\n\n    return isUndefined(fieldNames)\n      ? values\n      : isString(fieldNames)\n        ? get(values, fieldNames)\n        : fieldNames.map((name) => get(values, name));\n  };\n\n  const getFieldState: UseFormGetFieldState<TFieldValues> = (\n    name,\n    formState,\n  ) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    error: get((formState || _formState).errors, name),\n    isValidating: !!get(_formState.validatingFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name),\n  });\n\n  const clearErrors: UseFormClearErrors<TFieldValues> = (name) => {\n    name &&\n      convertToArrayPayload(name).forEach((inputName) =>\n        unset(_formState.errors, inputName),\n      );\n\n    _subjects.state.next({\n      errors: name ? _formState.errors : {},\n    });\n  };\n\n  const setError: UseFormSetError<TFieldValues> = (name, error, options) => {\n    const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n    const currentError = get(_formState.errors, name) || {};\n\n    // Don't override existing error messages elsewhere in the object tree.\n    const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n\n    set(_formState.errors, name, {\n      ...restOfErrorTree,\n      ...error,\n      ref,\n    });\n\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false,\n    });\n\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n\n  const watch: UseFormWatch<TFieldValues> = (\n    name?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>\n      | WatchObserver<TFieldValues>,\n    defaultValue?: DeepPartial<TFieldValues>,\n  ) =>\n    isFunction(name)\n      ? _subjects.state.subscribe({\n          next: (payload) =>\n            name(\n              _getWatch(undefined, defaultValue),\n              payload as {\n                name?: FieldPath<TFieldValues>;\n                type?: EventType;\n                value?: unknown;\n              },\n            ),\n        })\n      : _getWatch(\n          name as InternalFieldName | InternalFieldName[],\n          defaultValue,\n          true,\n        );\n\n  const _subscribe: FromSubscribe<TFieldValues> = (props) =>\n    _subjects.state.subscribe({\n      next: (\n        formState: Partial<FormState<TFieldValues>> & {\n          name?: InternalFieldName;\n          values?: TFieldValues | undefined;\n        },\n      ) => {\n        if (\n          shouldSubscribeByName(props.name, formState.name, props.exact) &&\n          shouldRenderFormState(\n            formState,\n            (props.formState as ReadFormState) || _proxyFormState,\n            _setFormState,\n            props.reRenderRoot,\n          )\n        ) {\n          props.callback({\n            values: { ..._formValues } as TFieldValues,\n            ..._formState,\n            ...formState,\n          });\n        }\n      },\n    }).unsubscribe;\n\n  const subscribe: UseFromSubscribe<TFieldValues> = (props) => {\n    _state.mount = true;\n    _proxySubscribeFormState = {\n      ..._proxySubscribeFormState,\n      ...props.formState,\n    };\n    return _subscribe({\n      ...props,\n      formState: _proxySubscribeFormState,\n    });\n  };\n\n  const unregister: UseFormUnregister<TFieldValues> = (name, options = {}) => {\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !options.keepIsValidating &&\n        unset(_formState.validatingFields, fieldName);\n      !_options.shouldUnregister &&\n        !options.keepDefaultValue &&\n        unset(_defaultValues, fieldName);\n    }\n\n    _subjects.state.next({\n      values: cloneObject(_formValues),\n    });\n\n    _subjects.state.next({\n      ..._formState,\n      ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n    });\n\n    !options.keepIsValid && _setValid();\n  };\n\n  const _setDisabledField: Control<TFieldValues>['_setDisabledField'] = ({\n    disabled,\n    name,\n  }) => {\n    if (\n      (isBoolean(disabled) && _state.mount) ||\n      !!disabled ||\n      _names.disabled.has(name)\n    ) {\n      disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n    }\n  };\n\n  const register: UseFormRegister<TFieldValues> = (name, options = {}) => {\n    let field = get(_fields, name);\n    const disabledIsDefined =\n      isBoolean(options.disabled) || isBoolean(_options.disabled);\n\n    set(_fields, name, {\n      ...(field || {}),\n      _f: {\n        ...(field && field._f ? field._f : { ref: { name } }),\n        name,\n        mount: true,\n        ...options,\n      },\n    });\n    _names.mount.add(name);\n\n    if (field) {\n      _setDisabledField({\n        disabled: isBoolean(options.disabled)\n          ? options.disabled\n          : _options.disabled,\n        name,\n      });\n    } else {\n      updateValidAndValue(name, true, options.value);\n    }\n\n    return {\n      ...(disabledIsDefined\n        ? { disabled: options.disabled || _options.disabled }\n        : {}),\n      ...(_options.progressive\n        ? {\n            required: !!options.required,\n            min: getRuleValue(options.min),\n            max: getRuleValue(options.max),\n            minLength: getRuleValue<number>(options.minLength) as number,\n            maxLength: getRuleValue(options.maxLength) as number,\n            pattern: getRuleValue(options.pattern) as string,\n          }\n        : {}),\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: (ref: HTMLInputElement | null): void => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n\n          const fieldRef = isUndefined(ref.value)\n            ? ref.querySelectorAll\n              ? (ref.querySelectorAll('input,select,textarea')[0] as Ref) || ref\n              : ref\n            : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n\n          if (\n            radioOrCheckbox\n              ? refs.find((option: Ref) => option === fieldRef)\n              : fieldRef === field._f.ref\n          ) {\n            return;\n          }\n\n          set(_fields, name, {\n            _f: {\n              ...field._f,\n              ...(radioOrCheckbox\n                ? {\n                    refs: [\n                      ...refs.filter(live),\n                      fieldRef,\n                      ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                    ],\n                    ref: { type: fieldRef.type, name },\n                  }\n                : { ref: fieldRef }),\n            },\n          });\n\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n\n          if (field._f) {\n            field._f.mount = false;\n          }\n\n          (_options.shouldUnregister || options.shouldUnregister) &&\n            !(isNameInFieldArray(_names.array, name) && _state.action) &&\n            _names.unMount.add(name);\n        }\n      },\n    };\n  };\n\n  const _focusError = () =>\n    _options.shouldFocusError &&\n    iterateFieldsByAction(_fields, _focusInput, _names.mount);\n\n  const _disableForm = (disabled?: boolean) => {\n    if (isBoolean(disabled)) {\n      _subjects.state.next({ disabled });\n      iterateFieldsByAction(\n        _fields,\n        (ref, name) => {\n          const currentField: Field = get(_fields, name);\n          if (currentField) {\n            ref.disabled = currentField._f.disabled || disabled;\n\n            if (Array.isArray(currentField._f.refs)) {\n              currentField._f.refs.forEach((inputRef) => {\n                inputRef.disabled = currentField._f.disabled || disabled;\n              });\n            }\n          }\n        },\n        0,\n        false,\n      );\n    }\n  };\n\n  const handleSubmit: UseFormHandleSubmit<TFieldValues, TTransformedValues> =\n    (onValid, onInvalid) => async (e) => {\n      let onValidError = undefined;\n      if (e) {\n        e.preventDefault && e.preventDefault();\n        (e as React.BaseSyntheticEvent).persist &&\n          (e as React.BaseSyntheticEvent).persist();\n      }\n      let fieldValues: TFieldValues | TTransformedValues | {} =\n        cloneObject(_formValues);\n\n      _subjects.state.next({\n        isSubmitting: true,\n      });\n\n      if (_options.resolver) {\n        const { errors, values } = await _runSchema();\n        _formState.errors = errors;\n        fieldValues = values as TFieldValues;\n      } else {\n        await executeBuiltInValidation(_fields);\n      }\n\n      if (_names.disabled.size) {\n        for (const name of _names.disabled) {\n          set(fieldValues, name, undefined);\n        }\n      }\n\n      unset(_formState.errors, 'root');\n\n      if (isEmptyObject(_formState.errors)) {\n        _subjects.state.next({\n          errors: {},\n        });\n        try {\n          await onValid(fieldValues as TTransformedValues, e);\n        } catch (error) {\n          onValidError = error;\n        }\n      } else {\n        if (onInvalid) {\n          await onInvalid({ ..._formState.errors }, e);\n        }\n        _focusError();\n        setTimeout(_focusError);\n      }\n\n      _subjects.state.next({\n        isSubmitted: true,\n        isSubmitting: false,\n        isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n        submitCount: _formState.submitCount + 1,\n        errors: _formState.errors,\n      });\n      if (onValidError) {\n        throw onValidError;\n      }\n    };\n\n  const resetField: UseFormResetField<TFieldValues> = (name, options = {}) => {\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, cloneObject(get(_defaultValues, name)));\n      } else {\n        setValue(\n          name,\n          options.defaultValue as Parameters<typeof setValue<typeof name>>[1],\n        );\n        set(_defaultValues, name, cloneObject(options.defaultValue));\n      }\n\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue\n          ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n          : _getDirty();\n      }\n\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _setValid();\n      }\n\n      _subjects.state.next({ ..._formState });\n    }\n  };\n\n  const _reset: UseFormReset<TFieldValues> = (\n    formValues,\n    keepStateOptions = {},\n  ) => {\n    const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const isEmptyResetValues = isEmptyObject(formValues);\n    const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues) {\n        const fieldsToCheck = new Set([\n          ..._names.mount,\n          ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n        ]);\n        for (const fieldName of Array.from(fieldsToCheck)) {\n          get(_formState.dirtyFields, fieldName)\n            ? set(values, fieldName, get(_formValues, fieldName))\n            : setValue(\n                fieldName as FieldPath<TFieldValues>,\n                get(values, fieldName),\n              );\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs)\n                ? field._f.refs[0]\n                : field._f.ref;\n\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n\n        for (const fieldName of _names.mount) {\n          setValue(\n            fieldName as FieldPath<TFieldValues>,\n            get(values, fieldName),\n          );\n        }\n      }\n\n      _formValues = cloneObject(values) as TFieldValues;\n\n      _subjects.array.next({\n        values: { ...values },\n      });\n\n      _subjects.state.next({\n        values: { ...values } as TFieldValues,\n      });\n    }\n\n    _names = {\n      mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      disabled: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: '',\n    };\n\n    _state.mount =\n      !_proxyFormState.isValid ||\n      !!keepStateOptions.keepIsValid ||\n      !!keepStateOptions.keepDirtyValues;\n\n    _state.watch = !!_options.shouldUnregister;\n\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount\n        ? _formState.submitCount\n        : 0,\n      isDirty: isEmptyResetValues\n        ? false\n        : keepStateOptions.keepDirty\n          ? _formState.isDirty\n          : !!(\n              keepStateOptions.keepDefaultValues &&\n              !deepEqual(formValues, _defaultValues)\n            ),\n      isSubmitted: keepStateOptions.keepIsSubmitted\n        ? _formState.isSubmitted\n        : false,\n      dirtyFields: isEmptyResetValues\n        ? {}\n        : keepStateOptions.keepDirtyValues\n          ? keepStateOptions.keepDefaultValues && _formValues\n            ? getDirtyFields(_defaultValues, _formValues)\n            : _formState.dirtyFields\n          : keepStateOptions.keepDefaultValues && formValues\n            ? getDirtyFields(_defaultValues, formValues)\n            : keepStateOptions.keepDirty\n              ? _formState.dirtyFields\n              : {},\n      touchedFields: keepStateOptions.keepTouched\n        ? _formState.touchedFields\n        : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n        ? _formState.isSubmitSuccessful\n        : false,\n      isSubmitting: false,\n    });\n  };\n\n  const reset: UseFormReset<TFieldValues> = (formValues, keepStateOptions) =>\n    _reset(\n      isFunction(formValues)\n        ? (formValues as Function)(_formValues as TFieldValues)\n        : formValues,\n      keepStateOptions,\n    );\n\n  const setFocus: UseFormSetFocus<TFieldValues> = (name, options = {}) => {\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs\n        ? fieldReference.refs[0]\n        : fieldReference.ref;\n\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect &&\n          isFunction(fieldRef.select) &&\n          fieldRef.select();\n      }\n    }\n  };\n\n  const _setFormState = (\n    updatedFormState: Partial<FormState<TFieldValues>>,\n  ) => {\n    _formState = {\n      ..._formState,\n      ...updatedFormState,\n    };\n  };\n\n  const _resetDefaultValues = () =>\n    isFunction(_options.defaultValues) &&\n    (_options.defaultValues as Function)().then((values: TFieldValues) => {\n      reset(values, _options.resetOptions);\n      _subjects.state.next({\n        isLoading: false,\n      });\n    });\n\n  const methods = {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _subscribe,\n      _runSchema,\n      _getWatch,\n      _getDirty,\n      _setValid,\n      _setFieldArray,\n      _setDisabledField,\n      _setErrors,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _removeUnmounted,\n      _disableForm,\n      _subjects,\n      _proxyFormState,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = {\n          ..._options,\n          ...value,\n        };\n      },\n    },\n    subscribe,\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState,\n  };\n\n  return {\n    ...methods,\n    formControl: methods,\n  };\n}\n", "import { Field } from '../types';\n\nexport default (options: Field['_f']) =>\n  options.mount &&\n  (options.required ||\n    options.min ||\n    options.max ||\n    options.maxLength ||\n    options.minLength ||\n    options.pattern ||\n    options.validate);\n", "import { ValidationModeFlags } from '../types';\n\nexport default (\n  isBlurEvent: boolean,\n  isTouched: boolean,\n  isSubmitted: boolean,\n  reValidateMode: {\n    isOnBlur: boolean;\n    isOnChange: boolean;\n  },\n  mode: Partial<ValidationModeFlags>,\n) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\n", "import convertToArrayPayload from '../utils/convertToArrayPayload';\n\nexport default <T extends string | string[] | undefined>(\n  name?: T,\n  signalName?: string,\n  exact?: boolean,\n) =>\n  !name ||\n  !signalName ||\n  name === signalName ||\n  convertToArrayPayload(name).some(\n    (currentName) =>\n      currentName &&\n      (exact\n        ? currentName === signalName\n        : currentName.startsWith(signalName) ||\n          signalName.startsWith(currentName)),\n  );\n", "import { FieldElement } from '../types';\n\nimport isCheckBoxInput from './isCheckBoxInput';\nimport isRadioInput from './isRadioInput';\n\nexport default (ref: FieldElement): ref is HTMLInputElement =>\n  isRadioInput(ref) || isCheckBoxInput(ref);\n", "import compact from '../utils/compact';\nimport get from '../utils/get';\nimport unset from '../utils/unset';\n\nexport default <T>(ref: T, name: string) =>\n  !compact(get(ref, name)).length && unset(ref, name);\n", "export default () => {\n  const d =\n    typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n\n    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n  });\n};\n", "import { FieldArrayMethodProps, InternalFieldName } from '../types';\nimport isUndefined from '../utils/isUndefined';\n\nexport default (\n  name: InternalFieldName,\n  index: number,\n  options: FieldArrayMethodProps = {},\n): string =>\n  options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n      `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...data,\n  ...convertToArrayPayload(value),\n];\n", "export default <T>(value: T | T[]): undefined[] | undefined =>\n  Array.isArray(value) ? value.map(() => undefined) : undefined;\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default function insert<T>(data: T[], index: number): (T | undefined)[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value: T | T[],\n): T[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value?: T | T[],\n): (T | undefined)[] {\n  return [\n    ...data.slice(0, index),\n    ...convertToArrayPayload(value),\n    ...data.slice(index),\n  ];\n}\n", "import isUndefined from './isUndefined';\n\nexport default <T>(\n  data: (T | undefined)[],\n  from: number,\n  to: number,\n): (T | undefined)[] => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n\n  return data;\n};\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...convertToArrayPayload(value),\n  ...convertToArrayPayload(data),\n];\n", "import compact from './compact';\nimport convertToArrayPayload from './convertToArrayPayload';\nimport isUndefined from './isUndefined';\n\nfunction removeAtIndexes<T>(data: T[], indexes: number[]): T[] {\n  let i = 0;\n  const temp = [...data];\n\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n\n  return compact(temp).length ? temp : [];\n}\n\nexport default <T>(data: T[], index?: number | number[]): T[] =>\n  isUndefined(index)\n    ? []\n    : removeAtIndexes(\n        data,\n        (convertToArrayPayload(index) as number[]).sort((a, b) => a - b),\n      );\n", "export default <T>(data: T[], indexA: number, indexB: number): void => {\n  [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n", "export default <T>(fieldValues: T[], index: number, value: T) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport deepEqual from './utils/deepEqual';\nimport isEmptyObject from './utils/isEmptyObject';\nimport isFunction from './utils/isFunction';\nimport { createFormControl } from './logic';\nimport { FieldValues, FormState, UseFormProps, UseFormReturn } from './types';\n\nconst useIsomorphicLayoutEffect =\n  typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useForm<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFormProps<TFieldValues, TContext, TTransformedValues> = {},\n): UseFormReturn<TFieldValues, TContext, TTransformedValues> {\n  const _formControl = React.useRef<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues> | undefined\n  >(undefined);\n  const _values = React.useRef<typeof props.values>(undefined);\n  const [formState, updateFormState] = React.useState<FormState<TFieldValues>>({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    validatingFields: {},\n    errors: props.errors || {},\n    disabled: props.disabled || false,\n    isReady: false,\n    defaultValues: isFunction(props.defaultValues)\n      ? undefined\n      : props.defaultValues,\n  });\n\n  if (!_formControl.current) {\n    _formControl.current = {\n      ...(props.formControl ? props.formControl : createFormControl(props)),\n      formState,\n    };\n\n    if (\n      props.formControl &&\n      props.defaultValues &&\n      !isFunction(props.defaultValues)\n    ) {\n      props.formControl.reset(props.defaultValues, props.resetOptions);\n    }\n  }\n\n  const control = _formControl.current.control;\n  control._options = props;\n\n  useIsomorphicLayoutEffect(() => {\n    const sub = control._subscribe({\n      formState: control._proxyFormState,\n      callback: () => updateFormState({ ...control._formState }),\n      reRenderRoot: true,\n    });\n\n    updateFormState((data) => ({\n      ...data,\n      isReady: true,\n    }));\n\n    control._formState.isReady = true;\n\n    return sub;\n  }, [control]);\n\n  React.useEffect(\n    () => control._disableForm(props.disabled),\n    [control, props.disabled],\n  );\n\n  React.useEffect(() => {\n    if (props.mode) {\n      control._options.mode = props.mode;\n    }\n    if (props.reValidateMode) {\n      control._options.reValidateMode = props.reValidateMode;\n    }\n    if (props.errors && !isEmptyObject(props.errors)) {\n      control._setErrors(props.errors);\n    }\n  }, [control, props.errors, props.mode, props.reValidateMode]);\n\n  React.useEffect(() => {\n    props.shouldUnregister &&\n      control._subjects.state.next({\n        values: control._getWatch(),\n      });\n  }, [control, props.shouldUnregister]);\n\n  React.useEffect(() => {\n    if (control._proxyFormState.isDirty) {\n      const isDirty = control._getDirty();\n      if (isDirty !== formState.isDirty) {\n        control._subjects.state.next({\n          isDirty,\n        });\n      }\n    }\n  }, [control, formState.isDirty]);\n\n  React.useEffect(() => {\n    if (props.values && !deepEqual(props.values, _values.current)) {\n      control._reset(props.values, control._options.resetOptions);\n      _values.current = props.values;\n      updateFormState((state) => ({ ...state }));\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [control, props.values]);\n\n  React.useEffect(() => {\n    if (!control._state.mount) {\n      control._setValid();\n      control._state.mount = true;\n    }\n\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next({ ...control._formState });\n    }\n\n    control._removeUnmounted();\n  });\n\n  _formControl.current.formState = getProxyFormState(formState, control);\n\n  return _formControl.current;\n}\n", "import React from 'react';\n\nimport generateId from './logic/generateId';\nimport getFocusFieldName from './logic/getFocusFieldName';\nimport getValidationModes from './logic/getValidationModes';\nimport isWatched from './logic/isWatched';\nimport iterateFieldsByAction from './logic/iterateFieldsByAction';\nimport updateFieldArrayRootError from './logic/updateFieldArrayRootError';\nimport validateField from './logic/validateField';\nimport appendAt from './utils/append';\nimport cloneObject from './utils/cloneObject';\nimport convertToArrayPayload from './utils/convertToArrayPayload';\nimport fillEmptyArray from './utils/fillEmptyArray';\nimport get from './utils/get';\nimport insertAt from './utils/insert';\nimport isEmptyObject from './utils/isEmptyObject';\nimport moveArrayAt from './utils/move';\nimport prependAt from './utils/prepend';\nimport removeArrayAt from './utils/remove';\nimport set from './utils/set';\nimport swapArrayAt from './utils/swap';\nimport unset from './utils/unset';\nimport updateAt from './utils/update';\nimport { VALIDATION_MODE } from './constants';\nimport {\n  Control,\n  Field,\n  FieldArray,\n  FieldArrayMethodProps,\n  FieldArrayPath,\n  FieldArrayWithId,\n  FieldErrors,\n  FieldPath,\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  RegisterOptions,\n  UseFieldArrayProps,\n  UseFieldArrayReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFieldArray<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldArrayName extends\n    FieldArrayPath<TFieldValues> = FieldArrayPath<TFieldValues>,\n  TKeyName extends string = 'id',\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFieldArrayProps<\n    TFieldValues,\n    TFieldArrayName,\n    TKeyName,\n    TTransformedValues\n  >,\n): UseFieldArrayReturn<TFieldValues, TFieldArrayName, TKeyName> {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister,\n    rules,\n  } = props;\n  const [fields, setFields] = React.useState(control._getFieldArray(name));\n  const ids = React.useRef<string[]>(\n    control._getFieldArray(name).map(generateId),\n  );\n  const _fieldIds = React.useRef(fields);\n  const _name = React.useRef(name);\n  const _actioned = React.useRef(false);\n\n  _name.current = name;\n  _fieldIds.current = fields;\n  control._names.array.add(name);\n\n  rules &&\n    (control as Control<TFieldValues, any, TTransformedValues>).register(\n      name as FieldPath<TFieldValues>,\n      rules as RegisterOptions<TFieldValues>,\n    );\n\n  React.useEffect(\n    () =>\n      control._subjects.array.subscribe({\n        next: ({\n          values,\n          name: fieldArrayName,\n        }: {\n          values?: FieldValues;\n          name?: InternalFieldName;\n        }) => {\n          if (fieldArrayName === _name.current || !fieldArrayName) {\n            const fieldValues = get(values, _name.current);\n            if (Array.isArray(fieldValues)) {\n              setFields(fieldValues);\n              ids.current = fieldValues.map(generateId);\n            }\n          }\n        },\n      }).unsubscribe,\n    [control],\n  );\n\n  const updateValues = React.useCallback(\n    <\n      T extends Partial<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >[],\n    >(\n      updatedFieldArrayValues: T,\n    ) => {\n      _actioned.current = true;\n      control._setFieldArray(name, updatedFieldArrayValues);\n    },\n    [control, name],\n  );\n\n  const append = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = appendAt(\n      control._getFieldArray(name),\n      appendValue,\n    );\n    control._names.focus = getFocusFieldName(\n      name,\n      updatedFieldArrayValues.length - 1,\n      options,\n    );\n    ids.current = appendAt(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const prepend = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prependAt(\n      control._getFieldArray(name),\n      prependValue,\n    );\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prependAt(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const remove = (index?: number | number[]) => {\n    const updatedFieldArrayValues: Partial<\n      FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n    >[] = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    !Array.isArray(get(control._fields, name)) &&\n      set(control._fields, name, undefined);\n    control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index,\n    });\n  };\n\n  const insert = (\n    index: number,\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insertAt(\n      control._getFieldArray(name),\n      index,\n      insertValue,\n    );\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insertAt(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, insertAt, {\n      argA: index,\n      argB: fillEmptyArray(value),\n    });\n  };\n\n  const swap = (indexA: number, indexB: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      swapArrayAt,\n      {\n        argA: indexA,\n        argB: indexB,\n      },\n      false,\n    );\n  };\n\n  const move = (from: number, to: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      moveArrayAt,\n      {\n        argA: from,\n        argB: to,\n      },\n      false,\n    );\n  };\n\n  const update = (\n    index: number,\n    value: FieldArray<TFieldValues, TFieldArrayName>,\n  ) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(\n      control._getFieldArray<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >(name),\n      index,\n      updateValue as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>,\n    );\n    ids.current = [...updatedFieldArrayValues].map((item, i) =>\n      !item || i === index ? generateId() : ids.current[i],\n    );\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      updateAt,\n      {\n        argA: index,\n        argB: updateValue,\n      },\n      true,\n      false,\n    );\n  };\n\n  const replace = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n  ) => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(\n      name,\n      [...updatedFieldArrayValues],\n      <T>(data: T): T => data,\n      {},\n      true,\n      false,\n    );\n  };\n\n  React.useEffect(() => {\n    control._state.action = false;\n\n    isWatched(name, control._names) &&\n      control._subjects.state.next({\n        ...control._formState,\n      } as FormState<TFieldValues>);\n\n    if (\n      _actioned.current &&\n      (!getValidationModes(control._options.mode).isOnSubmit ||\n        control._formState.isSubmitted) &&\n      !getValidationModes(control._options.reValidateMode).isOnSubmit\n    ) {\n      if (control._options.resolver) {\n        control._runSchema([name]).then((result) => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n\n          if (\n            existingError\n              ? (!error && existingError.type) ||\n                (error &&\n                  (existingError.type !== error.type ||\n                    existingError.message !== error.message))\n              : error && error.type\n          ) {\n            error\n              ? set(control._formState.errors, name, error)\n              : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors as FieldErrors<TFieldValues>,\n            });\n          }\n        });\n      } else {\n        const field: Field = get(control._fields, name);\n        if (\n          field &&\n          field._f &&\n          !(\n            getValidationModes(control._options.reValidateMode).isOnSubmit &&\n            getValidationModes(control._options.mode).isOnSubmit\n          )\n        ) {\n          validateField(\n            field,\n            control._names.disabled,\n            control._formValues,\n            control._options.criteriaMode === VALIDATION_MODE.all,\n            control._options.shouldUseNativeValidation,\n            true,\n          ).then(\n            (error) =>\n              !isEmptyObject(error) &&\n              control._subjects.state.next({\n                errors: updateFieldArrayRootError(\n                  control._formState.errors as FieldErrors<TFieldValues>,\n                  error,\n                  name,\n                ) as FieldErrors<TFieldValues>,\n              }),\n          );\n        }\n      }\n    }\n\n    control._subjects.state.next({\n      name,\n      values: cloneObject(control._formValues) as TFieldValues,\n    });\n\n    control._names.focus &&\n      iterateFieldsByAction(control._fields, (ref, key: string) => {\n        if (\n          control._names.focus &&\n          key.startsWith(control._names.focus) &&\n          ref.focus\n        ) {\n          ref.focus();\n          return 1;\n        }\n        return;\n      });\n\n    control._names.focus = '';\n\n    control._setValid();\n    _actioned.current = false;\n  }, [fields, name, control]);\n\n  React.useEffect(() => {\n    !get(control._formValues, name) && control._setFieldArray(name);\n\n    return () => {\n      const updateMounted = (name: InternalFieldName, value: boolean) => {\n        const field: Field = get(control._fields, name);\n        if (field && field._f) {\n          field._f.mount = value;\n        }\n      };\n\n      control._options.shouldUnregister || shouldUnregister\n        ? control.unregister(name as FieldPath<TFieldValues>)\n        : updateMounted(name, false);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n\n  return {\n    swap: React.useCallback(swap, [updateValues, name, control]),\n    move: React.useCallback(move, [updateValues, name, control]),\n    prepend: React.useCallback(prepend, [updateValues, name, control]),\n    append: React.useCallback(append, [updateValues, name, control]),\n    remove: React.useCallback(remove, [updateValues, name, control]),\n    insert: React.useCallback(insert, [updateValues, name, control]),\n    update: React.useCallback(update, [updateValues, name, control]),\n    replace: React.useCallback(replace, [updateValues, name, control]),\n    fields: React.useMemo(\n      () =>\n        fields.map((field, index) => ({\n          ...field,\n          [keyName]: ids.current[index] || generateId(),\n        })) as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>[],\n      [fields, keyName],\n    ),\n  };\n}\n"], "names": ["isCheckBoxInput", "element", "type", "isDateObject", "value", "Date", "isNullOrUndefined", "isObjectType", "isObject", "Array", "isArray", "getEventValue", "event", "target", "checked", "isNameInFieldArray", "names", "name", "has", "substring", "search", "getNodeParentName", "isWeb", "window", "HTMLElement", "document", "cloneObject", "data", "copy", "isFileListInstance", "FileList", "Set", "Blob", "tempObject", "prototypeCopy", "constructor", "prototype", "hasOwnProperty", "isPlainObject", "key", "compact", "filter", "Boolean", "isUndefined", "val", "undefined", "get", "object", "path", "defaultValue", "result", "split", "reduce", "isBoolean", "is<PERSON>ey", "test", "stringToPath", "input", "replace", "set", "index", "temp<PERSON>ath", "length", "lastIndex", "newValue", "objValue", "isNaN", "EVENTS", "VALIDATION_MODE", "INPUT_VALIDATION_RULES", "HookFormContext", "React", "createContext", "useFormContext", "useContext", "getProxyFormState", "formState", "control", "localProxyFormState", "isRoot", "defaultValues", "_defaultValues", "Object", "defineProperty", "_key", "_proxyFormState", "isPrimitive", "deepEqual", "object1", "object2", "getTime", "keys1", "keys", "keys2", "val1", "includes", "val2", "useDeepEqualEffect", "effect", "deps", "ref", "useRef", "current", "useEffect", "useFormState", "props", "methods", "disabled", "exact", "updateFormState", "useState", "_formState", "_localProxyFormState", "isDirty", "isLoading", "dirtyFields", "touchedFields", "validatingFields", "isValidating", "<PERSON><PERSON><PERSON><PERSON>", "errors", "_subscribe", "callback", "_setValid", "useMemo", "isString", "generateWatchOutput", "_names", "formValues", "isGlobal", "watch", "add", "map", "fieldName", "watchAll", "useWatch", "updateValue", "_getWatch", "values", "_formValues", "_removeUnmounted", "useController", "shouldUnregister", "isArrayField", "array", "_props", "_registerProps", "register", "rules", "fieldState", "defineProperties", "invalid", "enumerable", "isTouched", "error", "onChange", "useCallback", "onBlur", "elm", "field", "_fields", "_f", "focus", "select", "setCustomValidity", "message", "reportValidity", "_shouldUnregisterField", "_options", "updateMounted", "mount", "_state", "action", "unregister", "_setDisabledField", "flatten", "obj", "output", "nested", "nested<PERSON><PERSON>", "POST_REQUEST", "appendErrors", "validateAllFieldCriteria", "types", "convertToArrayPayload", "createSubject", "_observers", "observers", "next", "observer", "subscribe", "push", "unsubscribe", "o", "isEmptyObject", "isFileInput", "isFunction", "isHTMLElement", "owner", "ownerDocument", "defaultView", "isMultipleSelect", "isRadioInput", "live", "isConnected", "unset", "paths", "childObject", "updatePath", "slice", "baseGet", "isEmptyArray", "objectHasFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fields", "isParentNodeArray", "getDirtyFieldsFromDefaultValues", "dirtyField<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDirty<PERSON>ields", "defaultResult", "validResult", "getCheckboxValue", "options", "option", "attributes", "getFieldValueAs", "valueAsNumber", "valueAsDate", "setValueAs", "NaN", "defaultReturn", "getRadioValue", "previous", "getFieldValue", "files", "refs", "selectedOptions", "isCheckBox", "isRegex", "RegExp", "getRuleValue", "rule", "source", "getValidationModes", "mode", "isOnSubmit", "isOnBlur", "isOnChange", "isOnAll", "isOnTouch", "ASYNC_FUNCTION", "isWatched", "isBlurEvent", "some", "watchName", "startsWith", "iterateFieldsByAction", "fieldsNames", "abort<PERSON><PERSON><PERSON>", "current<PERSON><PERSON>", "schemaErrorLookup", "join", "found<PERSON><PERSON>r", "pop", "updateFieldArrayRootError", "fieldArrayErrors", "isMessage", "getValidateError", "every", "getValueAndMessage", "validationData", "validateField", "async", "disabled<PERSON>ieldN<PERSON>s", "shouldUseNativeValidation", "isFieldArray", "required", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "min", "max", "pattern", "validate", "inputValue", "inputRef", "isRadio", "isRadioOrCheckbox", "isEmpty", "appendErrors<PERSON><PERSON><PERSON>", "bind", "getMinMaxMessage", "exceedMax", "maxLengthMessage", "minLengthMessage", "maxType", "minType", "exceedMin", "maxOutput", "minOutput", "valueDate", "convertTimeToDate", "time", "toDateString", "isTime", "isWeek", "valueNumber", "maxLengthOutput", "minLengthOutput", "patternValue", "match", "validateError", "validationResult", "defaultOptions", "reValidateMode", "shouldFocusError", "createFormControl", "submitCount", "isReady", "isSubmitted", "isSubmitting", "isSubmitSuccessful", "delayError<PERSON><PERSON><PERSON>", "unMount", "timer", "_proxySubscribeFormState", "_subjects", "state", "validationModeBeforeSubmit", "validationModeAfterSubmit", "shouldDisplayAllAssociatedErrors", "criteriaMode", "shouldUpdateValid", "resolver", "_runSchema", "executeBuiltInValidation", "_updateIsValidating", "from", "for<PERSON>ach", "updateValidAndValue", "shouldSkipSetValueAs", "defaultChecked", "setFieldValue", "updateTouchAndDirty", "fieldValue", "should<PERSON>irty", "shouldRender", "shouldUpdateField", "is<PERSON>revious<PERSON><PERSON>y", "_getDirty", "isCurrentFieldPristine", "isPreviousFieldTouched", "shouldRenderByError", "previousFieldError", "delayError", "updateErrors", "wait", "clearTimeout", "setTimeout", "updatedFormState", "context", "getResolverOptions", "should<PERSON>nly<PERSON><PERSON><PERSON><PERSON>d", "valid", "isFieldArrayRoot", "isPromiseFunction", "fieldReference", "find", "validateFunction", "fieldError", "getV<PERSON>ues", "optionRef", "selected", "checkboxRef", "radioRef", "shouldTouch", "shouldValidate", "trigger", "set<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "setValue", "cloneValue", "isFieldValueUpdated", "_updateIsFieldValueUpdated", "Number", "shouldSkipValidation", "skipValidation", "watched", "previousErrorLookupResult", "errorLookupResult", "_focusInput", "fieldNames", "executeSchemaAndUpdateState", "Promise", "all", "shouldFocus", "getFieldState", "setError", "currentError", "currentRef", "restOfErrorTree", "signalName", "currentName", "formStateData", "shouldRenderFormState", "_setFormState", "reRenderRoot", "delete", "keepValue", "keepError", "keep<PERSON>irty", "keepTouched", "keepIsValidating", "keepDefaultValue", "keepIsValid", "disabledIsDefined", "progressive", "fieldRef", "querySelectorAll", "radioOrCheckbox", "_focusError", "handleSubmit", "onValid", "onInvalid", "e", "onValidError", "preventDefault", "persist", "field<PERSON><PERSON><PERSON>", "size", "_reset", "keepStateOptions", "updatedValues", "cloneUpdatedValues", "isEmptyResetValues", "keepDefaultValues", "keepV<PERSON>ues", "keepDirtyV<PERSON>ues", "fieldsToCheck", "form", "closest", "reset", "keepSubmitCount", "keepIsSubmitted", "keepErrors", "keepIsSubmitSuccessful", "_setFieldArray", "method", "args", "shouldSetValues", "shouldUpdateFieldsAndState", "argA", "argB", "unsetEmptyArray", "_setErrors", "_getFieldArray", "_resetDefaultValues", "then", "resetOptions", "_disableForm", "payload", "reset<PERSON>ield", "clearErrors", "inputName", "setFocus", "shouldSelect", "formControl", "generateId", "d", "performance", "now", "c", "r", "Math", "random", "toString", "getFocusFieldName", "focusName", "focusIndex", "appendAt", "fillEmptyArray", "insert", "moveArrayAt", "to", "splice", "prependAt", "removeArrayAt", "indexes", "i", "temp", "removeAtIndexes", "sort", "a", "b", "swapArrayAt", "indexA", "indexB", "updateAt", "useIsomorphicLayoutEffect", "useLayoutEffect", "render", "mounted", "setMounted", "onSubmit", "children", "headers", "encType", "onError", "onSuccess", "validateStatus", "rest", "submit", "<PERSON><PERSON><PERSON><PERSON>", "formData", "FormData", "formDataJson", "JSON", "stringify", "_a", "flattenForm<PERSON><PERSON>ues", "append", "shouldStringifySubmissionData", "response", "fetch", "String", "body", "status", "createElement", "Fragment", "noValidate", "Provider", "keyName", "setFields", "ids", "_fieldIds", "_name", "_actioned", "fieldArrayName", "updateValues", "updatedFieldArrayValues", "existingError", "swap", "move", "prepend", "prependValue", "appendValue", "remove", "insertValue", "insertAt", "update", "item", "_formControl", "_values", "sub"], "mappings": "kTAEAA,EAAgBC,GACG,aAAjBA,EAAQC,KCHVC,EAAgBC,GAAkCA,aAAiBC,KCAnEC,EAAgBF,GAAuD,MAATA,ECGvD,MAAMG,EAAgBH,GACV,iBAAVA,EAET,IAAAI,EAAkCJ,IAC/BE,EAAkBF,KAClBK,MAAMC,QAAQN,IACfG,EAAaH,KACZD,EAAaC,GCLDO,EAACC,GACdJ,EAASI,IAAWA,EAAgBC,OAChCb,EAAiBY,EAAgBC,QAC9BD,EAAgBC,OAAOC,QACvBF,EAAgBC,OAAOT,MAC1BQ,ECNNG,EAAe,CAACC,EAA+BC,IAC7CD,EAAME,ICLO,CAACD,GACdA,EAAKE,UAAU,EAAGF,EAAKG,OAAO,iBAAmBH,EDIvCI,CAAkBJ,IELfK,EAAkB,oBAAXC,aACU,IAAvBA,OAAOC,aACM,oBAAbC,SCEe,SAAAC,EAAeC,GACrC,IAAIC,EACJ,MAAMlB,EAAUD,MAAMC,QAAQiB,GACxBE,EACgB,oBAAbC,UAA2BH,aAAgBG,SAEpD,GAAIH,aAAgBtB,KAClBuB,EAAO,IAAIvB,KAAKsB,QACX,GAAIA,aAAgBI,IACzBH,EAAO,IAAIG,IAAIJ,OACV,IACHL,IAAUK,aAAgBK,MAAQH,KACnCnB,IAAWF,EAASmB,GAcrB,OAAOA,EAVP,GAFAC,EAAOlB,EAAU,GAAK,CAAE,EAEnBA,GClBM,CAACuB,IACd,MAAMC,EACJD,EAAWE,aAAeF,EAAWE,YAAYC,UAEnD,OACE5B,EAAS0B,IAAkBA,EAAcG,eAAe,gBAAgB,EDavDC,CAAcX,GAG7B,IAAK,MAAMY,KAAOZ,EACZA,EAAKU,eAAeE,KACtBX,EAAKW,GAAOb,EAAYC,EAAKY,UAJjCX,EAAOD,EAYX,OAAOC,CACT,CElCA,IAAAY,EAAwBpC,GACtBK,MAAMC,QAAQN,GAASA,EAAMqC,OAAOC,SAAW,GCDjDC,EAAgBC,QAA2CC,IAARD,ECKnDE,EAAe,CACbC,EACAC,EACAC,KAEA,IAAKD,IAASxC,EAASuC,GACrB,OAAOE,EAGT,MAAMC,EAASV,EAAQQ,EAAKG,MAAM,cAAcC,QAC9C,CAACF,EAAQX,IACPjC,EAAkB4C,GAAUA,EAASA,EAAOX,IAC9CQ,GAGF,OAAOJ,EAAYO,IAAWA,IAAWH,EACrCJ,EAAYI,EAAOC,IACjBC,EACAF,EAAOC,GACTE,CAAM,ECxBZG,EAAgBjD,GAAsD,kBAAVA,ECA7CkD,EAAClD,GAAkB,QAAQmD,KAAKnD,GCE/CoD,EAAgBC,GACdjB,EAAQiB,EAAMC,QAAQ,YAAa,IAAIP,MAAM,UCG/CQ,EAAe,CACbZ,EACAC,EACA5C,KAEA,IAAIwD,GAAU,EACd,MAAMC,EAAWP,EAAMN,GAAQ,CAACA,GAAQQ,EAAaR,GAC/Cc,EAASD,EAASC,OAClBC,EAAYD,EAAS,EAE3B,OAASF,EAAQE,GAAQ,CACvB,MAAMvB,EAAMsB,EAASD,GACrB,IAAII,EAAW5D,EAEf,GAAIwD,IAAUG,EAAW,CACvB,MAAME,EAAWlB,EAAOR,GACxByB,EACExD,EAASyD,IAAaxD,MAAMC,QAAQuD,GAChCA,EACCC,OAAOL,EAASD,EAAQ,IAEvB,CAAE,EADF,GAIV,GAAY,cAARrB,GAA+B,gBAARA,GAAiC,cAARA,EAClD,OAGFQ,EAAOR,GAAOyB,EACdjB,EAASA,EAAOR,KCnCb,MAAM4B,EACL,OADKA,EAEA,WAFAA,EAGH,SAGGC,EACH,SADGA,EAED,WAFCA,EAGD,WAHCA,EAIA,YAJAA,EAKN,MAGMC,EACN,MADMA,EAEN,MAFMA,EAGA,YAHAA,EAIA,YAJAA,EAKF,UALEA,EAMD,WANCA,EAOD,WCjBNC,EAAkBC,EAAMC,cAAoC,MAgCrDC,EAAiB,IAK5BF,EAAMG,WAAWJ,GCtCnB,IAAeK,EAAA,CAKbC,EACAC,EACAC,EACAC,GAAS,KAET,MAAM7B,EAAS,CACb8B,cAAeH,EAAQI,gBAGzB,IAAK,MAAM1C,KAAOqC,EAChBM,OAAOC,eAAejC,EAAQX,EAAK,CACjCO,IAAK,KACH,MAAMsC,EAAO7C,EAOb,OALIsC,EAAQQ,gBAAgBD,KAAUhB,IACpCS,EAAQQ,gBAAgBD,IAASL,GAAUX,GAG7CU,IAAwBA,EAAoBM,IAAQ,GAC7CR,EAAUQ,EAAK,IAK5B,OAAOlC,CAAM,EC3BfoC,EAAgBlF,GACdE,EAAkBF,KAAWG,EAAaH,GCD9B,SAAUmF,EAAUC,EAAcC,GAC9C,GAAIH,EAAYE,IAAYF,EAAYG,GACtC,OAAOD,IAAYC,EAGrB,GAAItF,EAAaqF,IAAYrF,EAAasF,GACxC,OAAOD,EAAQE,YAAcD,EAAQC,UAGvC,MAAMC,EAAQT,OAAOU,KAAKJ,GACpBK,EAAQX,OAAOU,KAAKH,GAE1B,GAAIE,EAAM7B,SAAW+B,EAAM/B,OACzB,OAAO,EAGT,IAAK,MAAMvB,KAAOoD,EAAO,CACvB,MAAMG,EAAON,EAAQjD,GAErB,IAAKsD,EAAME,SAASxD,GAClB,OAAO,EAGT,GAAY,QAARA,EAAe,CACjB,MAAMyD,EAAOP,EAAQlD,GAErB,GACGpC,EAAa2F,IAAS3F,EAAa6F,IACnCxF,EAASsF,IAAStF,EAASwF,IAC3BvF,MAAMC,QAAQoF,IAASrF,MAAMC,QAAQsF,IACjCT,EAAUO,EAAME,GACjBF,IAASE,EAEb,OAAO,GAKb,OAAO,CACT,CCxCO,MAAMC,EAAqB,CAChCC,EACAC,KAEA,MAAMC,EAAM7B,EAAM8B,OAAUF,GAEvBZ,EAAUY,EAAMC,EAAIE,WACvBF,EAAIE,QAAUH,GAIhB5B,EAAMgC,UAAUL,EAAQE,EAAIE,QAAQ,EC2BhC,SAAUE,EAIdC,GAEA,MAAMC,EAAUjC,KACVI,QAAEA,EAAU6B,EAAQ7B,QAAO8B,SAAEA,EAAQ1F,KAAEA,EAAI2F,MAAEA,GAAUH,GAAS,CAAE,GACjE7B,EAAWiC,GAAmBtC,EAAMuC,SAASjC,EAAQkC,YACtDC,EAAuBzC,EAAM8B,OAAO,CACxCY,SAAS,EACTC,WAAW,EACXC,aAAa,EACbC,eAAe,EACfC,kBAAkB,EAClBC,cAAc,EACdC,SAAS,EACTC,QAAQ,IAwBV,OArBAvB,GACE,IACEpB,EAAQ4C,WAAW,CACjBxG,KAAMA,EACN2D,UAAWoC,EAAqBV,QAChCM,QACAc,SAAW9C,KACR+B,GACCE,EAAgB,IACXhC,EAAQkC,cACRnC,GACH,KAGV,CAAC3D,EAAM0F,EAAUC,IAGnBrC,EAAMgC,WAAU,KACdS,EAAqBV,QAAQiB,SAAW1C,EAAQ8C,WAAU,EAAK,GAC9D,CAAC9C,IAEGN,EAAMqD,SACX,IACEjD,EACEC,EACAC,EACAmC,EAAqBV,SACrB,IAEJ,CAAC1B,EAAWC,GAEhB,CC7FA,IAAAgD,EAAgBzH,GAAqD,iBAAVA,ECI5C0H,EAAA,CACb9G,EACA+G,EACAC,EACAC,EACAhF,IAEI4E,EAAS7G,IACXiH,GAAYF,EAAOG,MAAMC,IAAInH,GACtB8B,EAAIkF,EAAYhH,EAAOiC,IAG5BxC,MAAMC,QAAQM,GACTA,EAAMoH,KACVC,IACCJ,GAAYF,EAAOG,MAAMC,IAAIE,GAAYvF,EAAIkF,EAAYK,OAK/DJ,IAAaF,EAAOO,UAAW,GAExBN,GCsHH,SAAUO,EACd9B,GAEA,MAAMC,EAAUjC,KACVI,QACJA,EAAU6B,EAAQ7B,QAAO5D,KACzBA,EAAIgC,aACJA,EAAY0D,SACZA,EAAQC,MACRA,GACEH,GAAS,CAAE,GAERrG,EAAOoI,GAAejE,EAAMuC,SACjCjC,EAAQ4D,UACNxH,EACAgC,IA6BJ,OAzBAgD,GACE,IACEpB,EAAQ4C,WAAW,CACjBxG,KAAMA,EACN2D,UAAW,CACT8D,QAAQ,GAEV9B,QACAc,SAAW9C,IACR+B,GACD6B,EACEV,EACE7G,EACA4D,EAAQkD,OACRnD,EAAU8D,QAAU7D,EAAQ8D,aAC5B,EACA1F,OAIV,CAAChC,EAAMgC,EAAc0D,EAAUC,IAGjCrC,EAAMgC,WAAU,IAAM1B,EAAQ+D,qBAEvBxI,CACT,CC7IM,SAAUyI,EAKdpC,GAEA,MAAMC,EAAUjC,KACVxD,KAAEA,EAAI0F,SAAEA,EAAQ9B,QAAEA,EAAU6B,EAAQ7B,QAAOiE,iBAAEA,GAAqBrC,EAClEsC,EAAehI,EAAmB8D,EAAQkD,OAAOiB,MAAO/H,GACxDb,EAAQmI,EAAS,CACrB1D,UACA5D,OACAgC,aAAcH,EACZ+B,EAAQ8D,YACR1H,EACA6B,EAAI+B,EAAQI,eAAgBhE,EAAMwF,EAAMxD,eAE1C2D,OAAO,IAEHhC,EAAY4B,EAAa,CAC7B3B,UACA5D,OACA2F,OAAO,IAGHqC,EAAS1E,EAAM8B,OAAOI,GACtByC,EAAiB3E,EAAM8B,OAC3BxB,EAAQsE,SAASlI,EAAM,IAClBwF,EAAM2C,MACThJ,WACIiD,EAAUoD,EAAME,UAAY,CAAEA,SAAUF,EAAME,UAAa,MAI7D0C,EAAa9E,EAAMqD,SACvB,IACE1C,OAAOoE,iBACL,GACA,CACEC,QAAS,CACPC,YAAY,EACZ1G,IAAK,MAAQA,EAAI8B,EAAU4C,OAAQvG,IAErCgG,QAAS,CACPuC,YAAY,EACZ1G,IAAK,MAAQA,EAAI8B,EAAUuC,YAAalG,IAE1CwI,UAAW,CACTD,YAAY,EACZ1G,IAAK,MAAQA,EAAI8B,EAAUwC,cAAenG,IAE5CqG,aAAc,CACZkC,YAAY,EACZ1G,IAAK,MAAQA,EAAI8B,EAAUyC,iBAAkBpG,IAE/CyI,MAAO,CACLF,YAAY,EACZ1G,IAAK,IAAMA,EAAI8B,EAAU4C,OAAQvG,OAIzC,CAAC2D,EAAW3D,IAGR0I,EAAWpF,EAAMqF,aACpBhJ,GACCsI,EAAe5C,QAAQqD,SAAS,CAC9B9I,OAAQ,CACNT,MAAOO,EAAcC,GACrBK,KAAMA,GAERf,KAAMiE,KAEV,CAAClD,IAGG4I,EAAStF,EAAMqF,aACnB,IACEV,EAAe5C,QAAQuD,OAAO,CAC5BhJ,OAAQ,CACNT,MAAO0C,EAAI+B,EAAQ8D,YAAa1H,GAChCA,KAAMA,GAERf,KAAMiE,KAEV,CAAClD,EAAM4D,EAAQ8D,cAGXvC,EAAM7B,EAAMqF,aACfE,IACC,MAAMC,EAAQjH,EAAI+B,EAAQmF,QAAS/I,GAE/B8I,GAASD,IACXC,EAAME,GAAG7D,IAAM,CACb8D,MAAO,IAAMJ,EAAII,QACjBC,OAAQ,IAAML,EAAIK,SAClBC,kBAAoBC,GAClBP,EAAIM,kBAAkBC,GACxBC,eAAgB,IAAMR,EAAIQ,qBAIhC,CAACzF,EAAQmF,QAAS/I,IAGd8I,EAAQxF,EAAMqD,SAClB,KAAO,CACL3G,OACAb,WACIiD,EAAUsD,IAAa/B,EAAU+B,SACjC,CAAEA,SAAU/B,EAAU+B,UAAYA,GAClC,GACJgD,WACAE,SACAzD,SAEF,CAACnF,EAAM0F,EAAU/B,EAAU+B,SAAUgD,EAAUE,EAAQzD,EAAKhG,IAoD9D,OAjDAmE,EAAMgC,WAAU,KACd,MAAMgE,EACJ1F,EAAQ2F,SAAS1B,kBAAoBA,EAEvCjE,EAAQsE,SAASlI,EAAM,IAClBgI,EAAO3C,QAAQ8C,SACd/F,EAAU4F,EAAO3C,QAAQK,UACzB,CAAEA,SAAUsC,EAAO3C,QAAQK,UAC3B,KAGN,MAAM8D,EAAgB,CAACxJ,EAAyBb,KAC9C,MAAM2J,EAAejH,EAAI+B,EAAQmF,QAAS/I,GAEtC8I,GAASA,EAAME,KACjBF,EAAME,GAAGS,MAAQtK,IAMrB,GAFAqK,EAAcxJ,GAAM,GAEhBsJ,EAAwB,CAC1B,MAAMnK,EAAQsB,EAAYoB,EAAI+B,EAAQ2F,SAASxF,cAAe/D,IAC9D0C,EAAIkB,EAAQI,eAAgBhE,EAAMb,GAC9BuC,EAAYG,EAAI+B,EAAQ8D,YAAa1H,KACvC0C,EAAIkB,EAAQ8D,YAAa1H,EAAMb,GAMnC,OAFC2I,GAAgBlE,EAAQsE,SAASlI,GAE3B,MAEH8H,EACIwB,IAA2B1F,EAAQ8F,OAAOC,OAC1CL,GAEF1F,EAAQgG,WAAW5J,GACnBwJ,EAAcxJ,GAAM,EAAM,CAC/B,GACA,CAACA,EAAM4D,EAASkE,EAAcD,IAEjCvE,EAAMgC,WAAU,KACd1B,EAAQiG,kBAAkB,CACxBnE,WACA1F,QACA,GACD,CAAC0F,EAAU1F,EAAM4D,IAEbN,EAAMqD,SACX,KAAO,CACLmC,QACAnF,YACAyE,gBAEF,CAACU,EAAOnF,EAAWyE,GAEvB,CCpLA,MCzCa0B,EAAWC,IACtB,MAAMC,EAAsB,CAAE,EAE9B,IAAK,MAAM1I,KAAO2C,OAAOU,KAAKoF,GAC5B,GAAIzK,EAAayK,EAAIzI,KAAsB,OAAbyI,EAAIzI,GAAe,CAC/C,MAAM2I,EAASH,EAAQC,EAAIzI,IAE3B,IAAK,MAAM4I,KAAajG,OAAOU,KAAKsF,GAClCD,EAAO,GAAG1I,KAAO4I,KAAeD,EAAOC,QAGzCF,EAAO1I,GAAOyI,EAAIzI,GAItB,OAAO0I,CAAM,ECbTG,EAAe,OCArB,IAAeC,EAAA,CACbpK,EACAqK,EACA9D,EACAtH,EACAmK,IAEAiB,EACI,IACK9D,EAAOvG,GACVsK,MAAO,IACD/D,EAAOvG,IAASuG,EAAOvG,GAAOsK,MAAQ/D,EAAOvG,GAAOsK,MAAQ,CAAA,EAChErL,CAACA,GAAOmK,IAAW,IAGvB,CAAE,ECrBRmB,EAAmBpL,GAAcK,MAAMC,QAAQN,GAASA,EAAQ,CAACA,GCgBjEqL,EAAe,KACb,IAAIC,EAA4B,GAqBhC,MAAO,CACL,aAAIC,GACF,OAAOD,CACR,EACDE,KAvBYxL,IACZ,IAAK,MAAMyL,KAAYH,EACrBG,EAASD,MAAQC,EAASD,KAAKxL,IAsBjC0L,UAlBiBD,IACjBH,EAAWK,KAAKF,GACT,CACLG,YAAa,KACXN,EAAaA,EAAWjJ,QAAQwJ,GAAMA,IAAMJ,GAAS,IAezDG,YAVkB,KAClBN,EAAa,EAAE,EAUhB,ECzCHQ,EAAgB9L,GACdI,EAASJ,KAAW8E,OAAOU,KAAKxF,GAAO0D,OCHzCqI,EAAgBlM,GACG,SAAjBA,EAAQC,KCHVkM,EAAgBhM,GACG,mBAAVA,ECCMiM,EAACjM,IACd,IAAKkB,EACH,OAAO,EAGT,MAAMgL,EAAQlM,EAAUA,EAAsBmM,cAA6B,EAC3E,OACEnM,aACCkM,GAASA,EAAME,YAAcF,EAAME,YAAYhL,YAAcA,YAAY,ECR/DiL,GAACxM,GACG,oBAAjBA,EAAQC,KCDVwM,GAAgBzM,GACG,UAAjBA,EAAQC,KCCKyM,GAACvG,GAAaiG,EAAcjG,IAAQA,EAAIwG,YCsBzC,SAAUC,GAAM9J,EAAaC,GACzC,MAAM8J,EAAQrM,MAAMC,QAAQsC,GACxBA,EACAM,EAAMN,GACJ,CAACA,GACDQ,EAAaR,GAEb+J,EAA+B,IAAjBD,EAAMhJ,OAAef,EA3B3C,SAAiBA,EAAaiK,GAC5B,MAAMlJ,EAASkJ,EAAWC,MAAM,GAAG,GAAInJ,OACvC,IAAIF,EAAQ,EAEZ,KAAOA,EAAQE,GACbf,EAASJ,EAAYI,GAAUa,IAAUb,EAAOiK,EAAWpJ,MAG7D,OAAOb,CACT,CAkBoDmK,CAAQnK,EAAQ+J,GAE5DlJ,EAAQkJ,EAAMhJ,OAAS,EACvBvB,EAAMuK,EAAMlJ,GAclB,OAZImJ,UACKA,EAAYxK,GAIT,IAAVqB,IACEpD,EAASuM,IAAgBb,EAAca,IACtCtM,MAAMC,QAAQqM,IA5BrB,SAAsB/B,GACpB,IAAK,MAAMzI,KAAOyI,EAChB,GAAIA,EAAI3I,eAAeE,KAASI,EAAYqI,EAAIzI,IAC9C,OAAO,EAGX,OAAO,CACT,CAqBqC4K,CAAaJ,KAE9CF,GAAM9J,EAAQ+J,EAAMG,MAAM,GAAK,IAG1BlK,CACT,CCjDA,IAAeqK,GAAIzL,IACjB,IAAK,MAAMY,KAAOZ,EAChB,GAAIyK,EAAWzK,EAAKY,IAClB,OAAO,EAGX,OAAO,CAAK,ECDd,SAAS8K,GAAmB1L,EAAS2L,EAA8B,IACjE,MAAMC,EAAoB9M,MAAMC,QAAQiB,GAExC,GAAInB,EAASmB,IAAS4L,EACpB,IAAK,MAAMhL,KAAOZ,EAEdlB,MAAMC,QAAQiB,EAAKY,KAClB/B,EAASmB,EAAKY,MAAU6K,GAAkBzL,EAAKY,KAEhD+K,EAAO/K,GAAO9B,MAAMC,QAAQiB,EAAKY,IAAQ,GAAK,CAAE,EAChD8K,GAAgB1L,EAAKY,GAAM+K,EAAO/K,KACxBjC,EAAkBqB,EAAKY,MACjC+K,EAAO/K,IAAO,GAKpB,OAAO+K,CACT,CAEA,SAASE,GACP7L,EACAqG,EACAyF,GAKA,MAAMF,EAAoB9M,MAAMC,QAAQiB,GAExC,GAAInB,EAASmB,IAAS4L,EACpB,IAAK,MAAMhL,KAAOZ,EAEdlB,MAAMC,QAAQiB,EAAKY,KAClB/B,EAASmB,EAAKY,MAAU6K,GAAkBzL,EAAKY,IAG9CI,EAAYqF,IACZ1C,EAAYmI,EAAsBlL,IAElCkL,EAAsBlL,GAAO9B,MAAMC,QAAQiB,EAAKY,IAC5C8K,GAAgB1L,EAAKY,GAAM,IAC3B,IAAK8K,GAAgB1L,EAAKY,KAE9BiL,GACE7L,EAAKY,GACLjC,EAAkB0H,GAAc,CAAE,EAAGA,EAAWzF,GAChDkL,EAAsBlL,IAI1BkL,EAAsBlL,IAAQgD,EAAU5D,EAAKY,GAAMyF,EAAWzF,IAKpE,OAAOkL,CACT,CAEA,IAAAC,GAAe,CAAI1I,EAAkBgD,IACnCwF,GACExI,EACAgD,EACAqF,GAAgBrF,IC/DpB,MAAM2F,GAAqC,CACzCvN,OAAO,EACPmH,SAAS,GAGLqG,GAAc,CAAExN,OAAO,EAAMmH,SAAS,GAE5C,IAAesG,GAACC,IACd,GAAIrN,MAAMC,QAAQoN,GAAU,CAC1B,GAAIA,EAAQhK,OAAS,EAAG,CACtB,MAAM4E,EAASoF,EACZrL,QAAQsL,GAAWA,GAAUA,EAAOjN,UAAYiN,EAAOpH,WACvDyB,KAAK2F,GAAWA,EAAO3N,QAC1B,MAAO,CAAEA,MAAOsI,EAAQnB,UAAWmB,EAAO5E,QAG5C,OAAOgK,EAAQ,GAAGhN,UAAYgN,EAAQ,GAAGnH,SAErCmH,EAAQ,GAAGE,aAAerL,EAAYmL,EAAQ,GAAGE,WAAW5N,OAC1DuC,EAAYmL,EAAQ,GAAG1N,QAA+B,KAArB0N,EAAQ,GAAG1N,MAC1CwN,GACA,CAAExN,MAAO0N,EAAQ,GAAG1N,MAAOmH,SAAS,GACtCqG,GACFD,GAGN,OAAOA,EAAa,EC7BtBM,GAAe,CACb7N,GACE8N,gBAAeC,cAAaC,gBAE9BzL,EAAYvC,GACRA,EACA8N,EACY,KAAV9N,EACEiO,IACAjO,GACGA,EACDA,EACJ+N,GAAetG,EAASzH,GACtB,IAAIC,KAAKD,GACTgO,EACEA,EAAWhO,GACXA,ECfZ,MAAMkO,GAAkC,CACtC/G,SAAS,EACTnH,MAAO,MAGT,IAAAmO,GAAgBT,GACdrN,MAAMC,QAAQoN,GACVA,EAAQ1K,QACN,CAACoL,EAAUT,IACTA,GAAUA,EAAOjN,UAAYiN,EAAOpH,SAChC,CACEY,SAAS,EACTnH,MAAO2N,EAAO3N,OAEhBoO,GACNF,IAEFA,GCXkB,SAAAG,GAAcxE,GACpC,MAAM7D,EAAM6D,EAAG7D,IAEf,OAAI+F,EAAY/F,GACPA,EAAIsI,MAGThC,GAAatG,GACRmI,GAActE,EAAG0E,MAAMvO,MAG5BqM,GAAiBrG,GACZ,IAAIA,EAAIwI,iBAAiBxG,KAAI,EAAGhI,WAAYA,IAGjDyO,EAAWzI,GACNyH,GAAiB5D,EAAG0E,MAAMvO,MAG5B6N,GAAgBtL,EAAYyD,EAAIhG,OAAS6J,EAAG7D,IAAIhG,MAAQgG,EAAIhG,MAAO6J,EAC5E,CCpBA,ICXA6E,GAAgB1O,GAAoCA,aAAiB2O,OCSrEC,GACEC,GAEAtM,EAAYsM,GACRA,EACAH,GAAQG,GACNA,EAAKC,OACL1O,EAASyO,GACPH,GAAQG,EAAK7O,OACX6O,EAAK7O,MAAM8O,OACXD,EAAK7O,MACP6O,ECjBKE,GAACC,IAAsC,CACpDC,YAAaD,GAAQA,IAAShL,EAC9BkL,SAAUF,IAAShL,EACnBmL,WAAYH,IAAShL,EACrBoL,QAASJ,IAAShL,EAClBqL,UAAWL,IAAShL,ICJtB,MAAMsL,GAAiB,gBAEvB,ICJeC,GAAA,CACb1O,EACA8G,EACA6H,KAECA,IACA7H,EAAOO,UACNP,EAAOG,MAAMhH,IAAID,IACjB,IAAI8G,EAAOG,OAAO2H,MACfC,GACC7O,EAAK8O,WAAWD,IAChB,SAASvM,KAAKtC,EAAKgM,MAAM6C,EAAUhM,YCT3C,MAAMkM,GAAwB,CAC5B1C,EACA1C,EACAqF,EACAC,KAEA,IAAK,MAAM3N,KAAO0N,GAAe/K,OAAOU,KAAK0H,GAAS,CACpD,MAAMvD,EAAQjH,EAAIwK,EAAQ/K,GAE1B,GAAIwH,EAAO,CACT,MAAME,GAAEA,KAAOkG,GAAiBpG,EAEhC,GAAIE,EAAI,CACN,GAAIA,EAAG0E,MAAQ1E,EAAG0E,KAAK,IAAM/D,EAAOX,EAAG0E,KAAK,GAAIpM,KAAS2N,EACvD,OAAO,EACF,GAAIjG,EAAG7D,KAAOwE,EAAOX,EAAG7D,IAAK6D,EAAGhJ,QAAUiP,EAC/C,OAAO,EAEP,GAAIF,GAAsBG,EAAcvF,GACtC,WAGC,GAAIpK,EAAS2P,IACdH,GAAsBG,EAA2BvF,GACnD,OAKR,EC7BsB,SAAAwF,GACtB5I,EACAwC,EACA/I,GAKA,MAAMyI,EAAQ5G,EAAI0E,EAAQvG,GAE1B,GAAIyI,GAASpG,EAAMrC,GACjB,MAAO,CACLyI,QACAzI,QAIJ,MAAMD,EAAQC,EAAKkC,MAAM,KAEzB,KAAOnC,EAAM8C,QAAQ,CACnB,MAAMuE,EAAYrH,EAAMqP,KAAK,KACvBtG,EAAQjH,EAAIkH,EAAS3B,GACrBiI,EAAaxN,EAAI0E,EAAQa,GAE/B,GAAI0B,IAAUtJ,MAAMC,QAAQqJ,IAAU9I,IAASoH,EAC7C,MAAO,CAAEpH,QAGX,GAAIqP,GAAcA,EAAWpQ,KAC3B,MAAO,CACLe,KAAMoH,EACNqB,MAAO4G,GAIXtP,EAAMuP,MAGR,MAAO,CACLtP,OAEJ,CCpCA,ICCAuP,GAAe,CACbhJ,EACAkC,EACAzI,KAEA,MAAMwP,EAAmBjF,EAAsB1I,EAAI0E,EAAQvG,IAG3D,OAFA0C,EAAI8M,EAAkB,OAAQ/G,EAAMzI,IACpC0C,EAAI6D,EAAQvG,EAAMwP,GACXjJ,CAAM,ECffkJ,GAAgBtQ,GAAqCyH,EAASzH,GCChD,SAAUuQ,GACtBzN,EACAkD,EACAlG,EAAO,YAEP,GACEwQ,GAAUxN,IACTzC,MAAMC,QAAQwC,IAAWA,EAAO0N,MAAMF,KACtCrN,EAAUH,KAAYA,EAEvB,MAAO,CACLhD,OACAmK,QAASqG,GAAUxN,GAAUA,EAAS,GACtCkD,MAGN,CChBA,IAAeyK,GAACC,GACdtQ,EAASsQ,KAAoBhC,GAAQgC,GACjCA,EACA,CACE1Q,MAAO0Q,EACPzG,QAAS,ICwBjB0G,GAAeC,MACbjH,EACAkH,EACAjJ,EACAsD,EACA4F,EACAC,KAEA,MAAM/K,IACJA,EAAGuI,KACHA,EAAIyC,SACJA,EAAQC,UACRA,EAASC,UACTA,EAASC,IACTA,EAAGC,IACHA,EAAGC,QACHA,EAAOC,SACPA,EAAQzQ,KACRA,EAAIiN,cACJA,EAAaxD,MACbA,GACEX,EAAME,GACJ0H,EAA+B7O,EAAIkF,EAAY/G,GACrD,IAAKyJ,GAASuG,EAAmB/P,IAAID,GACnC,MAAO,CAAE,EAEX,MAAM2Q,EAA6BjD,EAAOA,EAAK,GAAMvI,EAC/CgE,EAAqBC,IACrB6G,GAA6BU,EAAStH,iBACxCsH,EAASxH,kBAAkB/G,EAAUgH,GAAW,GAAKA,GAAW,IAChEuH,EAAStH,mBAGPZ,EAA6B,CAAE,EAC/BmI,EAAUnF,GAAatG,GACvByI,EAAa7O,EAAgBoG,GAC7B0L,EAAoBD,GAAWhD,EAC/BkD,GACF7D,GAAiB/B,EAAY/F,KAC7BzD,EAAYyD,EAAIhG,QAChBuC,EAAYgP,IACbtF,EAAcjG,IAAsB,KAAdA,EAAIhG,OACZ,KAAfuR,GACClR,MAAMC,QAAQiR,KAAgBA,EAAW7N,OACtCkO,EAAoB3G,EAAa4G,KACrC,KACAhR,EACAqK,EACA5B,GAEIwI,EAAmB,CACvBC,EACAC,EACAC,EACAC,EAAmBjO,EACnBkO,EAAmBlO,KAEnB,MAAMgG,EAAU8H,EAAYC,EAAmBC,EAC/C3I,EAAMzI,GAAQ,CACZf,KAAMiS,EAAYG,EAAUC,EAC5BlI,UACAjE,SACG4L,EAAkBG,EAAYG,EAAUC,EAASlI,GACrD,EAGH,GACE8G,GACK1Q,MAAMC,QAAQiR,KAAgBA,EAAW7N,OAC1CsN,KACGU,IAAsBC,GAAWzR,EAAkBqR,KACnDtO,EAAUsO,KAAgBA,GAC1B9C,IAAehB,GAAiBc,GAAMpH,SACtCsK,IAAYtD,GAAcI,GAAMpH,SACvC,CACA,MAAMnH,MAAEA,EAAKiK,QAAEA,GAAYqG,GAAUU,GACjC,CAAEhR,QAASgR,EAAU/G,QAAS+G,GAC9BP,GAAmBO,GAEvB,GAAIhR,IACFsJ,EAAMzI,GAAQ,CACZf,KAAMmE,EACNgG,UACAjE,IAAKwL,KACFI,EAAkB3N,EAAiCgG,KAEnDiB,GAEH,OADAlB,EAAkBC,GACXX,EAKb,KAAKqI,GAAazR,EAAkBiR,IAASjR,EAAkBkR,IAAO,CACpE,IAAIW,EACAK,EACJ,MAAMC,EAAY5B,GAAmBW,GAC/BkB,EAAY7B,GAAmBU,GAErC,GAAKjR,EAAkBqR,IAAgBzN,MAAMyN,GAUtC,CACL,MAAMgB,EACHvM,EAAyB+H,aAAe,IAAI9N,KAAKsR,GAC9CiB,EAAqBC,GACzB,IAAIxS,MAAK,IAAIA,MAAOyS,eAAiB,IAAMD,GACvCE,EAAqB,QAAZ3M,EAAIlG,KACb8S,EAAqB,QAAZ5M,EAAIlG,KAEf2H,EAAS4K,EAAUrS,QAAUuR,IAC/BQ,EAAYY,EACRH,EAAkBjB,GAAciB,EAAkBH,EAAUrS,OAC5D4S,EACErB,EAAac,EAAUrS,MACvBuS,EAAY,IAAItS,KAAKoS,EAAUrS,QAGnCyH,EAAS6K,EAAUtS,QAAUuR,IAC/Ba,EAAYO,EACRH,EAAkBjB,GAAciB,EAAkBF,EAAUtS,OAC5D4S,EACErB,EAAae,EAAUtS,MACvBuS,EAAY,IAAItS,KAAKqS,EAAUtS,YA/B2B,CAClE,MAAM6S,EACH7M,EAAyB8H,gBACzByD,GAAcA,EAAaA,GACzBrR,EAAkBmS,EAAUrS,SAC/B+R,EAAYc,EAAcR,EAAUrS,OAEjCE,EAAkBoS,EAAUtS,SAC/BoS,EAAYS,EAAcP,EAAUtS,OA2BxC,IAAI+R,GAAaK,KACfN,IACIC,EACFM,EAAUpI,QACVqI,EAAUrI,QACVhG,EACAA,IAEGiH,GAEH,OADAlB,EAAkBV,EAAMzI,GAAOoJ,SACxBX,EAKb,IACG2H,GAAaC,KACbS,IACAlK,EAAS8J,IAAgBR,GAAgB1Q,MAAMC,QAAQiR,IACxD,CACA,MAAMuB,EAAkBrC,GAAmBQ,GACrC8B,EAAkBtC,GAAmBS,GACrCa,GACH7R,EAAkB4S,EAAgB9S,QACnCuR,EAAW7N,QAAUoP,EAAgB9S,MACjCoS,GACHlS,EAAkB6S,EAAgB/S,QACnCuR,EAAW7N,QAAUqP,EAAgB/S,MAEvC,IAAI+R,GAAaK,KACfN,EACEC,EACAe,EAAgB7I,QAChB8I,EAAgB9I,UAEbiB,GAEH,OADAlB,EAAkBV,EAAMzI,GAAOoJ,SACxBX,EAKb,GAAI+H,IAAYM,GAAWlK,EAAS8J,GAAa,CAC/C,MAAQvR,MAAOgT,EAAY/I,QAAEA,GAAYwG,GAAmBY,GAE5D,GAAI3C,GAAQsE,KAAkBzB,EAAW0B,MAAMD,KAC7C1J,EAAMzI,GAAQ,CACZf,KAAMmE,EACNgG,UACAjE,SACG4L,EAAkB3N,EAAgCgG,KAElDiB,GAEH,OADAlB,EAAkBC,GACXX,EAKb,GAAIgI,EACF,GAAItF,EAAWsF,GAAW,CACxB,MACM4B,EAAgB3C,SADDe,EAASC,EAAY3J,GACK4J,GAE/C,GAAI0B,IACF5J,EAAMzI,GAAQ,IACTqS,KACAtB,EACD3N,EACAiP,EAAcjJ,WAGbiB,GAEH,OADAlB,EAAkBkJ,EAAcjJ,SACzBX,OAGN,GAAIlJ,EAASkR,GAAW,CAC7B,IAAI6B,EAAmB,CAAgB,EAEvC,IAAK,MAAMhR,KAAOmP,EAAU,CAC1B,IAAKxF,EAAcqH,KAAsBjI,EACvC,MAGF,MAAMgI,EAAgB3C,SACde,EAASnP,GAAKoP,EAAY3J,GAChC4J,EACArP,GAGE+Q,IACFC,EAAmB,IACdD,KACAtB,EAAkBzP,EAAK+Q,EAAcjJ,UAG1CD,EAAkBkJ,EAAcjJ,SAE5BiB,IACF5B,EAAMzI,GAAQsS,IAKpB,IAAKrH,EAAcqH,KACjB7J,EAAMzI,GAAQ,CACZmF,IAAKwL,KACF2B,IAEAjI,GACH,OAAO5B,EAOf,OADAU,GAAkB,GACXV,CAAK,ECnMd,MAAM8J,GAAiB,CACrBpE,KAAMhL,EACNqP,eAAgBrP,EAChBsP,kBAAkB,GAGJ,SAAAC,GAKdlN,EAAkE,IAUlE,IAAI+D,EAAW,IACVgJ,MACA/M,GAEDM,EAAsC,CACxC6M,YAAa,EACb3M,SAAS,EACT4M,SAAS,EACT3M,UAAWkF,EAAW5B,EAASxF,eAC/BsC,cAAc,EACdwM,aAAa,EACbC,cAAc,EACdC,oBAAoB,EACpBzM,SAAS,EACTH,cAAe,CAAE,EACjBD,YAAa,CAAE,EACfE,iBAAkB,CAAE,EACpBG,OAAQgD,EAAShD,QAAU,CAAE,EAC7Bb,SAAU6D,EAAS7D,WAAY,GAEjC,MAAMqD,EAAqB,CAAE,EAC7B,IAmBIiK,EAnBAhP,GACFzE,EAASgK,EAASxF,gBAAkBxE,EAASgK,EAAS9B,UAClDhH,EAAY8I,EAAS9B,QAAU8B,EAASxF,gBACxC,CAAE,EACJ2D,EAAc6B,EAAS1B,iBACtB,CAAA,EACApH,EAAYuD,GACb0F,EAAS,CACXC,QAAQ,EACRF,OAAO,EACPxC,OAAO,GAELH,EAAgB,CAClB2C,MAAO,IAAI3I,IACX4E,SAAU,IAAI5E,IACdmS,QAAS,IAAInS,IACbiH,MAAO,IAAIjH,IACXmG,MAAO,IAAInG,KAGToS,EAAQ,EACZ,MAAM9O,EAAiC,CACrC4B,SAAS,EACTE,aAAa,EACbE,kBAAkB,EAClBD,eAAe,EACfE,cAAc,EACdC,SAAS,EACTC,QAAQ,GAEV,IAAI4M,EAA2B,IAC1B/O,GAEL,MAAMgP,EAAoC,CACxCrL,MAAOyC,IACP6I,MAAO7I,KAEH8I,EAA6BpF,GAAmB3E,EAAS4E,MACzDoF,EAA4BrF,GAAmB3E,EAASiJ,gBACxDgB,EACJjK,EAASkK,eAAiBtQ,EAStBuD,EAAYqJ,MAAO2D,IACvB,IACGnK,EAAS7D,WACTtB,EAAgBkC,SACf6M,EAAyB7M,SACzBoN,GACF,CACA,MAAMpN,EAAUiD,EAASoK,SACrB1I,SAAqB2I,KAAcrN,cAC7BsN,EAAyB9K,GAAS,GAExCzC,IAAYR,EAAWQ,SACzB8M,EAAUC,MAAM1I,KAAK,CACnBrE,cAMFwN,EAAsB,CAAC/T,EAAkBsG,MAE1CkD,EAAS7D,WACTtB,EAAgBiC,cACfjC,EAAgBgC,kBAChB+M,EAAyB9M,cACzB8M,EAAyB/M,qBAE1BrG,GAASP,MAAMuU,KAAKjN,EAAO2C,QAAQuK,SAAShU,IACvCA,IACFqG,EACI3D,EAAIoD,EAAWM,iBAAkBpG,EAAMqG,GACvCuF,GAAM9F,EAAWM,iBAAkBpG,OAI3CoT,EAAUC,MAAM1I,KAAK,CACnBvE,iBAAkBN,EAAWM,iBAC7BC,cAAe4E,EAAcnF,EAAWM,sBA8ExC6N,EAAsB,CAC1BjU,EACAkU,EACA/U,EACAgG,KAEA,MAAM2D,EAAejH,EAAIkH,EAAS/I,GAElC,GAAI8I,EAAO,CACT,MAAM9G,EAAeH,EACnB6F,EACA1H,EACA0B,EAAYvC,GAAS0C,EAAImC,EAAgBhE,GAAQb,GAGnDuC,EAAYM,IACXmD,GAAQA,EAAyBgP,gBAClCD,EACIxR,EACEgF,EACA1H,EACAkU,EAAuBlS,EAAewL,GAAc1E,EAAME,KAE5DoL,EAAcpU,EAAMgC,GAExB0H,EAAOD,OAAS/C,MAId2N,EAAsB,CAC1BrU,EACAsU,EACA3F,EACA4F,EACAC,KAIA,IAAIC,GAAoB,EACpBC,GAAkB,EACtB,MAAM1K,EAA8D,CAClEhK,QAGF,IAAKuJ,EAAS7D,SAAU,CACtB,IAAKiJ,GAAe4F,EAAa,EAC3BnQ,EAAgB4B,SAAWmN,EAAyBnN,WACtD0O,EAAkB5O,EAAWE,QAC7BF,EAAWE,QAAUgE,EAAOhE,QAAU2O,IACtCF,EAAoBC,IAAoB1K,EAAOhE,SAGjD,MAAM4O,EAAyBtQ,EAC7BzC,EAAImC,EAAgBhE,GACpBsU,GAGFI,IAAoB7S,EAAIiE,EAAWI,YAAalG,GAChD4U,EACIhJ,GAAM9F,EAAWI,YAAalG,GAC9B0C,EAAIoD,EAAWI,YAAalG,GAAM,GACtCgK,EAAO9D,YAAcJ,EAAWI,YAChCuO,EACEA,IACErQ,EAAgB8B,aAChBiN,EAAyBjN,cACzBwO,KAAqBE,EAG3B,GAAIjG,EAAa,CACf,MAAMkG,EAAyBhT,EAAIiE,EAAWK,cAAenG,GAExD6U,IACHnS,EAAIoD,EAAWK,cAAenG,EAAM2O,GACpC3E,EAAO7D,cAAgBL,EAAWK,cAClCsO,EACEA,IACErQ,EAAgB+B,eAChBgN,EAAyBhN,gBACzB0O,IAA2BlG,GAInC8F,GAAqBD,GAAgBpB,EAAUC,MAAM1I,KAAKX,GAG5D,OAAOyK,EAAoBzK,EAAS,CAAE,CAAA,EAGlC8K,EAAsB,CAC1B9U,EACAsG,EACAmC,EACAL,KAMA,MAAM2M,EAAqBlT,EAAIiE,EAAWS,OAAQvG,GAC5C0T,GACHtP,EAAgBkC,SAAW6M,EAAyB7M,UACrDlE,EAAUkE,IACVR,EAAWQ,UAAYA,EAhOzB,IAAqBG,EA6OrB,GAXI8C,EAASyL,YAAcvM,GAlONhC,EAmOW,IAzHb,EAACzG,EAAyByI,KAC7C/F,EAAIoD,EAAWS,OAAQvG,EAAMyI,GAC7B2K,EAAUC,MAAM1I,KAAK,CACnBpE,OAAQT,EAAWS,QACnB,EAqHoC0O,CAAajV,EAAMyI,GAAvDuK,EAlODkC,IACCC,aAAajC,GACbA,EAAQkC,WAAW3O,EAAUyO,EAAK,EAiOlClC,EAAmBzJ,EAASyL,cAE5BG,aAAajC,GACbF,EAAqB,KACrBvK,EACI/F,EAAIoD,EAAWS,OAAQvG,EAAMyI,GAC7BmD,GAAM9F,EAAWS,OAAQvG,KAI5ByI,GAASnE,EAAUyQ,EAAoBtM,GAASsM,KAChD9J,EAAc7C,IACfsL,EACA,CACA,MAAM2B,EAAmB,IACpBjN,KACCsL,GAAqBtR,EAAUkE,GAAW,CAAEA,WAAY,GAC5DC,OAAQT,EAAWS,OACnBvG,QAGF8F,EAAa,IACRA,KACAuP,GAGLjC,EAAUC,MAAM1I,KAAK0K,KAInBzB,EAAa7D,MAAO/P,IACxB8T,EAAoB9T,GAAM,GAC1B,MAAMiC,QAAesH,EAASoK,SAC5BjM,EACA6B,EAAS+L,Qd1aA,EACbtG,EACAjG,EACA0K,EACAxD,KAEA,MAAM5D,EAAiD,CAAE,EAEzD,IAAK,MAAMrM,KAAQgP,EAAa,CAC9B,MAAMlG,EAAejH,EAAIkH,EAAS/I,GAElC8I,GAASpG,EAAI2J,EAAQrM,EAAM8I,EAAME,IAGnC,MAAO,CACLyK,eACA1T,MAAO,IAAIiP,GACX3C,SACA4D,4BACD,EcwZGsF,CACEvV,GAAQ8G,EAAO2C,MACfV,EACAQ,EAASkK,aACTlK,EAAS0G,4BAIb,OADA6D,EAAoB9T,GACbiC,CAAM,EAoBT4R,EAA2B9D,MAC/B1D,EACAmJ,EACAF,EAEI,CACFG,OAAO,MAGT,IAAK,MAAMzV,KAAQqM,EAAQ,CACzB,MAAMvD,EAAQuD,EAAOrM,GAErB,GAAI8I,EAAO,CACT,MAAME,GAAEA,KAAOsL,GAAexL,EAE9B,GAAIE,EAAI,CACN,MAAM0M,EAAmB5O,EAAOiB,MAAM9H,IAAI+I,EAAGhJ,MACvC2V,EACJ7M,EAAME,QV9dF4M,EU8d8B9M,EAAgBE,OV5d1D4M,EAAenF,aAEdtF,EAAWyK,EAAenF,WACzBmF,EAAenF,SAASvP,YAAYlB,OAASyO,IAC9ClP,EAASqW,EAAenF,WACvBxM,OAAOwD,OAAOmO,EAAenF,UAAUoF,MACpCC,GACCA,EAAiB5U,YAAYlB,OAASyO,OUudlCkH,GAAqBvR,EAAgBgC,kBACvC0N,EAAoB,CAAC9T,IAAO,GAG9B,MAAM+V,QAAmBjG,GACvBhH,EACAhC,EAAOpB,SACPgC,EACA8L,EACAjK,EAAS0G,4BAA8BuF,EACvCE,GAOF,GAJIC,GAAqBvR,EAAgBgC,kBACvC0N,EAAoB,CAAC9T,IAGnB+V,EAAW/M,EAAGhJ,QAChBsV,EAAQG,OAAQ,EACZD,GACF,OAIHA,IACE3T,EAAIkU,EAAY/M,EAAGhJ,MAChB0V,EACEnG,GACEzJ,EAAWS,OACXwP,EACA/M,EAAGhJ,MAEL0C,EAAIoD,EAAWS,OAAQyC,EAAGhJ,KAAM+V,EAAW/M,EAAGhJ,OAChD4L,GAAM9F,EAAWS,OAAQyC,EAAGhJ,QAGnCiL,EAAcqJ,UACNT,EACLS,EACAkB,EACAF,IVxgBG,IAACM,EU6gBZ,OAAON,EAAQG,KAAK,EAiBhBd,EAAwB,CAAC3U,EAAMU,KAClC6I,EAAS7D,WACT1F,GAAQU,GAAQgC,EAAIgF,EAAa1H,EAAMU,IACvC4D,EAAU0R,KAAahS,IAEpBwD,EAAyC,CAC7CzH,EACAiC,EACAgF,IAEAH,EACE9G,EACA+G,EACA,IACM4C,EAAOD,MACP/B,EACAhG,EAAYM,GACVgC,EACA4C,EAAS7G,GACP,CAAEA,CAACA,GAAQiC,GACXA,GAEVgF,EACAhF,GAcEoS,EAAgB,CACpBpU,EACAb,EACA0N,EAA0B,CAAA,KAE1B,MAAM/D,EAAejH,EAAIkH,EAAS/I,GAClC,IAAIsU,EAAsBnV,EAE1B,GAAI2J,EAAO,CACT,MAAM8M,EAAiB9M,EAAME,GAEzB4M,KACDA,EAAelQ,UACdhD,EAAIgF,EAAa1H,EAAMgN,GAAgB7N,EAAOyW,IAEhDtB,EACElJ,EAAcwK,EAAezQ,MAAQ9F,EAAkBF,GACnD,GACAA,EAEFqM,GAAiBoK,EAAezQ,KAClC,IAAIyQ,EAAezQ,IAAI0H,SAASmH,SAC7BiC,GACEA,EAAUC,SACT5B,EACAxP,SAASmR,EAAU9W,SAEhByW,EAAelI,KACpB3O,EAAgB6W,EAAezQ,KACjCyQ,EAAelI,KAAK7K,OAAS,EACzB+S,EAAelI,KAAKsG,SACjBmC,KACGA,EAAYhC,iBAAmBgC,EAAYzQ,YAC5CyQ,EAAYtW,QAAUL,MAAMC,QAAQ6U,KAC9BA,EAAkBuB,MAClBnV,GAAiBA,IAASyV,EAAYhX,QAEzCmV,IAAe6B,EAAYhX,SAEnCyW,EAAelI,KAAK,KACnBkI,EAAelI,KAAK,GAAG7N,UAAYyU,GAExCsB,EAAelI,KAAKsG,SACjBoC,GACEA,EAASvW,QAAUuW,EAASjX,QAAUmV,IAGpCpJ,EAAY0K,EAAezQ,KACpCyQ,EAAezQ,IAAIhG,MAAQ,IAE3ByW,EAAezQ,IAAIhG,MAAQmV,EAEtBsB,EAAezQ,IAAIlG,MACtBmU,EAAUC,MAAM1I,KAAK,CACnB3K,OACAyH,OAAQhH,EAAYiH,QAO7BmF,EAAQ0H,aAAe1H,EAAQwJ,cAC9BhC,EACErU,EACAsU,EACAzH,EAAQwJ,YACRxJ,EAAQ0H,aACR,GAGJ1H,EAAQyJ,gBAAkBC,GAAQvW,EAA2B,EAGzDwW,EAAY,CAKhBxW,EACAb,EACA0N,KAEA,IAAK,MAAM4J,KAAYtX,EAAO,CAC5B,MAAMmV,EAAanV,EAAMsX,GACnBrP,EAAY,GAAGpH,KAAQyW,IACvB3N,EAAQjH,EAAIkH,EAAS3B,IAE1BN,EAAOiB,MAAM9H,IAAID,IAChBT,EAAS+U,IACRxL,IAAUA,EAAME,MAClB9J,EAAaoV,GACVkC,EAAUpP,EAAWkN,EAAYzH,GACjCuH,EAAchN,EAAWkN,EAAYzH,KAIvC6J,EAA0C,CAC9C1W,EACAb,EACA0N,EAAU,CAAA,KAEV,MAAM/D,EAAQjH,EAAIkH,EAAS/I,GACrBkQ,EAAepJ,EAAOiB,MAAM9H,IAAID,GAChC2W,EAAalW,EAAYtB,GAE/BuD,EAAIgF,EAAa1H,EAAM2W,GAEnBzG,GACFkD,EAAUrL,MAAM4C,KAAK,CACnB3K,OACAyH,OAAQhH,EAAYiH,MAInBtD,EAAgB4B,SACf5B,EAAgB8B,aAChBiN,EAAyBnN,SACzBmN,EAAyBjN,cAC3B2G,EAAQ0H,aAERnB,EAAUC,MAAM1I,KAAK,CACnB3K,OACAkG,YAAauG,GAAezI,EAAgB0D,GAC5C1B,QAAS2O,EAAU3U,EAAM2W,OAI7B7N,GAAUA,EAAME,IAAO3J,EAAkBsX,GAErCvC,EAAcpU,EAAM2W,EAAY9J,GADhC2J,EAAUxW,EAAM2W,EAAY9J,GAIlC6B,GAAU1O,EAAM8G,IAAWsM,EAAUC,MAAM1I,KAAK,IAAK7E,IACrDsN,EAAUC,MAAM1I,KAAK,CACnB3K,KAAM0J,EAAOD,MAAQzJ,OAAO4B,EAC5B6F,OAAQhH,EAAYiH,IACpB,EAGEgB,EAA0BqH,MAAOpQ,IACrC+J,EAAOD,OAAQ,EACf,MAAM7J,EAASD,EAAMC,OACrB,IAAII,EAAeJ,EAAOI,KACtB4W,GAAsB,EAC1B,MAAM9N,EAAejH,EAAIkH,EAAS/I,GAC5B6W,EAA8BvC,IAClCsC,EACEE,OAAO7T,MAAMqR,IACZpV,EAAaoV,IAAerR,MAAMqR,EAAW7P,YAC9CH,EAAUgQ,EAAYzS,EAAI6F,EAAa1H,EAAMsU,GAAY,EAG7D,GAAIxL,EAAO,CACT,IAAIL,EACAnC,EACJ,MAAMgO,EAAa1U,EAAOX,KACtBuO,GAAc1E,EAAME,IACpBtJ,EAAcC,GACZgP,EACJhP,EAAMV,OAASiE,GAAevD,EAAMV,OAASiE,EACzC6T,KCxuBIlK,EDyuBQ/D,EAAME,ICxuBpBS,QACPoD,EAAQsD,UACPtD,EAAQyD,KACRzD,EAAQ0D,KACR1D,EAAQuD,WACRvD,EAAQwD,WACRxD,EAAQ2D,SACR3D,EAAQ4D,WDkuBDlH,EAASoK,UACT9R,EAAIiE,EAAWS,OAAQvG,IACvB8I,EAAME,GAAG9D,OE5uBL,EACbyJ,EACAnG,EACAqK,EACAL,EAIArE,KAEIA,EAAKI,WAEGsE,GAAe1E,EAAKK,YACrBhG,GAAamG,IACbkE,EAAcL,EAAenE,SAAWF,EAAKE,WAC9CM,IACCkE,EAAcL,EAAelE,WAAaH,EAAKG,aACjDK,GF4tBHqI,CACErI,EACA9M,EAAIiE,EAAWK,cAAenG,GAC9B8F,EAAW+M,YACXU,EACAD,GAEE2D,EAAUvI,GAAU1O,EAAM8G,EAAQ6H,GAExCjM,EAAIgF,EAAa1H,EAAMsU,GAEnB3F,GACF7F,EAAME,GAAGJ,QAAUE,EAAME,GAAGJ,OAAOjJ,GACnCqT,GAAsBA,EAAmB,IAChClK,EAAME,GAAGN,UAClBI,EAAME,GAAGN,SAAS/I,GAGpB,MAAMyI,EAAaiM,EAAoBrU,EAAMsU,EAAY3F,GAEnD6F,GAAgBvJ,EAAc7C,IAAe6O,EASnD,IAPCtI,GACCyE,EAAUC,MAAM1I,KAAK,CACnB3K,OACAf,KAAMU,EAAMV,KACZwI,OAAQhH,EAAYiH,KAGpBqP,EAWF,OAVI3S,EAAgBkC,SAAW6M,EAAyB7M,WAChC,WAAlBiD,EAAS4E,KACPQ,GACFjI,IAEQiI,GACVjI,KAKF8N,GACApB,EAAUC,MAAM1I,KAAK,CAAE3K,UAAUiX,EAAU,CAAA,EAAK7O,IAMpD,IAFCuG,GAAesI,GAAW7D,EAAUC,MAAM1I,KAAK,IAAK7E,IAEjDyD,EAASoK,SAAU,CACrB,MAAMpN,OAAEA,SAAiBqN,EAAW,CAAC5T,IAIrC,GAFA6W,EAA2BvC,GAEvBsC,EAAqB,CACvB,MAAMM,EAA4B/H,GAChCrJ,EAAWS,OACXwC,EACA/I,GAEImX,EAAoBhI,GACxB5I,EACAwC,EACAmO,EAA0BlX,MAAQA,GAGpCyI,EAAQ0O,EAAkB1O,MAC1BzI,EAAOmX,EAAkBnX,KAEzBsG,EAAU2E,EAAc1E,SAG1BuN,EAAoB,CAAC9T,IAAO,GAC5ByI,SACQqH,GACJhH,EACAhC,EAAOpB,SACPgC,EACA8L,EACAjK,EAAS0G,4BAEXjQ,GACF8T,EAAoB,CAAC9T,IAErB6W,EAA2BvC,GAEvBsC,IACEnO,EACFnC,GAAU,GAEVlC,EAAgBkC,SAChB6M,EAAyB7M,WAEzBA,QAAgBuN,EAAyB9K,GAAS,KAKpD6N,IACF9N,EAAME,GAAG9D,MACPqR,GACEzN,EAAME,GAAG9D,MAIb4P,EAAoB9U,EAAMsG,EAASmC,EAAOL,ICr1BnC,IAACyE,GD01BRuK,GAAc,CAACjS,EAAU7D,KAC7B,GAAIO,EAAIiE,EAAWS,OAAQjF,IAAQ6D,EAAI8D,MAErC,OADA9D,EAAI8D,QACG,CAET,EAGIsN,GAAwCxG,MAAO/P,EAAM6M,EAAU,CAAA,KACnE,IAAIvG,EACAgM,EACJ,MAAM+E,EAAa9M,EAAsBvK,GAEzC,GAAIuJ,EAASoK,SAAU,CACrB,MAAMpN,OAza0BwJ,OAAOhQ,IACzC,MAAMwG,OAAEA,SAAiBqN,EAAW7T,GAEpC,GAAIA,EACF,IAAK,MAAMC,KAAQD,EAAO,CACxB,MAAM0I,EAAQ5G,EAAI0E,EAAQvG,GAC1ByI,EACI/F,EAAIoD,EAAWS,OAAQvG,EAAMyI,GAC7BmD,GAAM9F,EAAWS,OAAQvG,QAG/B8F,EAAWS,OAASA,EAGtB,OAAOA,CAAM,EA2ZU+Q,CACnB5V,EAAY1B,GAAQA,EAAOqX,GAG7B/Q,EAAU2E,EAAc1E,GACxB+L,EAAmBtS,GACdqX,EAAWzI,MAAM5O,GAAS6B,EAAI0E,EAAQvG,KACvCsG,OACKtG,GACTsS,SACQiF,QAAQC,IACZH,EAAWlQ,KAAI4I,MAAO3I,IACpB,MAAM0B,EAAQjH,EAAIkH,EAAS3B,GAC3B,aAAayM,EACX/K,GAASA,EAAME,GAAK,CAAE5B,CAACA,GAAY0B,GAAUA,EAC9C,MAGL6G,MAAMlO,UACL6Q,GAAqBxM,EAAWQ,UAAYI,KAE/C4L,EAAmBhM,QAAgBuN,EAAyB9K,GAqB9D,OAlBAqK,EAAUC,MAAM1I,KAAK,KACd/D,EAAS5G,KACZoE,EAAgBkC,SAAW6M,EAAyB7M,UACpDA,IAAYR,EAAWQ,QACrB,CAAA,EACA,CAAEtG,WACFuJ,EAASoK,WAAa3T,EAAO,CAAEsG,WAAY,GAC/CC,OAAQT,EAAWS,SAGrBsG,EAAQ4K,cACLnF,GACDvD,GACEhG,EACAqO,GACApX,EAAOqX,EAAavQ,EAAO2C,OAGxB6I,CAAgB,EAGnB0D,GACJqB,IAIA,MAAM5P,EAAS,IACTiC,EAAOD,MAAQ/B,EAAc1D,GAGnC,OAAOtC,EAAY2V,GACf5P,EACAb,EAASyQ,GACPxV,EAAI4F,EAAQ4P,GACZA,EAAWlQ,KAAKnH,GAAS6B,EAAI4F,EAAQzH,IAAM,EAG7C0X,GAAoD,CACxD1X,EACA2D,KACI,CACJ2E,UAAWzG,GAAK8B,GAAamC,GAAYS,OAAQvG,GACjDgG,UAAWnE,GAAK8B,GAAamC,GAAYI,YAAalG,GACtDyI,MAAO5G,GAAK8B,GAAamC,GAAYS,OAAQvG,GAC7CqG,eAAgBxE,EAAIiE,EAAWM,iBAAkBpG,GACjDwI,YAAa3G,GAAK8B,GAAamC,GAAYK,cAAenG,KActD2X,GAA0C,CAAC3X,EAAMyI,EAAOoE,KAC5D,MAAM1H,GAAOtD,EAAIkH,EAAS/I,EAAM,CAAEgJ,GAAI,KAAMA,IAAM,CAAE,GAAE7D,IAChDyS,EAAe/V,EAAIiE,EAAWS,OAAQvG,IAAS,CAAE,GAG/CmF,IAAK0S,EAAUzO,QAAEA,EAAOnK,KAAEA,KAAS6Y,GAAoBF,EAE/DlV,EAAIoD,EAAWS,OAAQvG,EAAM,IACxB8X,KACArP,EACHtD,QAGFiO,EAAUC,MAAM1I,KAAK,CACnB3K,OACAuG,OAAQT,EAAWS,OACnBD,SAAS,IAGXuG,GAAWA,EAAQ4K,aAAetS,GAAOA,EAAI8D,OAAS9D,EAAI8D,OAAO,EA4B7DzC,GAA2ChB,GAC/C4N,EAAUC,MAAMxI,UAAU,CACxBF,KACEhH,IG7+BO,IACb3D,EACA+X,EACApS,EAFA3F,EHk/B8BwF,EAAMxF,KGj/BpC+X,EHi/B0CpU,EAAU3D,KGh/BpD2F,EHg/B0DH,EAAMG,MG9+B/D3F,GACA+X,GACD/X,IAAS+X,IACTxN,EAAsBvK,GAAM4O,MACzBoJ,GACCA,IACCrS,EACGqS,IAAgBD,EAChBC,EAAYlJ,WAAWiJ,IACvBA,EAAWjJ,WAAWkJ,QTPjB,EACbC,EAIA7T,EACAwB,EACA9B,KAEA8B,EAAgBqS,GAChB,MAAMjY,KAAEA,KAAS2D,GAAcsU,EAE/B,OACEhN,EAActH,IACdM,OAAOU,KAAKhB,GAAWd,QAAUoB,OAAOU,KAAKP,GAAiBvB,QAC9DoB,OAAOU,KAAKhB,GAAWkS,MACpBvU,GACC8C,EAAgB9C,OACdwC,GAAUX,IACf,EM09BK+U,CACEvU,EACC6B,EAAM7B,WAA+BS,EACtC+T,GACA3S,EAAM4S,eAGR5S,EAAMiB,SAAS,CACbgB,OAAQ,IAAKC,MACV5B,KACAnC,OAIRoH,YAcCnB,GAA8C,CAAC5J,EAAM6M,EAAU,CAAA,KACnE,IAAK,MAAMzF,KAAapH,EAAOuK,EAAsBvK,GAAQ8G,EAAO2C,MAClE3C,EAAO2C,MAAM4O,OAAOjR,GACpBN,EAAOiB,MAAMsQ,OAAOjR,GAEfyF,EAAQyL,YACX1M,GAAM7C,EAAS3B,GACfwE,GAAMlE,EAAaN,KAGpByF,EAAQ0L,WAAa3M,GAAM9F,EAAWS,OAAQa,IAC9CyF,EAAQ2L,WAAa5M,GAAM9F,EAAWI,YAAakB,IACnDyF,EAAQ4L,aAAe7M,GAAM9F,EAAWK,cAAeiB,IACvDyF,EAAQ6L,kBACP9M,GAAM9F,EAAWM,iBAAkBgB,IACpCmC,EAAS1B,mBACPgF,EAAQ8L,kBACT/M,GAAM5H,EAAgBoD,GAG1BgM,EAAUC,MAAM1I,KAAK,CACnBlD,OAAQhH,EAAYiH,KAGtB0L,EAAUC,MAAM1I,KAAK,IAChB7E,KACE+G,EAAQ2L,UAAiB,CAAExS,QAAS2O,KAAhB,CAAA,KAG1B9H,EAAQ+L,aAAelS,GAAW,EAG/BmD,GAAgE,EACpEnE,WACA1F,YAGGoC,EAAUsD,IAAagE,EAAOD,OAC7B/D,GACFoB,EAAOpB,SAASzF,IAAID,MAEpB0F,EAAWoB,EAAOpB,SAASwB,IAAIlH,GAAQ8G,EAAOpB,SAAS2S,OAAOrY,KAI5DkI,GAA0C,CAAClI,EAAM6M,EAAU,CAAA,KAC/D,IAAI/D,EAAQjH,EAAIkH,EAAS/I,GACzB,MAAM6Y,EACJzW,EAAUyK,EAAQnH,WAAatD,EAAUmH,EAAS7D,UAwBpD,OAtBAhD,EAAIqG,EAAS/I,EAAM,IACb8I,GAAS,CAAA,EACbE,GAAI,IACEF,GAASA,EAAME,GAAKF,EAAME,GAAK,CAAE7D,IAAK,CAAEnF,SAC5CA,OACAyJ,OAAO,KACJoD,KAGP/F,EAAO2C,MAAMvC,IAAIlH,GAEb8I,EACFe,GAAkB,CAChBnE,SAAUtD,EAAUyK,EAAQnH,UACxBmH,EAAQnH,SACR6D,EAAS7D,SACb1F,SAGFiU,EAAoBjU,GAAM,EAAM6M,EAAQ1N,OAGnC,IACD0Z,EACA,CAAEnT,SAAUmH,EAAQnH,UAAY6D,EAAS7D,UACzC,MACA6D,EAASuP,YACT,CACE3I,WAAYtD,EAAQsD,SACpBG,IAAKvC,GAAalB,EAAQyD,KAC1BC,IAAKxC,GAAalB,EAAQ0D,KAC1BF,UAAWtC,GAAqBlB,EAAQwD,WACxCD,UAAWrC,GAAalB,EAAQuD,WAChCI,QAASzC,GAAalB,EAAQ2D,UAEhC,GACJxQ,OACA0I,WACAE,OAAQF,EACRvD,IAAMA,IACJ,GAAIA,EAAK,CACP+C,GAASlI,EAAM6M,GACf/D,EAAQjH,EAAIkH,EAAS/I,GAErB,MAAM+Y,EAAWrX,EAAYyD,EAAIhG,QAC7BgG,EAAI6T,kBACD7T,EAAI6T,iBAAiB,yBAAyB,IAEjD7T,EACE8T,EIhnCD,CAAC9T,GACdsG,GAAatG,IAAQpG,EAAgBoG,GJ+mCL0L,CAAkBkI,GACpCrL,EAAO5E,EAAME,GAAG0E,MAAQ,GAE9B,GACEuL,EACIvL,EAAKmI,MAAM/I,GAAgBA,IAAWiM,IACtCA,IAAajQ,EAAME,GAAG7D,IAE1B,OAGFzC,EAAIqG,EAAS/I,EAAM,CACjBgJ,GAAI,IACCF,EAAME,MACLiQ,EACA,CACEvL,KAAM,IACDA,EAAKlM,OAAOkK,IACfqN,KACIvZ,MAAMC,QAAQoC,EAAImC,EAAgBhE,IAAS,CAAC,IAAM,IAExDmF,IAAK,CAAElG,KAAM8Z,EAAS9Z,KAAMe,SAE9B,CAAEmF,IAAK4T,MAIf9E,EAAoBjU,GAAM,OAAO4B,EAAWmX,QAE5CjQ,EAAQjH,EAAIkH,EAAS/I,EAAM,CAAA,GAEvB8I,EAAME,KACRF,EAAME,GAAGS,OAAQ,IAGlBF,EAAS1B,kBAAoBgF,EAAQhF,qBAClC/H,EAAmBgH,EAAOiB,MAAO/H,KAAS0J,EAAOC,SACnD7C,EAAOmM,QAAQ/L,IAAIlH,IAG1B,EAGGkZ,GAAc,IAClB3P,EAASkJ,kBACT1D,GAAsBhG,EAASqO,GAAatQ,EAAO2C,OAyB/C0P,GACJ,CAACC,EAASC,IAActJ,MAAOuJ,IAC7B,IAAIC,EACAD,IACFA,EAAEE,gBAAkBF,EAAEE,iBACrBF,EAA+BG,SAC7BH,EAA+BG,WAEpC,IAAIC,EACFjZ,EAAYiH,GAMd,GAJA0L,EAAUC,MAAM1I,KAAK,CACnBmI,cAAc,IAGZvJ,EAASoK,SAAU,CACrB,MAAMpN,OAAEA,EAAMkB,OAAEA,SAAiBmM,IACjC9N,EAAWS,OAASA,EACpBmT,EAAcjS,aAERoM,EAAyB9K,GAGjC,GAAIjC,EAAOpB,SAASiU,KAClB,IAAK,MAAM3Z,KAAQ8G,EAAOpB,SACxBhD,EAAIgX,EAAa1Z,OAAM4B,GAM3B,GAFAgK,GAAM9F,EAAWS,OAAQ,QAErB0E,EAAcnF,EAAWS,QAAS,CACpC6M,EAAUC,MAAM1I,KAAK,CACnBpE,OAAQ,CAAE,IAEZ,UACQ6S,EAAQM,EAAmCJ,GACjD,MAAO7Q,GACP8Q,EAAe9Q,QAGb4Q,SACIA,EAAU,IAAKvT,EAAWS,QAAU+S,GAE5CJ,KACA9D,WAAW8D,IAUb,GAPA9F,EAAUC,MAAM1I,KAAK,CACnBkI,aAAa,EACbC,cAAc,EACdC,mBAAoB9H,EAAcnF,EAAWS,UAAYgT,EACzD5G,YAAa7M,EAAW6M,YAAc,EACtCpM,OAAQT,EAAWS,SAEjBgT,EACF,MAAMA,GAoCNK,GAAqC,CACzC7S,EACA8S,EAAmB,CAAA,KAEnB,MAAMC,EAAgB/S,EAAatG,EAAYsG,GAAc/C,EACvD+V,EAAqBtZ,EAAYqZ,GACjCE,EAAqB/O,EAAclE,GACnCU,EAASuS,EAAqBhW,EAAiB+V,EAMrD,GAJKF,EAAiBI,oBACpBjW,EAAiB8V,IAGdD,EAAiBK,WAAY,CAChC,GAAIL,EAAiBM,gBAAiB,CACpC,MAAMC,EAAgB,IAAItZ,IAAI,IACzBgG,EAAO2C,SACPxF,OAAOU,KAAK8H,GAAezI,EAAgB0D,MAEhD,IAAK,MAAMN,KAAa5H,MAAMuU,KAAKqG,GACjCvY,EAAIiE,EAAWI,YAAakB,GACxB1E,EAAI+E,EAAQL,EAAWvF,EAAI6F,EAAaN,IACxCsP,EACEtP,EACAvF,EAAI4F,EAAQL,QAGf,CACL,GAAI/G,GAASqB,EAAYqF,GACvB,IAAK,MAAM/G,KAAQ8G,EAAO2C,MAAO,CAC/B,MAAMX,EAAQjH,EAAIkH,EAAS/I,GAC3B,GAAI8I,GAASA,EAAME,GAAI,CACrB,MAAM4M,EAAiBpW,MAAMC,QAAQqJ,EAAME,GAAG0E,MAC1C5E,EAAME,GAAG0E,KAAK,GACd5E,EAAME,GAAG7D,IAEb,GAAIiG,EAAcwK,GAAiB,CACjC,MAAMyE,EAAOzE,EAAe0E,QAAQ,QACpC,GAAID,EAAM,CACRA,EAAKE,QACL,SAOV,IAAK,MAAMnT,KAAaN,EAAO2C,MAC7BiN,EACEtP,EACAvF,EAAI4F,EAAQL,IAKlBM,EAAcjH,EAAYgH,GAE1B2L,EAAUrL,MAAM4C,KAAK,CACnBlD,OAAQ,IAAKA,KAGf2L,EAAUC,MAAM1I,KAAK,CACnBlD,OAAQ,IAAKA,KAIjBX,EAAS,CACP2C,MAAOoQ,EAAiBM,gBAAkBrT,EAAO2C,MAAQ,IAAI3I,IAC7DmS,QAAS,IAAInS,IACbiH,MAAO,IAAIjH,IACX4E,SAAU,IAAI5E,IACdmG,MAAO,IAAInG,IACXuG,UAAU,EACV4B,MAAO,IAGTS,EAAOD,OACJrF,EAAgBkC,WACfuT,EAAiBjB,eACjBiB,EAAiBM,gBAErBzQ,EAAOzC,QAAUsC,EAAS1B,iBAE1BuL,EAAUC,MAAM1I,KAAK,CACnBgI,YAAakH,EAAiBW,gBAC1B1U,EAAW6M,YACX,EACJ3M,SAASgU,IAELH,EAAiBrB,UACf1S,EAAWE,WAET6T,EAAiBI,mBAChB3V,EAAUyC,EAAY/C,KAE/B6O,cAAagH,EAAiBY,iBAC1B3U,EAAW+M,YAEf3M,YAAa8T,EACT,CAAA,EACAH,EAAiBM,gBACfN,EAAiBI,mBAAqBvS,EACpC+E,GAAezI,EAAgB0D,GAC/B5B,EAAWI,YACb2T,EAAiBI,mBAAqBlT,EACpC0F,GAAezI,EAAgB+C,GAC/B8S,EAAiBrB,UACf1S,EAAWI,YACX,CAAE,EACZC,cAAe0T,EAAiBpB,YAC5B3S,EAAWK,cACX,CAAE,EACNI,OAAQsT,EAAiBa,WAAa5U,EAAWS,OAAS,CAAE,EAC5DwM,qBAAoB8G,EAAiBc,wBACjC7U,EAAWiN,mBAEfD,cAAc,GACd,EAGEyH,GAAoC,CAACxT,EAAY8S,IACrDD,GACEzO,EAAWpE,GACNA,EAAwBW,GACzBX,EACJ8S,GAqBE1B,GACJ9C,IAEAvP,EAAa,IACRA,KACAuP,EACJ,EAYG5P,GAAU,CACd7B,QAAS,CACPsE,YACA0B,cACA8N,iBACAyB,gBACAxB,YACAnR,cACAoN,aACApM,YACAmN,YACAjO,YACAkU,eAxuC0C,CAC5C5a,EACAyH,EAAS,GACToT,EACAC,EACAC,GAAkB,EAClBC,GAA6B,KAE7B,GAAIF,GAAQD,IAAWtR,EAAS7D,SAAU,CAExC,GADAgE,EAAOC,QAAS,EACZqR,GAA8Bxb,MAAMC,QAAQoC,EAAIkH,EAAS/I,IAAQ,CACnE,MAAM0Z,EAAcmB,EAAOhZ,EAAIkH,EAAS/I,GAAO8a,EAAKG,KAAMH,EAAKI,MAC/DH,GAAmBrY,EAAIqG,EAAS/I,EAAM0Z,GAGxC,GACEsB,GACAxb,MAAMC,QAAQoC,EAAIiE,EAAWS,OAAQvG,IACrC,CACA,MAAMuG,EAASsU,EACbhZ,EAAIiE,EAAWS,OAAQvG,GACvB8a,EAAKG,KACLH,EAAKI,MAEPH,GAAmBrY,EAAIoD,EAAWS,OAAQvG,EAAMuG,GKnPzC,EAAIpB,EAAQnF,MACxBuB,EAAQM,EAAIsD,EAAKnF,IAAO6C,QAAU+I,GAAMzG,EAAKnF,EAAK,ELmP7Cmb,CAAgBrV,EAAWS,OAAQvG,GAGrC,IACGoE,EAAgB+B,eACfgN,EAAyBhN,gBAC3B6U,GACAxb,MAAMC,QAAQoC,EAAIiE,EAAWK,cAAenG,IAC5C,CACA,MAAMmG,EAAgB0U,EACpBhZ,EAAIiE,EAAWK,cAAenG,GAC9B8a,EAAKG,KACLH,EAAKI,MAEPH,GAAmBrY,EAAIoD,EAAWK,cAAenG,EAAMmG,IAGrD/B,EAAgB8B,aAAeiN,EAAyBjN,eAC1DJ,EAAWI,YAAcuG,GAAezI,EAAgB0D,IAG1D0L,EAAUC,MAAM1I,KAAK,CACnB3K,OACAgG,QAAS2O,EAAU3U,EAAMyH,GACzBvB,YAAaJ,EAAWI,YACxBK,OAAQT,EAAWS,OACnBD,QAASR,EAAWQ,eAGtB5D,EAAIgF,EAAa1H,EAAMyH,IAmrCvBoC,qBACAuR,WAzqCgB7U,IAClBT,EAAWS,OAASA,EACpB6M,EAAUC,MAAM1I,KAAK,CACnBpE,OAAQT,EAAWS,OACnBD,SAAS,GACT,EAqqCA+U,eA34BFrb,GAEAuB,EACEM,EACE6H,EAAOD,MAAQ/B,EAAc1D,EAC7BhE,EACAuJ,EAAS1B,iBAAmBhG,EAAImC,EAAgBhE,EAAM,IAAM,KAs4B9D4Z,UACA0B,oBA1BwB,IAC1BnQ,EAAW5B,EAASxF,gBACnBwF,EAASxF,gBAA6BwX,MAAM9T,IAC3C8S,GAAM9S,EAAQ8B,EAASiS,cACvBpI,EAAUC,MAAM1I,KAAK,CACnB1E,WAAW,GACX,IAqBF0B,iBAv7BqB,KACvB,IAAK,MAAM3H,KAAQ8G,EAAOmM,QAAS,CACjC,MAAMnK,EAAejH,EAAIkH,EAAS/I,GAElC8I,IACGA,EAAME,GAAG0E,KACN5E,EAAME,GAAG0E,KAAKiC,OAAOxK,IAASuG,GAAKvG,MAClCuG,GAAK5C,EAAME,GAAG7D,OACnByE,GAAW5J,GAGf8G,EAAOmM,QAAU,IAAInS,GAAK,EA66BxB2a,aA1SkB/V,IAChBtD,EAAUsD,KACZ0N,EAAUC,MAAM1I,KAAK,CAAEjF,aACvBqJ,GACEhG,GACA,CAAC5D,EAAKnF,KACJ,MAAMkP,EAAsBrN,EAAIkH,EAAS/I,GACrCkP,IACF/J,EAAIO,SAAWwJ,EAAalG,GAAGtD,UAAYA,EAEvClG,MAAMC,QAAQyP,EAAalG,GAAG0E,OAChCwB,EAAalG,GAAG0E,KAAKsG,SAASrD,IAC5BA,EAASjL,SAAWwJ,EAAalG,GAAGtD,UAAYA,CAAQ,OAKhE,GACA,KAyRF0N,YACAhP,kBACA,WAAI2E,GACF,OAAOA,CACR,EACD,eAAIrB,GACF,OAAOA,CACR,EACD,UAAIgC,GACF,OAAOA,CACR,EACD,UAAIA,CAAOvK,GACTuK,EAASvK,CACV,EACD,kBAAI6E,GACF,OAAOA,CACR,EACD,UAAI8C,GACF,OAAOA,CACR,EACD,UAAIA,CAAO3H,GACT2H,EAAS3H,CACV,EACD,cAAI2G,GACF,OAAOA,CACR,EACD,YAAIyD,GACF,OAAOA,CACR,EACD,YAAIA,CAASpK,GACXoK,EAAW,IACNA,KACApK,EAEN,GAEH0L,UA7eiDrF,IACjDkE,EAAOD,OAAQ,EACf0J,EAA2B,IACtBA,KACA3N,EAAM7B,WAEJ6C,GAAW,IACbhB,EACH7B,UAAWwP,KAseboD,WACArO,YACAiR,gBACAlS,MApiBwC,CACxCjH,EAIAgC,IAEAmJ,EAAWnL,GACPoT,EAAUC,MAAMxI,UAAU,CACxBF,KAAO+Q,GACL1b,EACEwH,OAAU5F,EAAWI,GACrB0Z,KAONlU,EACExH,EACAgC,GACA,GA+gBN0U,WACAV,aACAuE,SACAoB,WApQkD,CAAC3b,EAAM6M,EAAU,CAAA,KAC/DhL,EAAIkH,EAAS/I,KACX0B,EAAYmL,EAAQ7K,cACtB0U,EAAS1W,EAAMS,EAAYoB,EAAImC,EAAgBhE,MAE/C0W,EACE1W,EACA6M,EAAQ7K,cAEVU,EAAIsB,EAAgBhE,EAAMS,EAAYoM,EAAQ7K,gBAG3C6K,EAAQ4L,aACX7M,GAAM9F,EAAWK,cAAenG,GAG7B6M,EAAQ2L,YACX5M,GAAM9F,EAAWI,YAAalG,GAC9B8F,EAAWE,QAAU6G,EAAQ7K,aACzB2S,EAAU3U,EAAMS,EAAYoB,EAAImC,EAAgBhE,KAChD2U,KAGD9H,EAAQ0L,YACX3M,GAAM9F,EAAWS,OAAQvG,GACzBoE,EAAgBkC,SAAWI,KAG7B0M,EAAUC,MAAM1I,KAAK,IAAK7E,MAyO5B8V,YA1kBqD5b,IACrDA,GACEuK,EAAsBvK,GAAMgU,SAAS6H,GACnCjQ,GAAM9F,EAAWS,OAAQsV,KAG7BzI,EAAUC,MAAM1I,KAAK,CACnBpE,OAAQvG,EAAO8F,EAAWS,OAAS,CAAE,GACrC,EAmkBFqD,cACA+N,YACAmE,SAxG8C,CAAC9b,EAAM6M,EAAU,CAAA,KAC/D,MAAM/D,EAAQjH,EAAIkH,EAAS/I,GACrB4V,EAAiB9M,GAASA,EAAME,GAEtC,GAAI4M,EAAgB,CAClB,MAAMmD,EAAWnD,EAAelI,KAC5BkI,EAAelI,KAAK,GACpBkI,EAAezQ,IAEf4T,EAAS9P,QACX8P,EAAS9P,QACT4D,EAAQkP,cACN5Q,EAAW4N,EAAS7P,SACpB6P,EAAS7P,YA4FfwO,kBAGF,MAAO,IACFjS,GACHuW,YAAavW,GAEjB,CMvgDA,IAAAwW,GAAe,KACb,MAAMC,EACmB,oBAAhBC,YAA8B/c,KAAKgd,MAA4B,IAApBD,YAAYC,MAEhE,MAAO,uCAAuC3Z,QAAQ,SAAU4Z,IAC9D,MAAMC,GAAqB,GAAhBC,KAAKC,SAAgBN,GAAK,GAAK,EAE1C,OAAa,KAALG,EAAWC,EAAS,EAAJA,EAAW,GAAKG,SAAS,GAAG,GACpD,ECLJC,GAAe,CACb1c,EACA2C,EACAkK,EAAiC,CAAA,IAEjCA,EAAQ4K,aAAe/V,EAAYmL,EAAQ4K,aACvC5K,EAAQ8P,WACR,GAAG3c,KAAQ0B,EAAYmL,EAAQ+P,YAAcja,EAAQkK,EAAQ+P,cAC7D,GCTNC,GAAe,CAAInc,EAAWvB,IAAwB,IACjDuB,KACA6J,EAAsBpL,ICJ3B2d,GAAmB3d,GACjBK,MAAMC,QAAQN,GAASA,EAAMgI,KAAI,KAAe,SAAIvF,ECO9B,SAAAmb,GACtBrc,EACAiC,EACAxD,GAEA,MAAO,IACFuB,EAAKsL,MAAM,EAAGrJ,MACd4H,EAAsBpL,MACtBuB,EAAKsL,MAAMrJ,GAElB,CChBA,IAAAqa,GAAe,CACbtc,EACAqT,EACAkJ,IAEKzd,MAAMC,QAAQiB,IAIfgB,EAAYhB,EAAKuc,MACnBvc,EAAKuc,QAAMrb,GAEblB,EAAKwc,OAAOD,EAAI,EAAGvc,EAAKwc,OAAOnJ,EAAM,GAAG,IAEjCrT,GARE,GCNXyc,GAAe,CAAIzc,EAAWvB,IAAwB,IACjDoL,EAAsBpL,MACtBoL,EAAsB7J,ICY3B,IAAA0c,GAAe,CAAI1c,EAAWiC,IAC5BjB,EAAYiB,GACR,GAdN,SAA4BjC,EAAW2c,GACrC,IAAIC,EAAI,EACR,MAAMC,EAAO,IAAI7c,GAEjB,IAAK,MAAMiC,KAAS0a,EAClBE,EAAKL,OAAOva,EAAQ2a,EAAG,GACvBA,IAGF,OAAO/b,EAAQgc,GAAM1a,OAAS0a,EAAO,EACvC,CAKMC,CACE9c,EACC6J,EAAsB5H,GAAoB8a,MAAK,CAACC,EAAGC,IAAMD,EAAIC,KCrBtEC,GAAe,CAAIld,EAAWmd,EAAgBC,MAC3Cpd,EAAKmd,GAASnd,EAAKod,IAAW,CAACpd,EAAKod,GAASpd,EAAKmd,GAAQ,ECD7DE,GAAe,CAAIrE,EAAkB/W,EAAexD,KAClDua,EAAY/W,GAASxD,EACdua,GCOT,MAAMsE,GACc,oBAAX1d,OAAyBgD,EAAM2a,gBAAkB3a,EAAMgC,6BlDwC9DE,GAEAA,EAAM0Y,OAAOtW,EAAuDpC,iBEtBtE,SAGEA,GACA,MAAMC,EAAUjC,KACT2a,EAASC,GAAc9a,EAAMuC,UAAS,IACvCjC,QACJA,EAAU6B,EAAQ7B,QAAOya,SACzBA,EAAQC,SACRA,EAAQ3U,OACRA,EAAMkR,OACNA,EAAS1Q,EAAYoU,QACrBA,EAAOC,QACPA,EAAOC,QACPA,EAAOP,OACPA,EAAMQ,UACNA,EAASC,eACTA,KACGC,GACDpZ,EAEEqZ,EAAS9O,MAAOpQ,IACpB,IAAImf,GAAW,EACX7f,EAAO,SAEL2E,EAAQuV,cAAapJ,MAAOrP,IAChC,MAAMqe,EAAW,IAAIC,SACrB,IAAIC,EAAe,GAEnB,IACEA,EAAeC,KAAKC,UAAUze,GAC9B,MAAM0e,GAAA,CAER,MAAMC,EAAoBvV,EAAQlG,EAAQ8D,aAE1C,IAAK,MAAMpG,KAAO+d,EAChBN,EAASO,OAAOhe,EAAK+d,EAAkB/d,IAazC,GAVI+c,SACIA,EAAS,CACb3d,OACAf,QACAkb,SACAkE,WACAE,iBAIAtV,EACF,IACE,MAAM4V,EAAgC,CACpChB,GAAWA,EAAQ,gBACnBC,GACA5P,MAAMzP,GAAUA,GAASA,EAAM2F,SAAS,UAEpC0a,QAAiBC,MAAMC,OAAO/V,GAAS,CAC3CkR,SACA0D,QAAS,IACJA,KACCC,EAAU,CAAE,eAAgBA,GAAY,CAAA,GAE9CmB,KAAMJ,EAAgCN,EAAeF,IAIrDS,IACCb,GACIA,EAAea,EAASI,QACzBJ,EAASI,OAAS,KAAOJ,EAASI,QAAU,MAEhDd,GAAW,EACXL,GAAWA,EAAQ,CAAEe,aACrBvgB,EAAOygB,OAAOF,EAASI,SAEvBlB,GAAaA,EAAU,CAAEc,aAE3B,MAAO/W,GACPqW,GAAW,EACXL,GAAWA,EAAQ,CAAEhW,aAtDrB7E,CAyDHjE,GAECmf,GAAYtZ,EAAM5B,UACpB4B,EAAM5B,QAAQwP,UAAUC,MAAM1I,KAAK,CACjCoI,oBAAoB,IAEtBvN,EAAM5B,QAAQ+T,SAAS,cAAe,CACpC1Y,WASN,OAJAqE,EAAMgC,WAAU,KACd8Y,GAAW,EAAK,GACf,IAEIF,EACL5a,EAAAuc,cAAAvc,EAAAwc,SAAA,KACG5B,EAAO,CACNW,YAIJvb,EAAAuc,cAAA,OAAA,CACEE,WAAY5B,EACZxU,OAAQA,EACRkR,OAAQA,EACR2D,QAASA,EACTH,SAAUQ,KACND,GAEHN,EAGP,uBZhEE9Y,IAEA,MAAM8Y,SAAEA,KAAa5d,GAAS8E,EAC9B,OACElC,EAAAuc,cAACxc,EAAgB2c,SAAQ,CAAC7gB,MAAOuB,GAC9B4d,EACwB,gI6DTzB,SAOJ9Y,GAOA,MAAMC,EAAUjC,KACVI,QACJA,EAAU6B,EAAQ7B,QAAO5D,KACzBA,EAAIigB,QACJA,EAAU,KAAIpY,iBACdA,EAAgBM,MAChBA,GACE3C,GACG6G,EAAQ6T,GAAa5c,EAAMuC,SAASjC,EAAQyX,eAAerb,IAC5DmgB,EAAM7c,EAAM8B,OAChBxB,EAAQyX,eAAerb,GAAMmH,IAAI8U,KAE7BmE,EAAY9c,EAAM8B,OAAOiH,GACzBgU,EAAQ/c,EAAM8B,OAAOpF,GACrBsgB,EAAYhd,EAAM8B,QAAO,GAE/Bib,EAAMhb,QAAUrF,EAChBogB,EAAU/a,QAAUgH,EACpBzI,EAAQkD,OAAOiB,MAAMb,IAAIlH,GAEzBmI,GACGvE,EAA2DsE,SAC1DlI,EACAmI,GAGJ7E,EAAMgC,WACJ,IACE1B,EAAQwP,UAAUrL,MAAM8C,UAAU,CAChCF,KAAM,EACJlD,SACAzH,KAAMugB,MAKN,GAAIA,IAAmBF,EAAMhb,UAAYkb,EAAgB,CACvD,MAAM7G,EAAc7X,EAAI4F,EAAQ4Y,EAAMhb,SAClC7F,MAAMC,QAAQia,KAChBwG,EAAUxG,GACVyG,EAAI9a,QAAUqU,EAAYvS,IAAI8U,SAInClR,aACL,CAACnH,IAGH,MAAM4c,EAAeld,EAAMqF,aAMvB8X,IAEAH,EAAUjb,SAAU,EACpBzB,EAAQgX,eAAe5a,EAAMygB,EAAwB,GAEvD,CAAC7c,EAAS5D,IAqRZ,OA5GAsD,EAAMgC,WAAU,KAQd,GAPA1B,EAAQ8F,OAAOC,QAAS,EAExB+E,GAAU1O,EAAM4D,EAAQkD,SACtBlD,EAAQwP,UAAUC,MAAM1I,KAAK,IACxB/G,EAAQkC,aAIbwa,EAAUjb,WACR6I,GAAmBtK,EAAQ2F,SAAS4E,MAAMC,YAC1CxK,EAAQkC,WAAW+M,eACpB3E,GAAmBtK,EAAQ2F,SAASiJ,gBAAgBpE,WAErD,GAAIxK,EAAQ2F,SAASoK,SACnB/P,EAAQgQ,WAAW,CAAC5T,IAAOub,MAAMtZ,IAC/B,MAAMwG,EAAQ5G,EAAII,EAAOsE,OAAQvG,GAC3B0gB,EAAgB7e,EAAI+B,EAAQkC,WAAWS,OAAQvG,IAGnD0gB,GACMjY,GAASiY,EAAczhB,MACxBwJ,IACEiY,EAAczhB,OAASwJ,EAAMxJ,MAC5ByhB,EAActX,UAAYX,EAAMW,SACpCX,GAASA,EAAMxJ,QAEnBwJ,EACI/F,EAAIkB,EAAQkC,WAAWS,OAAQvG,EAAMyI,GACrCmD,GAAMhI,EAAQkC,WAAWS,OAAQvG,GACrC4D,EAAQwP,UAAUC,MAAM1I,KAAK,CAC3BpE,OAAQ3C,EAAQkC,WAAWS,iBAI5B,CACL,MAAMuC,EAAejH,EAAI+B,EAAQmF,QAAS/I,IAExC8I,IACAA,EAAME,IAEJkF,GAAmBtK,EAAQ2F,SAASiJ,gBAAgBpE,YACpDF,GAAmBtK,EAAQ2F,SAAS4E,MAAMC,YAG5C0B,GACEhH,EACAlF,EAAQkD,OAAOpB,SACf9B,EAAQ8D,YACR9D,EAAQ2F,SAASkK,eAAiBtQ,EAClCS,EAAQ2F,SAAS0G,2BACjB,GACAsL,MACC9S,IACEwC,EAAcxC,IACf7E,EAAQwP,UAAUC,MAAM1I,KAAK,CAC3BpE,OAAQgJ,GACN3L,EAAQkC,WAAWS,OACnBkC,EACAzI,OAQd4D,EAAQwP,UAAUC,MAAM1I,KAAK,CAC3B3K,OACAyH,OAAQhH,EAAYmD,EAAQ8D,eAG9B9D,EAAQkD,OAAOmC,OACb8F,GAAsBnL,EAAQmF,SAAS,CAAC5D,EAAK7D,KAC3C,GACEsC,EAAQkD,OAAOmC,OACf3H,EAAIwN,WAAWlL,EAAQkD,OAAOmC,QAC9B9D,EAAI8D,MAGJ,OADA9D,EAAI8D,QACG,CAET,IAGJrF,EAAQkD,OAAOmC,MAAQ,GAEvBrF,EAAQ8C,YACR4Z,EAAUjb,SAAU,CAAK,GACxB,CAACgH,EAAQrM,EAAM4D,IAElBN,EAAMgC,WAAU,MACbzD,EAAI+B,EAAQ8D,YAAa1H,IAAS4D,EAAQgX,eAAe5a,GAEnD,KAQL4D,EAAQ2F,SAAS1B,kBAAoBA,EACjCjE,EAAQgG,WAAW5J,GARD,EAACA,EAAyBb,KAC9C,MAAM2J,EAAejH,EAAI+B,EAAQmF,QAAS/I,GACtC8I,GAASA,EAAME,KACjBF,EAAME,GAAGS,MAAQtK,IAMjBqK,CAAcxJ,GAAM,EAAM,IAE/B,CAACA,EAAM4D,EAASqc,EAASpY,IAErB,CACL8Y,KAAMrd,EAAMqF,aAlMD,CAACkV,EAAgBC,KAC5B,MAAM2C,EAA0B7c,EAAQyX,eAAerb,GACvD4d,GAAY6C,EAAyB5C,EAAQC,GAC7CF,GAAYuC,EAAI9a,QAASwY,EAAQC,GACjC0C,EAAaC,GACbP,EAAUO,GACV7c,EAAQgX,eACN5a,EACAygB,EACA7C,GACA,CACE3C,KAAM4C,EACN3C,KAAM4C,IAER,EACD,GAmL6B,CAAC0C,EAAcxgB,EAAM4D,IACnDgd,KAAMtd,EAAMqF,aAjLD,CAACoL,EAAckJ,KAC1B,MAAMwD,EAA0B7c,EAAQyX,eAAerb,GACvDgd,GAAYyD,EAAyB1M,EAAMkJ,GAC3CD,GAAYmD,EAAI9a,QAAS0O,EAAMkJ,GAC/BuD,EAAaC,GACbP,EAAUO,GACV7c,EAAQgX,eACN5a,EACAygB,EACAzD,GACA,CACE/B,KAAMlH,EACNmH,KAAM+B,IAER,EACD,GAkK6B,CAACuD,EAAcxgB,EAAM4D,IACnDid,QAASvd,EAAMqF,aA7PD,CACdxJ,EAGA0N,KAEA,MAAMiU,EAAevW,EAAsB9J,EAAYtB,IACjDshB,EAA0BtD,GAC9BvZ,EAAQyX,eAAerb,GACvB8gB,GAEFld,EAAQkD,OAAOmC,MAAQyT,GAAkB1c,EAAM,EAAG6M,GAClDsT,EAAI9a,QAAU8X,GAAUgD,EAAI9a,QAASyb,EAAa3Z,IAAI8U,KACtDuE,EAAaC,GACbP,EAAUO,GACV7c,EAAQgX,eAAe5a,EAAMygB,EAAyBtD,GAAW,CAC/DlC,KAAM6B,GAAe3d,IACrB,GA4OkC,CAACqhB,EAAcxgB,EAAM4D,IACzD0b,OAAQhc,EAAMqF,aAtRD,CACbxJ,EAGA0N,KAEA,MAAMkU,EAAcxW,EAAsB9J,EAAYtB,IAChDshB,EAA0B5D,GAC9BjZ,EAAQyX,eAAerb,GACvB+gB,GAEFnd,EAAQkD,OAAOmC,MAAQyT,GACrB1c,EACAygB,EAAwB5d,OAAS,EACjCgK,GAEFsT,EAAI9a,QAAUwX,GAASsD,EAAI9a,QAAS0b,EAAY5Z,IAAI8U,KACpDuE,EAAaC,GACbP,EAAUO,GACV7c,EAAQgX,eAAe5a,EAAMygB,EAAyB5D,GAAU,CAC9D5B,KAAM6B,GAAe3d,IACrB,GAiQgC,CAACqhB,EAAcxgB,EAAM4D,IACvDod,OAAQ1d,EAAMqF,aA3OAhG,IACd,MAAM8d,EAEArD,GAAcxZ,EAAQyX,eAAerb,GAAO2C,GAClDwd,EAAI9a,QAAU+X,GAAc+C,EAAI9a,QAAS1C,GACzC6d,EAAaC,GACbP,EAAUO,IACTjhB,MAAMC,QAAQoC,EAAI+B,EAAQmF,QAAS/I,KAClC0C,EAAIkB,EAAQmF,QAAS/I,OAAM4B,GAC7BgC,EAAQgX,eAAe5a,EAAMygB,EAAyBrD,GAAe,CACnEnC,KAAMtY,GACN,GAgOgC,CAAC6d,EAAcxgB,EAAM4D,IACvDmZ,OAAQzZ,EAAMqF,aA9ND,CACbhG,EACAxD,EAGA0N,KAEA,MAAMoU,EAAc1W,EAAsB9J,EAAYtB,IAChDshB,EAA0BS,GAC9Btd,EAAQyX,eAAerb,GACvB2C,EACAse,GAEFrd,EAAQkD,OAAOmC,MAAQyT,GAAkB1c,EAAM2C,EAAOkK,GACtDsT,EAAI9a,QAAU6b,GAASf,EAAI9a,QAAS1C,EAAOse,EAAY9Z,IAAI8U,KAC3DuE,EAAaC,GACbP,EAAUO,GACV7c,EAAQgX,eAAe5a,EAAMygB,EAAyBS,GAAU,CAC9DjG,KAAMtY,EACNuY,KAAM4B,GAAe3d,IACrB,GA0MgC,CAACqhB,EAAcxgB,EAAM4D,IACvDud,OAAQ7d,EAAMqF,aApKD,CACbhG,EACAxD,KAEA,MAAMoI,EAAc9G,EAAYtB,GAC1BshB,EAA0B1C,GAC9Bna,EAAQyX,eAENrb,GACF2C,EACA4E,GAEF4Y,EAAI9a,QAAU,IAAIob,GAAyBtZ,KAAI,CAACia,EAAM9D,IACnD8D,GAAQ9D,IAAM3a,EAAuBwd,EAAI9a,QAAQiY,GAA3BrB,OAEzBuE,EAAaC,GACbP,EAAU,IAAIO,IACd7c,EAAQgX,eACN5a,EACAygB,EACA1C,GACA,CACE9C,KAAMtY,EACNuY,KAAM3T,IAER,GACA,EACD,GAyIiC,CAACiZ,EAAcxgB,EAAM4D,IACvDnB,QAASa,EAAMqF,aAtIfxJ,IAIA,MAAMshB,EAA0BlW,EAAsB9J,EAAYtB,IAClEghB,EAAI9a,QAAUob,EAAwBtZ,IAAI8U,IAC1CuE,EAAa,IAAIC,IACjBP,EAAU,IAAIO,IACd7c,EAAQgX,eACN5a,EACA,IAAIygB,IACA/f,GAAeA,GACnB,IACA,GACA,EACD,GAuHmC,CAAC8f,EAAcxgB,EAAM4D,IACzDyI,OAAQ/I,EAAMqD,SACZ,IACE0F,EAAOlF,KAAI,CAAC2B,EAAOnG,KAAW,IACzBmG,EACHmX,CAACA,GAAUE,EAAI9a,QAAQ1C,IAAUsZ,UAErC,CAAC5P,EAAQ4T,IAGf,kBDtZgB,SAKdza,EAAkE,IAElE,MAAM6b,EAAe/d,EAAM8B,YAEzBxD,GACI0f,EAAUhe,EAAM8B,YAA4BxD,IAC3C+B,EAAWiC,GAAmBtC,EAAMuC,SAAkC,CAC3EG,SAAS,EACTK,cAAc,EACdJ,UAAWkF,EAAW3F,EAAMzB,eAC5B8O,aAAa,EACbC,cAAc,EACdC,oBAAoB,EACpBzM,SAAS,EACTqM,YAAa,EACbzM,YAAa,CAAE,EACfC,cAAe,CAAE,EACjBC,iBAAkB,CAAE,EACpBG,OAAQf,EAAMe,QAAU,CAAE,EAC1Bb,SAAUF,EAAME,WAAY,EAC5BkN,SAAS,EACT7O,cAAeoH,EAAW3F,EAAMzB,oBAC5BnC,EACA4D,EAAMzB,gBAGPsd,EAAahc,UAChBgc,EAAahc,QAAU,IACjBG,EAAMwW,YAAcxW,EAAMwW,YAActJ,GAAkBlN,GAC9D7B,aAIA6B,EAAMwW,aACNxW,EAAMzB,gBACLoH,EAAW3F,EAAMzB,gBAElByB,EAAMwW,YAAYzB,MAAM/U,EAAMzB,cAAeyB,EAAMgW,eAIvD,MAAM5X,EAAUyd,EAAahc,QAAQzB,QAiFrC,OAhFAA,EAAQ2F,SAAW/D,EAEnBwY,IAA0B,KACxB,MAAMuD,EAAM3d,EAAQ4C,WAAW,CAC7B7C,UAAWC,EAAQQ,gBACnBqC,SAAU,IAAMb,EAAgB,IAAKhC,EAAQkC,aAC7CsS,cAAc,IAUhB,OAPAxS,GAAiBlF,IAAU,IACtBA,EACHkS,SAAS,MAGXhP,EAAQkC,WAAW8M,SAAU,EAEtB2O,CAAG,GACT,CAAC3d,IAEJN,EAAMgC,WACJ,IAAM1B,EAAQ6X,aAAajW,EAAME,WACjC,CAAC9B,EAAS4B,EAAME,WAGlBpC,EAAMgC,WAAU,KACVE,EAAM2I,OACRvK,EAAQ2F,SAAS4E,KAAO3I,EAAM2I,MAE5B3I,EAAMgN,iBACR5O,EAAQ2F,SAASiJ,eAAiBhN,EAAMgN,gBAEtChN,EAAMe,SAAW0E,EAAczF,EAAMe,SACvC3C,EAAQwX,WAAW5V,EAAMe,UAE1B,CAAC3C,EAAS4B,EAAMe,OAAQf,EAAM2I,KAAM3I,EAAMgN,iBAE7ClP,EAAMgC,WAAU,KACdE,EAAMqC,kBACJjE,EAAQwP,UAAUC,MAAM1I,KAAK,CAC3BlD,OAAQ7D,EAAQ4D,aAChB,GACH,CAAC5D,EAAS4B,EAAMqC,mBAEnBvE,EAAMgC,WAAU,KACd,GAAI1B,EAAQQ,gBAAgB4B,QAAS,CACnC,MAAMA,EAAUpC,EAAQ+Q,YACpB3O,IAAYrC,EAAUqC,SACxBpC,EAAQwP,UAAUC,MAAM1I,KAAK,CAC3B3E,eAIL,CAACpC,EAASD,EAAUqC,UAEvB1C,EAAMgC,WAAU,KACVE,EAAMiC,SAAWnD,EAAUkB,EAAMiC,OAAQ6Z,EAAQjc,UACnDzB,EAAQgW,OAAOpU,EAAMiC,OAAQ7D,EAAQ2F,SAASiS,cAC9C8F,EAAQjc,QAAUG,EAAMiC,OACxB7B,GAAiByN,IAAK,IAAWA,OAEjCzP,EAAQ0X,wBAET,CAAC1X,EAAS4B,EAAMiC,SAEnBnE,EAAMgC,WAAU,KACT1B,EAAQ8F,OAAOD,QAClB7F,EAAQ8C,YACR9C,EAAQ8F,OAAOD,OAAQ,GAGrB7F,EAAQ8F,OAAOzC,QACjBrD,EAAQ8F,OAAOzC,OAAQ,EACvBrD,EAAQwP,UAAUC,MAAM1I,KAAK,IAAK/G,EAAQkC,cAG5ClC,EAAQ+D,kBAAkB,IAG5B0Z,EAAahc,QAAQ1B,UAAYD,EAAkBC,EAAWC,GAEvDyd,EAAahc,OACtB"}