!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("motion-utils")):"function"==typeof define&&define.amd?define(["exports","motion-utils"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).MotionDom={},t.MotionUtils)}(this,(function(t,e){"use strict";const n=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],i={value:null,addProjectionMetrics:null};function s(t,s){let a=!1,r=!0;const o={delta:0,timestamp:0,isProcessing:!1},l=()=>a=!0,u=n.reduce(((t,e)=>(t[e]=function(t,e){let n=new Set,s=new Set,a=!1,r=!1;const o=new WeakSet;let l={delta:0,timestamp:0,isProcessing:!1},u=0;function c(e){o.has(e)&&(h.schedule(e),t()),u++,e(l)}const h={schedule:(t,e=!1,i=!1)=>{const r=i&&a?n:s;return e&&o.add(t),r.has(t)||r.add(t),t},cancel:t=>{s.delete(t),o.delete(t)},process:t=>{l=t,a?r=!0:(a=!0,[n,s]=[s,n],n.forEach(c),e&&i.value&&i.value.frameloop[e].push(u),u=0,n.clear(),a=!1,r&&(r=!1,h.process(t)))}};return h}(l,s?e:void 0),t)),{}),{setup:c,read:h,resolveKeyframes:d,preUpdate:m,update:p,preRender:f,render:y,postRender:g}=u,v=()=>{const n=e.MotionGlobalConfig.useManualTiming?o.timestamp:performance.now();a=!1,e.MotionGlobalConfig.useManualTiming||(o.delta=r?1e3/60:Math.max(Math.min(n-o.timestamp,40),1)),o.timestamp=n,o.isProcessing=!0,c.process(o),h.process(o),d.process(o),m.process(o),p.process(o),f.process(o),y.process(o),g.process(o),o.isProcessing=!1,a&&s&&(r=!1,t(v))};return{schedule:n.reduce(((e,n)=>{const i=u[n];return e[n]=(e,n=!1,s=!1)=>(a||(a=!0,r=!0,o.isProcessing||t(v)),i.schedule(e,n,s)),e}),{}),cancel:t=>{for(let e=0;e<n.length;e++)u[n[e]].cancel(t)},state:o,steps:u}}const{schedule:a,cancel:r,state:o,steps:l}=s("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:e.noop,!0);let u;function c(){u=void 0}const h={now:()=>(void 0===u&&h.set(o.isProcessing||e.MotionGlobalConfig.useManualTiming?o.timestamp:performance.now()),u),set:t=>{u=t,queueMicrotask(c)}},d={layout:0,mainThread:0,waapi:0},m=t=>e=>"string"==typeof e&&e.startsWith(t),p=m("--"),f=m("var(--"),y=t=>!!f(t)&&g.test(t.split("/*")[0].trim()),g=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,v={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},T={...v,transform:t=>e.clamp(0,1,t)},b={...v,default:1},w=t=>Math.round(1e5*t)/1e5,M=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const x=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,A=(t,e)=>n=>Boolean("string"==typeof n&&x.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),S=(t,e,n)=>i=>{if("string"!=typeof i)return i;const[s,a,r,o]=i.match(M);return{[t]:parseFloat(s),[e]:parseFloat(a),[n]:parseFloat(r),alpha:void 0!==o?parseFloat(o):1}},k={...v,transform:t=>Math.round((t=>e.clamp(0,255,t))(t))},E={test:A("rgb","red"),parse:S("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+k.transform(t)+", "+k.transform(e)+", "+k.transform(n)+", "+w(T.transform(i))+")"};const P={test:A("#"),parse:function(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}},transform:E.transform},V=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),R=V("deg"),F=V("%"),D=V("px"),C=V("vh"),K=V("vw"),O=(()=>({...F,parse:t=>F.parse(t)/100,transform:t=>F.transform(100*t)}))(),L={test:A("hsl","hue"),parse:S("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+F.transform(w(e))+", "+F.transform(w(n))+", "+w(T.transform(i))+")"},N={test:t=>E.test(t)||P.test(t)||L.test(t),parse:t=>E.test(t)?E.parse(t):L.test(t)?L.parse(t):P.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?E.transform(t):L.transform(t)},W=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const B="number",$="color",j=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function I(t){const e=t.toString(),n=[],i={color:[],number:[],var:[]},s=[];let a=0;const r=e.replace(j,(t=>(N.test(t)?(i.color.push(a),s.push($),n.push(N.parse(t))):t.startsWith("var(")?(i.var.push(a),s.push("var"),n.push(t)):(i.number.push(a),s.push(B),n.push(parseFloat(t))),++a,"${}"))).split("${}");return{values:n,split:r,indexes:i,types:s}}function Y(t){return I(t).values}function X(t){const{split:e,types:n}=I(t),i=e.length;return t=>{let s="";for(let a=0;a<i;a++)if(s+=e[a],void 0!==t[a]){const e=n[a];s+=e===B?w(t[a]):e===$?N.transform(t[a]):t[a]}return s}}const U=t=>"number"==typeof t?0:t;const q={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(M)?.length||0)+(t.match(W)?.length||0)>0},parse:Y,createTransformer:X,getAnimatableNone:function(t){const e=Y(t);return X(t)(e.map(U))}};function G(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function z({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,n/=100;let s=0,a=0,r=0;if(e/=100){const i=n<.5?n*(1+e):n+e-n*e,o=2*n-i;s=G(o,i,t+1/3),a=G(o,i,t),r=G(o,i,t-1/3)}else s=a=r=n;return{red:Math.round(255*s),green:Math.round(255*a),blue:Math.round(255*r),alpha:i}}function Z(t,e){return n=>n>0?e:t}const _=(t,e,n)=>t+(e-t)*n,H=(t,e,n)=>{const i=t*t,s=n*(e*e-i)+i;return s<0?0:Math.sqrt(s)},J=[P,E,L];function Q(t){const n=(i=t,J.find((t=>t.test(i))));var i;if(e.warning(Boolean(n),`'${t}' is not an animatable color. Use the equivalent color code instead.`),!Boolean(n))return!1;let s=n.parse(t);return n===L&&(s=z(s)),s}const tt=(t,e)=>{const n=Q(t),i=Q(e);if(!n||!i)return Z(t,e);const s={...n};return t=>(s.red=H(n.red,i.red,t),s.green=H(n.green,i.green,t),s.blue=H(n.blue,i.blue,t),s.alpha=_(n.alpha,i.alpha,t),E.transform(s))},et=new Set(["none","hidden"]);function nt(t,e){return et.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function it(t,e){return n=>_(t,e,n)}function st(t){return"number"==typeof t?it:"string"==typeof t?y(t)?Z:N.test(t)?tt:ot:Array.isArray(t)?at:"object"==typeof t?N.test(t)?tt:rt:Z}function at(t,e){const n=[...t],i=n.length,s=t.map(((t,n)=>st(t)(t,e[n])));return t=>{for(let e=0;e<i;e++)n[e]=s[e](t);return n}}function rt(t,e){const n={...t,...e},i={};for(const s in n)void 0!==t[s]&&void 0!==e[s]&&(i[s]=st(t[s])(t[s],e[s]));return t=>{for(const e in i)n[e]=i[e](t);return n}}const ot=(t,n)=>{const i=q.createTransformer(n),s=I(t),a=I(n);return s.indexes.var.length===a.indexes.var.length&&s.indexes.color.length===a.indexes.color.length&&s.indexes.number.length>=a.indexes.number.length?et.has(t)&&!a.values.length||et.has(n)&&!s.values.length?nt(t,n):e.pipe(at(function(t,e){const n=[],i={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){const a=e.types[s],r=t.indexes[a][i[a]],o=t.values[r]??0;n[s]=o,i[a]++}return n}(s,a),a.values),i):(e.warning(!0,`Complex values '${t}' and '${n}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),Z(t,n))};function lt(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return _(t,e,n);return st(t)(t,e)}const ut=t=>{const e=({timestamp:e})=>t(e);return{start:()=>a.update(e,!0),stop:()=>r(e),now:()=>o.isProcessing?o.timestamp:h.now()}},ct=(t,e,n=10)=>{let i="";const s=Math.max(Math.round(e/n),2);for(let e=0;e<s;e++)i+=t(e/(s-1))+", ";return`linear(${i.substring(0,i.length-2)})`},ht=2e4;function dt(t){let e=0;let n=t.next(e);for(;!n.done&&e<ht;)e+=50,n=t.next(e);return e>=ht?1/0:e}function mt(t,n=100,i){const s=i({...t,keyframes:[0,n]}),a=Math.min(dt(s),ht);return{type:"keyframes",ease:t=>s.next(a*t).value/n,duration:e.millisecondsToSeconds(a)}}function pt(t,n,i){const s=Math.max(n-5,0);return e.velocityPerSecond(i-t(s),n-s)}const ft=100,yt=10,gt=1,vt=0,Tt=800,bt=.3,wt=.3,Mt={granular:.01,default:2},xt={granular:.005,default:.5},At=.01,St=10,kt=.05,Et=1,Pt=.001;function Vt({duration:t=Tt,bounce:n=bt,velocity:i=vt,mass:s=gt}){let a,r;e.warning(t<=e.secondsToMilliseconds(St),"Spring duration must be 10 seconds or less");let o=1-n;o=e.clamp(kt,Et,o),t=e.clamp(At,St,e.millisecondsToSeconds(t)),o<1?(a=e=>{const n=e*o,s=n*t,a=n-i,r=Ft(e,o),l=Math.exp(-s);return Pt-a/r*l},r=e=>{const n=e*o*t,s=n*i+i,r=Math.pow(o,2)*Math.pow(e,2)*t,l=Math.exp(-n),u=Ft(Math.pow(e,2),o);return(-a(e)+Pt>0?-1:1)*((s-r)*l)/u}):(a=e=>Math.exp(-e*t)*((e-i)*t+1)-.001,r=e=>Math.exp(-e*t)*(t*t*(i-e)));const l=function(t,e,n){let i=n;for(let n=1;n<Rt;n++)i-=t(i)/e(i);return i}(a,r,5/t);if(t=e.secondsToMilliseconds(t),isNaN(l))return{stiffness:ft,damping:yt,duration:t};{const e=Math.pow(l,2)*s;return{stiffness:e,damping:2*o*Math.sqrt(s*e),duration:t}}}const Rt=12;function Ft(t,e){return t*Math.sqrt(1-e*e)}const Dt=["duration","bounce"],Ct=["stiffness","damping","mass"];function Kt(t,e){return e.some((e=>void 0!==t[e]))}function Ot(t=wt,n=bt){const i="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:n}:t;let{restSpeed:s,restDelta:a}=i;const r=i.keyframes[0],o=i.keyframes[i.keyframes.length-1],l={done:!1,value:r},{stiffness:u,damping:c,mass:h,duration:d,velocity:m,isResolvedFromDuration:p}=function(t){let n={velocity:vt,stiffness:ft,damping:yt,mass:gt,isResolvedFromDuration:!1,...t};if(!Kt(t,Ct)&&Kt(t,Dt))if(t.visualDuration){const i=t.visualDuration,s=2*Math.PI/(1.2*i),a=s*s,r=2*e.clamp(.05,1,1-(t.bounce||0))*Math.sqrt(a);n={...n,mass:gt,stiffness:a,damping:r}}else{const e=Vt(t);n={...n,...e,mass:gt},n.isResolvedFromDuration=!0}return n}({...i,velocity:-e.millisecondsToSeconds(i.velocity||0)}),f=m||0,y=c/(2*Math.sqrt(u*h)),g=o-r,v=e.millisecondsToSeconds(Math.sqrt(u/h)),T=Math.abs(g)<5;let b;if(s||(s=T?Mt.granular:Mt.default),a||(a=T?xt.granular:xt.default),y<1){const t=Ft(v,y);b=e=>{const n=Math.exp(-y*v*e);return o-n*((f+y*v*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}}else if(1===y)b=t=>o-Math.exp(-v*t)*(g+(f+v*g)*t);else{const t=v*Math.sqrt(y*y-1);b=e=>{const n=Math.exp(-y*v*e),i=Math.min(t*e,300);return o-n*((f+y*v*g)*Math.sinh(i)+t*g*Math.cosh(i))/t}}const w={calculatedDuration:p&&d||null,next:t=>{const n=b(t);if(p)l.done=t>=d;else{let i=0===t?f:0;y<1&&(i=0===t?e.secondsToMilliseconds(f):pt(b,t,n));const r=Math.abs(i)<=s,u=Math.abs(o-n)<=a;l.done=r&&u}return l.value=l.done?o:n,l},toString:()=>{const t=Math.min(dt(w),ht),e=ct((e=>w.next(t*e).value),t,30);return t+"ms "+e},toTransition:()=>{}};return w}function Lt({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:a=500,modifyTarget:r,min:o,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],d={done:!1,value:h},m=t=>void 0===o?l:void 0===l||Math.abs(o-t)<Math.abs(l-t)?o:l;let p=n*e;const f=h+p,y=void 0===r?f:r(f);y!==f&&(p=y-h);const g=t=>-p*Math.exp(-t/i),v=t=>y+g(t),T=t=>{const e=g(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?y:n};let b,w;const M=t=>{var e;(e=d.value,void 0!==o&&e<o||void 0!==l&&e>l)&&(b=t,w=Ot({keyframes:[d.value,m(d.value)],velocity:pt(v,t,d.value),damping:s,stiffness:a,restDelta:u,restSpeed:c}))};return M(0),{calculatedDuration:null,next:t=>{let e=!1;return w||void 0!==b||(e=!0,T(t),M(t)),void 0!==b&&t>=b?w.next(t-b):(!e&&T(t),d)}}}function Nt(t,n,{clamp:i=!0,ease:s,mixer:a}={}){const r=t.length;if(e.invariant(r===n.length,"Both input and output ranges must be the same length"),1===r)return()=>n[0];if(2===r&&n[0]===n[1])return()=>n[1];const o=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),n=[...n].reverse());const l=function(t,n,i){const s=[],a=i||e.MotionGlobalConfig.mix||lt,r=t.length-1;for(let i=0;i<r;i++){let r=a(t[i],t[i+1]);if(n){const t=Array.isArray(n)?n[i]||e.noop:n;r=e.pipe(t,r)}s.push(r)}return s}(n,s,a),u=l.length,c=i=>{if(o&&i<t[0])return n[0];let s=0;if(u>1)for(;s<t.length-2&&!(i<t[s+1]);s++);const a=e.progress(t[s],t[s+1],i);return l[s](a)};return i?n=>c(e.clamp(t[0],t[r-1],n)):c}function Wt(t,n){const i=t[t.length-1];for(let s=1;s<=n;s++){const a=e.progress(0,n,s);t.push(_(i,1,a))}}function Bt(t){const e=[0];return Wt(e,t.length-1),e}function $t(t,e){return t.map((t=>t*e))}function jt(t,n){return t.map((()=>n||e.easeInOut)).splice(0,t.length-1)}function It({duration:t=300,keyframes:n,times:i,ease:s="easeInOut"}){const a=e.isEasingArray(s)?s.map(e.easingDefinitionToFunction):e.easingDefinitionToFunction(s),r={done:!1,value:n[0]},o=Nt($t(i&&i.length===n.length?i:Bt(n),t),n,{ease:Array.isArray(a)?a:jt(n,a)});return{calculatedDuration:t,next:e=>(r.value=o(e),r.done=e>=t,r)}}Ot.applyToOptions=t=>{const n=mt(t,100,Ot);return t.ease=n.ease,t.duration=e.secondsToMilliseconds(n.duration),t.type="keyframes",t};const Yt=t=>null!==t;function Xt(t,{repeat:e,repeatType:n="loop"},i,s=1){const a=t.filter(Yt),r=s<0||e&&"loop"!==n&&e%2==1?0:a.length-1;return r&&void 0!==i?i:a[r]}const Ut={decay:Lt,inertia:Lt,tween:It,keyframes:It,spring:Ot};function qt(t){"string"==typeof t.type&&(t.type=Ut[t.type])}class Gt{constructor(){this.count=0,this.updateFinished()}get finished(){return this._finished}updateFinished(){this.count++,this._finished=new Promise((t=>{this.resolve=t}))}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const zt=t=>t/100;class Zt extends Gt{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:t}=this.options;if(t&&t.updatedAt!==h.now()&&this.tick(h.now()),this.isStopped=!0,"idle"===this.state)return;this.teardown();const{onStop:e}=this.options;e&&e()},d.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;qt(t);const{type:n=It,repeat:i=0,repeatDelay:s=0,repeatType:a,velocity:r=0}=t;let{keyframes:o}=t;const l=n||It;l!==It&&"number"!=typeof o[0]&&(this.mixKeyframes=e.pipe(zt,lt(o[0],o[1])),o=[0,100]);const u=l({...t,keyframes:o});"mirror"===a&&(this.mirroredGenerator=l({...t,keyframes:[...o].reverse(),velocity:-r})),null===u.calculatedDuration&&(u.calculatedDuration=dt(u));const{calculatedDuration:c}=u;this.calculatedDuration=c,this.resolvedDuration=c+s,this.totalDuration=this.resolvedDuration*(i+1)-s,this.generator=u}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,n=!1){const{generator:i,totalDuration:s,mixKeyframes:a,mirroredGenerator:r,resolvedDuration:o,calculatedDuration:l}=this;if(null===this.startTime)return i.next(0);const{delay:u=0,keyframes:c,repeat:h,repeatType:d,repeatDelay:m,type:p,onUpdate:f,finalKeyframe:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-s/this.speed,this.startTime)),n?this.currentTime=t:this.updateTime(t);const g=this.currentTime-u*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>s;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=s);let T=this.currentTime,b=i;if(h){const t=Math.min(this.currentTime,s)/o;let n=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&n--,n=Math.min(n,h+1);Boolean(n%2)&&("reverse"===d?(i=1-i,m&&(i-=m/o)):"mirror"===d&&(b=r)),T=e.clamp(0,1,i)*o}const w=v?{done:!1,value:c[0]}:b.next(T);a&&(w.value=a(w.value));let{done:M}=w;v||null===l||(M=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);const x=null===this.holdTime&&("finished"===this.state||"running"===this.state&&M);return x&&p!==Lt&&(w.value=Xt(c,this.options,y,this.speed)),f&&f(w.value),x&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return e.millisecondsToSeconds(this.calculatedDuration)}get time(){return e.millisecondsToSeconds(this.currentTime)}set time(t){t=e.secondsToMilliseconds(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(h.now());const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=e.millisecondsToSeconds(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=ut,onPlay:e,startTime:n}=this.options;this.driver||(this.driver=t((t=>this.tick(t)))),e&&e();const i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=n??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(h.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown()}teardown(){this.notifyFinished(),this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,d.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),t.observe(this)}}function _t(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const Ht=t=>180*t/Math.PI,Jt=t=>{const e=Ht(Math.atan2(t[1],t[0]));return te(e)},Qt={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Jt,rotateZ:Jt,skewX:t=>Ht(Math.atan(t[1])),skewY:t=>Ht(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},te=t=>((t%=360)<0&&(t+=360),t),ee=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),ne=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),ie={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ee,scaleY:ne,scale:t=>(ee(t)+ne(t))/2,rotateX:t=>te(Ht(Math.atan2(t[6],t[5]))),rotateY:t=>te(Ht(Math.atan2(-t[2],t[0]))),rotateZ:Jt,rotate:Jt,skewX:t=>Ht(Math.atan(t[4])),skewY:t=>Ht(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function se(t){return t.includes("scale")?1:0}function ae(t,e){if(!t||"none"===t)return se(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let i,s;if(n)i=ie,s=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=Qt,s=e}if(!s)return se(e);const a=i[e],r=s[1].split(",").map(re);return"function"==typeof a?a(r):r[a]}function re(t){return parseFloat(t.trim())}const oe=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],le=(()=>new Set(oe))(),ue=t=>t===v||t===D,ce=new Set(["x","y","z"]),he=oe.filter((t=>!ce.has(t)));const de={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>ae(e,"x"),y:(t,{transform:e})=>ae(e,"y")};de.translateX=de.x,de.translateY=de.y;const me=new Set;let pe=!1,fe=!1,ye=!1;function ge(){if(fe){const t=Array.from(me).filter((t=>t.needsMeasurement)),e=new Set(t.map((t=>t.element))),n=new Map;e.forEach((t=>{const e=function(t){const e=[];return he.forEach((n=>{const i=t.getValue(n);void 0!==i&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))})),e}(t);e.length&&(n.set(t,e),t.render())})),t.forEach((t=>t.measureInitialState())),e.forEach((t=>{t.render();const e=n.get(t);e&&e.forEach((([e,n])=>{t.getValue(e)?.set(n)}))})),t.forEach((t=>t.measureEndState())),t.forEach((t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)}))}fe=!1,pe=!1,me.forEach((t=>t.complete(ye))),me.clear()}function ve(){me.forEach((t=>{t.readKeyframes(),t.needsMeasurement&&(fe=!0)}))}function Te(){ye=!0,ve(),ge(),ye=!1}class be{constructor(t,e,n,i,s,a=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=i,this.element=s,this.isAsync=a}scheduleResolve(){this.isScheduled=!0,this.isAsync?(me.add(this),pe||(pe=!0,a.read(ve),a.resolveKeyframes(ge))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:i}=this;if(null===t[0]){const s=i?.get(),a=t[t.length-1];if(void 0!==s)t[0]=s;else if(n&&e){const i=n.readValue(e,a);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=a),i&&void 0===s&&i.set(t[0])}_t(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),me.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,me.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const we=t=>t.startsWith("--");function Me(t,e,n){we(e)?t.style.setProperty(e,n):t.style[e]=n}const xe=e.memo((()=>void 0!==window.ScrollTimeline)),Ae={};function Se(t,n){const i=e.memo(t);return()=>Ae[n]??i()}const ke=Se((()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}),"linearEasing"),Ee=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,Pe={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Ee([0,.65,.55,1]),circOut:Ee([.55,0,1,.45]),backIn:Ee([.31,.01,.66,-.59]),backOut:Ee([.33,1.53,.69,.99])};function Ve(t,n){return t?"function"==typeof t?ke()?ct(t,n):"ease-out":e.isBezierDefinition(t)?Ee(t):Array.isArray(t)?t.map((t=>Ve(t,n)||Pe.easeOut)):Pe[t]:void 0}function Re(t,e,n,{delay:s=0,duration:a=300,repeat:r=0,repeatType:o="loop",ease:l="easeOut",times:u}={},c=void 0){const h={[e]:n};u&&(h.offset=u);const m=Ve(l,a);Array.isArray(m)&&(h.easing=m),i.value&&d.waapi++;const p={delay:s,duration:a,easing:Array.isArray(m)?"linear":m,fill:"both",iterations:r+1,direction:"reverse"===o?"alternate":"normal"};c&&(p.pseudoElement=c);const f=t.animate(h,p);return i.value&&f.finished.finally((()=>{d.waapi--})),f}function Fe(t){return"function"==typeof t&&"applyToOptions"in t}function De({type:t,...e}){return Fe(t)&&ke()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class Ce extends Gt{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:n,name:i,keyframes:s,pseudoElement:a,allowFlatten:r=!1,finalKeyframe:o,onComplete:l}=t;this.isPseudoElement=Boolean(a),this.allowFlatten=r,this.options=t,e.invariant("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');const u=De(t);this.animation=Re(n,i,s,u,a),!1===u.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!a){const t=Xt(s,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):Me(n,i,t),this.animation.cancel()}l?.(),this.notifyFinished()},this.animation.oncancel=()=>this.notifyFinished()}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return e.millisecondsToSeconds(Number(t))}get time(){return e.millisecondsToSeconds(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=e.secondsToMilliseconds(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:n}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&xe()?(this.animation.timeline=t,e.noop):n(this)}}const Ke={anticipate:e.anticipate,backInOut:e.backInOut,circInOut:e.circInOut};function Oe(t){"string"==typeof t.ease&&t.ease in Ke&&(t.ease=Ke[t.ease])}class Le extends Ce{constructor(t){Oe(t),qt(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:n,onUpdate:i,onComplete:s,element:a,...r}=this.options;if(!n)return;if(void 0!==t)return void n.set(t);const o=new Zt({...r,autoplay:!1}),l=e.secondsToMilliseconds(this.finishedTime??this.time);n.setWithVelocity(o.sample(l-10).value,o.sample(l).value,10),o.stop()}}const Ne=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!q.test(t)&&"0"!==t||t.startsWith("url(")));const We=new Set(["opacity","clipPath","filter","transform"]),Be=e.memo((()=>Object.hasOwnProperty.call(Element.prototype,"animate")));function $e(t){const{motionValue:e,name:n,repeatDelay:i,repeatType:s,damping:a,type:r}=t;if(!(e&&e.owner&&e.owner.current instanceof HTMLElement))return!1;const{onUpdate:o,transformTemplate:l}=e.owner.getProps();return Be()&&n&&We.has(n)&&("transform"!==n||!l)&&!o&&!i&&"mirror"!==s&&0!==a&&"inertia"!==r}class je{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map((t=>t.finished)))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=e}attachTimeline(t){const e=this.animations.map((e=>e.attachTimeline(t)));return()=>{e.forEach(((t,e)=>{t&&t(),this.animations[e].stop()}))}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach((e=>e[t]()))}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class Ie extends Ce{constructor(t){super(),this.animation=t,t.onfinish=()=>{this.finishedTime=this.time,this.notifyFinished()}}}const Ye=new WeakMap;const Xe=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Ue(t){const e=Xe.exec(t);if(!e)return[,];const[,n,i,s]=e;return[`--${n??i}`,s]}function qe(t,n,i=1){e.invariant(i<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);const[s,a]=Ue(t);if(!s)return;const r=window.getComputedStyle(n).getPropertyValue(s);if(r){const t=r.trim();return e.isNumericalString(t)?parseFloat(t):t}return y(a)?qe(a,n,i+1):a}function Ge(t,e){return t?.[e]??t?.default??t}const ze=new Set(["width","height","top","left","right","bottom",...oe]),Ze=t=>e=>e.test(t),_e=[v,D,F,R,K,C,{test:t=>"auto"===t,parse:t=>t}],He=t=>_e.find(Ze(t));const Je=new Set(["brightness","contrast","saturate","opacity"]);function Qe(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[i]=n.match(M)||[];if(!i)return t;const s=n.replace(i,"");let a=Je.has(e)?1:0;return i!==n&&(a*=100),e+"("+a+s+")"}const tn=/\b([a-z-]*)\(.*?\)/gu,en={...q,getAnimatableNone:t=>{const e=t.match(tn);return e?e.map(Qe).join(" "):t}},nn={...v,transform:Math.round},sn={rotate:R,rotateX:R,rotateY:R,rotateZ:R,scale:b,scaleX:b,scaleY:b,scaleZ:b,skew:R,skewX:R,skewY:R,distance:D,translateX:D,translateY:D,translateZ:D,x:D,y:D,z:D,perspective:D,transformPerspective:D,opacity:T,originX:O,originY:O,originZ:D},an={borderWidth:D,borderTopWidth:D,borderRightWidth:D,borderBottomWidth:D,borderLeftWidth:D,borderRadius:D,radius:D,borderTopLeftRadius:D,borderTopRightRadius:D,borderBottomRightRadius:D,borderBottomLeftRadius:D,width:D,maxWidth:D,height:D,maxHeight:D,top:D,right:D,bottom:D,left:D,padding:D,paddingTop:D,paddingRight:D,paddingBottom:D,paddingLeft:D,margin:D,marginTop:D,marginRight:D,marginBottom:D,marginLeft:D,backgroundPositionX:D,backgroundPositionY:D,...sn,zIndex:nn,fillOpacity:T,strokeOpacity:T,numOctaves:nn},rn={...an,color:N,backgroundColor:N,outlineColor:N,fill:N,stroke:N,borderColor:N,borderTopColor:N,borderRightColor:N,borderBottomColor:N,borderLeftColor:N,filter:en,WebkitFilter:en},on=t=>rn[t];function ln(t,e){let n=on(t);return n!==en&&(n=q),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const un=new Set(["auto","none","0"]);const cn=new Set(["borderWidth","borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","borderRadius","radius","borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius","width","maxWidth","height","maxHeight","top","right","bottom","left","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","margin","marginTop","marginRight","marginBottom","marginLeft","backgroundPositionX","backgroundPositionY"]);const hn=e.memo((()=>{try{document.createElement("div").animate({opacity:[1]})}catch(t){return!1}return!0})),dn=new Set(["opacity","clipPath","filter","transform"]);function mn(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let i=document;e&&(i=e.current);const s=n?.[t]??i.querySelectorAll(t);return s?Array.from(s):[]}return Array.from(t)}const{schedule:pn,cancel:fn}=s(queueMicrotask,!1),yn={x:!1,y:!1};function gn(){return yn.x||yn.y}function vn(t,e){const n=mn(t),i=new AbortController;return[n,{passive:!0,...e,signal:i.signal},()=>i.abort()]}function Tn(t){return!("touch"===t.pointerType||gn())}const bn=(t,e)=>!!e&&(t===e||bn(t,e.parentElement)),wn=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,Mn=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const xn=new WeakSet;function An(t){return e=>{"Enter"===e.key&&t(e)}}function Sn(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function kn(t){return wn(t)&&!gn()}function En(){const{value:t}=i;null!==t?(t.frameloop.rate.push(o.delta),t.animations.mainThread.push(d.mainThread),t.animations.waapi.push(d.waapi),t.animations.layout.push(d.layout)):r(En)}function Pn(t){return t.reduce(((t,e)=>t+e),0)/t.length}function Vn(t,e=Pn){return 0===t.length?{min:0,max:0,avg:0}:{min:Math.min(...t),max:Math.max(...t),avg:e(t)}}const Rn=t=>Math.round(1e3/t);function Fn(){i.value=null,i.addProjectionMetrics=null}function Dn(){const{value:t}=i;if(!t)throw new Error("Stats are not being measured");Fn(),r(En);const e={frameloop:{setup:Vn(t.frameloop.setup),rate:Vn(t.frameloop.rate),read:Vn(t.frameloop.read),resolveKeyframes:Vn(t.frameloop.resolveKeyframes),preUpdate:Vn(t.frameloop.preUpdate),update:Vn(t.frameloop.update),preRender:Vn(t.frameloop.preRender),render:Vn(t.frameloop.render),postRender:Vn(t.frameloop.postRender)},animations:{mainThread:Vn(t.animations.mainThread),waapi:Vn(t.animations.waapi),layout:Vn(t.animations.layout)},layoutProjection:{nodes:Vn(t.layoutProjection.nodes),calculatedTargetDeltas:Vn(t.layoutProjection.calculatedTargetDeltas),calculatedProjections:Vn(t.layoutProjection.calculatedProjections)}},{rate:n}=e.frameloop;return n.min=Rn(n.min),n.max=Rn(n.max),n.avg=Rn(n.avg),[n.min,n.max]=[n.max,n.min],e}function Cn(...t){const e=!Array.isArray(t[0]),n=e?0:-1,i=t[0+n],s=Nt(t[1+n],t[2+n],t[3+n]);return e?s(i):s}const Kn={current:void 0};class On{constructor(t,e={}){this.version="12.9.1",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const n=h.now();this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change?.notify(this.current),e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=h.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new e.SubscriptionManager);const i=this.events[t].add(n);return"change"===t?()=>{i(),a.read((()=>{this.events.change.getSize()||this.stop()}))}:i}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return Kn.current&&Kn.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const t=h.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e.velocityPerSecond(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise((e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Ln(t,e){return new On(t,e)}function Nn(t){const e=[];Kn.current=e;const n=t();Kn.current=void 0;const i=Ln(n);return function(t,e,n){const i=()=>e.set(n()),s=()=>a.preRender(i,!1,!0),o=t.map((t=>t.on("change",s)));e.on("destroy",(()=>{o.forEach((t=>t())),r(i)}))}(e,i,t),i}const Wn=[..._e,N,q];function Bn(t){return"layout"===t?"group":"enter"===t||"new"===t?"new":"exit"===t||"old"===t?"old":"group"}let $n={},jn=null;const In=(t,e)=>{$n[t]=e},Yn=()=>{jn||(jn=document.createElement("style"),jn.id="motion-view");let t="";for(const e in $n){const n=$n[e];t+=`${e} {\n`;for(const[e,i]of Object.entries(n))t+=`  ${e}: ${i};\n`;t+="}\n"}jn.textContent=t,document.head.appendChild(jn),$n={}},Xn=()=>{jn&&jn.parentElement&&jn.parentElement.removeChild(jn)};function Un(t){const e=t.match(/::view-transition-(old|new|group|image-pair)\((.*?)\)/);return e?{layer:e[2],type:e[1]}:null}function qn(t){const{effect:e}=t;return!!e&&(e.target===document.documentElement&&e.pseudoElement?.startsWith("::view-transition"))}const Gn=["layout","enter","exit","new","old"];function zn(t){const{update:n,targets:i,options:s}=t;if(!document.startViewTransition)return new Promise((async t=>{await n(),t(new je([]))}));(function(t,e){return e.has(t)&&Object.keys(e.get(t)).length>0})("root",i)||In(":root",{"view-transition-name":"none"}),In("::view-transition-group(*), ::view-transition-old(*), ::view-transition-new(*)",{"animation-timing-function":"linear !important"}),Yn();const a=document.startViewTransition((async()=>{await n()}));return a.finished.finally((()=>{Xn()})),new Promise((t=>{a.ready.then((()=>{const n=document.getAnimations().filter(qn),a=[];i.forEach(((t,n)=>{for(const i of Gn){if(!t[i])continue;const{keyframes:r,options:o}=t[i];for(let[t,l]of Object.entries(r)){if(!l)continue;const r={...Ge(s,t),...Ge(o,t)},u=Bn(i);if("opacity"===t&&!Array.isArray(l)){l=["new"===u?0:1,l]}"function"==typeof r.delay&&(r.delay=r.delay(0,1)),r.duration&&(r.duration=e.secondsToMilliseconds(r.duration)),r.delay&&(r.delay=e.secondsToMilliseconds(r.delay));const c=new Ce({...r,element:document.documentElement,name:t,pseudoElement:`::view-transition-${u}(${n})`,keyframes:l});a.push(c)}}}));for(const t of n){if("finished"===t.playState)continue;const{effect:n}=t;if(!(n&&n instanceof KeyframeEffect))continue;const{pseudoElement:r}=n;if(!r)continue;const o=Un(r);if(!o)continue;const l=i.get(o.layer);if(l)Zn(l,"enter")&&Zn(l,"exit")&&n.getKeyframes().some((t=>t.mixBlendMode))?a.push(new Ie(t)):t.cancel();else{const i="group"===o.type?"layout":"";let r={...Ge(s,i)};r.duration&&(r.duration=e.secondsToMilliseconds(r.duration)),r=De(r);const l=Ve(r.ease,r.duration);n.updateTiming({delay:e.secondsToMilliseconds(r.delay??0),duration:r.duration,easing:l}),a.push(new Ie(t))}}t(new je(a))}))}))}function Zn(t,e){return t?.[e]?.keyframes.opacity}let _n=[],Hn=null;function Jn(){Hn=null;const[t]=_n;var n;t&&(n=t,e.removeItem(_n,n),Hn=n,zn(n).then((t=>{n.notifyReady(t),t.finished.finally(Jn)})))}function Qn(){for(let t=_n.length-1;t>=0;t--){const e=_n[t],{interrupt:n}=e.options;if("immediate"===n){const n=_n.slice(0,t+1).map((t=>t.update)),i=_n.slice(t+1);e.update=()=>{n.forEach((t=>t()))},_n=[e,...i];break}}Hn&&"immediate"!==_n[0]?.options.interrupt||Jn()}class ti{constructor(t,n={}){var i;this.currentTarget="root",this.targets=new Map,this.notifyReady=e.noop,this.readyPromise=new Promise((t=>{this.notifyReady=t})),this.update=t,this.options={interrupt:"wait",...n},i=this,_n.push(i),pn.render(Qn)}get(t){return this.currentTarget=t,this}layout(t,e){return this.updateTarget("layout",t,e),this}new(t,e){return this.updateTarget("new",t,e),this}old(t,e){return this.updateTarget("old",t,e),this}enter(t,e){return this.updateTarget("enter",t,e),this}exit(t,e){return this.updateTarget("exit",t,e),this}crossfade(t){return this.updateTarget("enter",{opacity:1},t),this.updateTarget("exit",{opacity:0},t),this}updateTarget(t,e,n={}){const{currentTarget:i,targets:s}=this;s.has(i)||s.set(i,{});s.get(i)[t]={keyframes:e,options:n}}then(t,e){return this.readyPromise.then(t,e)}}const ei=a,ni=n.reduce(((t,e)=>(t[e]=t=>r(t),t)),{});t.AsyncMotionValueAnimation=class extends Gt{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:a="loop",keyframes:r,name:o,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation?(this._animation.stop(),this.stopTimeline?.()):this.keyframeResolver?.cancel()},this.createdAt=h.now();const d={autoplay:t,delay:e,type:n,repeat:i,repeatDelay:s,repeatType:a,name:o,motionValue:l,element:u,...c},m=u?.KeyframeResolver||be;this.keyframeResolver=new m(r,((t,e,n)=>this.onKeyframesResolved(t,e,d,!n)),o,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,n,i,s){this.keyframeResolver=void 0;const{name:a,type:r,velocity:o,delay:l,isHandoff:u,onUpdate:c}=i;this.resolvedAt=h.now(),function(t,n,i,s){const a=t[0];if(null===a)return!1;if("display"===n||"visibility"===n)return!0;const r=t[t.length-1],o=Ne(a,n),l=Ne(r,n);return e.warning(o===l,`You are trying to animate ${n} from "${a}" to "${r}". ${a} is not an animatable value - to enable this animation set ${a} to a value animatable to ${r} via the \`style\` property.`),!(!o||!l)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===i||Fe(i))&&s)}(t,a,r,o)||(!e.MotionGlobalConfig.instantAnimations&&l||c?.(Xt(t,i,n)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);const d={startTime:s?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:n,...i,keyframes:t},m=!u&&$e(d)?new Le({...d,element:d.motionValue.owner.current}):new Zt(d);m.finished.then((()=>this.notifyFinished())).catch(e.noop),this.pendingTimeline&&(this.stopTimeline=m.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=m}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then((()=>{}))}get animation(){return this._animation||Te(),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this.animation.cancel()}},t.DOMKeyframesResolver=class extends be{constructor(t,e,n,i,s){super(t,e,n,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let i=t[n];if("string"==typeof i&&(i=i.trim(),y(i))){const s=qe(i,e.current);void 0!==s&&(t[n]=s),n===t.length-1&&(this.finalKeyframe=i)}}if(this.resolveNoneKeyframes(),!ze.has(n)||2!==t.length)return;const[i,s]=t,a=He(i),r=He(s);if(a!==r)if(ue(a)&&ue(r))for(let e=0;e<t.length;e++){const n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,i=[];for(let n=0;n<t.length;n++)(null===t[n]||("number"==typeof(s=t[n])?0===s:null===s||"none"===s||"0"===s||e.isZeroValueString(s)))&&i.push(n);var s;i.length&&function(t,e,n){let i,s=0;for(;s<t.length&&!i;){const e=t[s];"string"==typeof e&&!un.has(e)&&I(e).values.length&&(i=t[s]),s++}if(i&&n)for(const s of e)t[s]=ln(n,i)}(t,i,n)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=de[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const i=e[e.length-1];void 0!==i&&t.getValue(n,i).jump(i,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const i=t.getValue(e);i&&i.jump(this.measuredOrigin,!1);const s=n.length-1,a=n[s];n[s]=de[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==a&&void 0===this.finalKeyframe&&(this.finalKeyframe=a),this.removedTransforms?.length&&this.removedTransforms.forEach((([e,n])=>{t.getValue(e).set(n)})),this.resolveNoneKeyframes()}},t.GroupAnimation=je,t.GroupAnimationWithThen=class extends je{then(t,e){return this.finished.finally(t).then((()=>{}))}},t.JSAnimation=Zt,t.KeyframeResolver=be,t.MotionValue=On,t.NativeAnimation=Ce,t.NativeAnimationExtended=Le,t.NativeAnimationWrapper=Ie,t.ViewTransitionBuilder=ti,t.acceleratedValues=dn,t.activeAnimations=d,t.alpha=T,t.analyseComplexValue=I,t.animateValue=function(t){return new Zt(t)},t.animateView=function(t,e={}){return new ti(t,e)},t.animationMapKey=(t,e="")=>`${t}:${e}`,t.applyPxDefaults=function(t,e){for(let n=0;n<t.length;n++)"number"==typeof t[n]&&cn.has(e)&&(t[n]=t[n]+"px")},t.calcGeneratorDuration=dt,t.cancelFrame=r,t.cancelMicrotask=fn,t.cancelSync=ni,t.collectMotionValues=Kn,t.color=N,t.complex=q,t.convertOffsetToTimes=$t,t.createGeneratorEasing=mt,t.createRenderBatcher=s,t.cubicBezierAsString=Ee,t.defaultEasing=jt,t.defaultOffset=Bt,t.defaultValueTypes=rn,t.degrees=R,t.dimensionValueTypes=_e,t.fillOffset=Wt,t.fillWildcards=_t,t.findDimensionValueType=He,t.findValueType=t=>Wn.find(Ze(t)),t.flushKeyframeResolvers=Te,t.frame=a,t.frameData=o,t.frameSteps=l,t.generateLinearEasing=ct,t.getAnimatableNone=ln,t.getAnimationMap=function(t){const e=Ye.get(t)||new Map;return Ye.set(t,e),e},t.getComputedStyle=function(t,e){const n=window.getComputedStyle(t);return we(e)?n.getPropertyValue(e):n[e]},t.getDefaultValueType=on,t.getMixer=st,t.getValueAsType=(t,e)=>e&&"number"==typeof t?e.transform(t):t,t.getValueTransition=Ge,t.getVariableValue=qe,t.hex=P,t.hover=function(t,e,n={}){const[i,s,a]=vn(t,n),r=t=>{if(!Tn(t))return;const{target:n}=t,i=e(n,t);if("function"!=typeof i||!n)return;const a=t=>{Tn(t)&&(i(t),n.removeEventListener("pointerleave",a))};n.addEventListener("pointerleave",a,s)};return i.forEach((t=>{t.addEventListener("pointerenter",r,s)})),a},t.hsla=L,t.hslaToRgba=z,t.inertia=Lt,t.interpolate=Nt,t.invisibleValues=et,t.isCSSVariableName=p,t.isCSSVariableToken=y,t.isDragActive=gn,t.isDragging=yn,t.isGenerator=Fe,t.isNodeOrChild=bn,t.isPrimaryPointer=wn,t.isWaapiSupportedEasing=function t(n){return Boolean("function"==typeof n&&ke()||!n||"string"==typeof n&&(n in Pe||ke())||e.isBezierDefinition(n)||Array.isArray(n)&&n.every(t))},t.keyframes=It,t.mapEasingToNativeEasing=Ve,t.mapValue=function(t,e,n,i){const s=Cn(e,n,i);return Nn((()=>s(t.get())))},t.maxGeneratorDuration=ht,t.microtask=pn,t.mix=lt,t.mixArray=at,t.mixColor=tt,t.mixComplex=ot,t.mixImmediate=Z,t.mixLinearColor=H,t.mixNumber=_,t.mixObject=rt,t.mixVisibility=nt,t.motionValue=Ln,t.number=v,t.numberValueTypes=an,t.observeTimeline=function(t,e){let n;const i=()=>{const{currentTime:i}=e,s=(null===i?0:i.value)/100;n!==s&&t(s),n=s};return a.preUpdate(i,!0),()=>r(i)},t.parseCSSVariable=Ue,t.parseValueFromTransform=ae,t.percent=F,t.positionalKeys=ze,t.press=function(t,e,n={}){const[i,s,a]=vn(t,n),r=t=>{const i=t.currentTarget;if(!kn(t)||xn.has(i))return;xn.add(i);const a=e(i,t),r=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),kn(t)&&xn.has(i)&&(xn.delete(i),"function"==typeof a&&a(t,{success:e}))},o=t=>{r(t,i===window||i===document||n.useGlobalTarget||bn(i,t.target))},l=t=>{r(t,!1)};window.addEventListener("pointerup",o,s),window.addEventListener("pointercancel",l,s)};return i.forEach((t=>{var e;(n.useGlobalTarget?window:t).addEventListener("pointerdown",r,s),t instanceof HTMLElement&&(t.addEventListener("focus",(t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const i=An((()=>{if(xn.has(n))return;Sn(n,"down");const t=An((()=>{Sn(n,"up")}));n.addEventListener("keyup",t,e),n.addEventListener("blur",(()=>Sn(n,"cancel")),e)}));n.addEventListener("keydown",i,e),n.addEventListener("blur",(()=>n.removeEventListener("keydown",i)),e)})(t,s))),e=t,Mn.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))})),a},t.progressPercentage=O,t.px=D,t.readTransformValue=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return ae(n,e)},t.recordStats=function(){if(i.value)throw Fn(),new Error("Stats are already being measured");const t=i;return t.value={frameloop:{setup:[],rate:[],read:[],resolveKeyframes:[],preUpdate:[],update:[],preRender:[],render:[],postRender:[]},animations:{mainThread:[],waapi:[],layout:[]},layoutProjection:{nodes:[],calculatedTargetDeltas:[],calculatedProjections:[]}},t.addProjectionMetrics=e=>{const{layoutProjection:n}=t.value;n.nodes.push(e.nodes),n.calculatedTargetDeltas.push(e.calculatedTargetDeltas),n.calculatedProjections.push(e.calculatedProjections)},a.postRender(En,!0),Dn},t.resolveElements=mn,t.rgbUnit=k,t.rgba=E,t.scale=b,t.setDragLock=function(t){return"x"===t||"y"===t?yn[t]?null:(yn[t]=!0,()=>{yn[t]=!1}):yn.x||yn.y?null:(yn.x=yn.y=!0,()=>{yn.x=yn.y=!1})},t.setStyle=Me,t.spring=Ot,t.startWaapiAnimation=Re,t.statsBuffer=i,t.styleEffect=function(t,e){const n=mn(t),i=[];for(let t=0;t<n.length;t++){const s=n[t];for(const t in e){const n=e[t],o=()=>{s.style[t]=n.get()},l=()=>a.render(o),u=n.on("change",l);l(),i.push((()=>{u(),r(o)}))}}return()=>{for(const t of i)t()}},t.supportedWaapiEasing=Pe,t.supportsBrowserAnimation=$e,t.supportsFlags=Ae,t.supportsLinearEasing=ke,t.supportsPartialKeyframes=hn,t.supportsScrollTimeline=xe,t.sync=ei,t.testValueType=Ze,t.time=h,t.transform=Cn,t.transformPropOrder=oe,t.transformProps=le,t.transformValue=Nn,t.transformValueTypes=sn,t.vh=C,t.vw=K}));
