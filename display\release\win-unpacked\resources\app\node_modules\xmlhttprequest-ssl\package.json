{"name": "xmlhttprequest-ssl", "description": "XMLHttpRequest for Node", "version": "2.1.2", "author": {"name": "<PERSON>"}, "licenses": [{"type": "MIT", "url": "http://creativecommons.org/licenses/MIT/"}], "repository": {"type": "git", "url": "git://github.com/mjwwit/node-XMLHttpRequest.git"}, "engines": {"node": ">=0.4.0"}, "directories": {"lib": "./lib", "example": "./example"}, "files": ["lib/XMLHttpRequest.js", "LICENSE", "README.md"], "main": "./lib/XMLHttpRequest.js", "dependencies": {}}