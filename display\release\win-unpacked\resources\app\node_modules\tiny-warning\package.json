{"name": "tiny-warning", "version": "1.0.3", "description": "A tiny warning function", "main": "dist/tiny-warning.cjs.js", "module": "dist/tiny-warning.esm.js", "types": "src/index.d.ts", "sideEffects": false, "files": ["/dist", "/src"], "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/alexreardon/tiny-warning.git"}, "license": "MIT", "devDependencies": {"@babel/core": "^7.5.0", "@babel/preset-env": "^7.5.0", "@babel/preset-flow": "^7.0.0", "babel-core": "7.0.0-bridge.0", "babel-jest": "^24.8.0", "flow-bin": "0.102.0", "jest": "^24.8.0", "prettier": "1.18.2", "regenerator-runtime": "^0.13.2", "rimraf": "^2.6.3", "rollup": "^1.16.6", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-uglify": "^6.0.2"}}