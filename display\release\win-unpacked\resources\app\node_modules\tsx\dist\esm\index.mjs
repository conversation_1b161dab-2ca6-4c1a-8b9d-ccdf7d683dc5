var b=Object.defineProperty;var c=(t,a)=>b(t,"name",{value:a,configurable:!0});import{isMainThread as j}from"node:worker_threads";import{i as g,a as v,e as M,m as W}from"../node-features-_8ZFwP_x.mjs";import{r as A}from"../register-RyGUjI6j.mjs";import"../get-pipe-path-BHW2eJdv.mjs";import"node:module";import f from"node:path";import{fileURLToPath as R,pathToFileURL as U}from"node:url";import"get-tsconfig";import{l as k,t as l,b as I,f as w,d as E,e as T,g as D,h as _,j as C,m as $,k as q,n as G}from"../register-C3TE0KFF.mjs";import S from"node:fs";import"esbuild";import"node:crypto";import{i as H,a as X,t as B,b as Q,r as z}from"../index-DlKgSVBb.mjs";import{p as L}from"../client-BQVF1NaW.mjs";import"../require-BnyFnlmk.mjs";import{readFile as K}from"node:fs/promises";import"module";import"../temporary-directory-CwHp0_NW.mjs";import"node:os";import"node:net";const m={active:!0},x=c(async t=>{if(!t)throw new Error(`tsx must be loaded with --import instead of --loader
The --loader flag was deprecated in Node v20.6.0 and v18.19.0`);m.namespace=t.namespace,t.tsconfig!==!1&&k(t.tsconfig??process.env.TSX_TSCONFIG_PATH),t.port&&(m.port=t.port,t.port.on("message",a=>{a==="deactivate"&&(m.active=!1,t.port.postMessage({type:"deactivated"}))}))},"initialize"),V=c(()=>(k(process.env.TSX_TSCONFIG_PATH),"process.setSourceMapsEnabled(true);"),"globalPreload"),d=new Map,Y=c(async t=>{if(d.has(t))return d.get(t);if(!await S.promises.access(t).then(()=>!0,()=>!1)){d.set(t,void 0);return}const r=await S.promises.readFile(t,"utf8");try{const e=JSON.parse(r);return d.set(t,e),e}catch{throw new Error(`Error parsing: ${t}`)}},"readPackageJson"),Z=c(async t=>{let a=new URL("package.json",t);for(;!a.pathname.endsWith("/node_modules/package.json");){const r=R(a),e=await Y(r);if(e)return e;const s=a;if(a=new URL("../package.json",a),a.pathname===s.pathname)break}},"findPackageJson"),tt=c(async t=>(await Z(t))?.type??"commonjs","getPackageType"),at=c(t=>{[t]=t.split("?");const a=f.extname(t);if(a===".mts")return"module";if(a===".cts")return"commonjs"},"getFormatFromExtension"),et=c(t=>{const a=at(t);if(a)return a;if(l.test(t))return tt(t)},"getFormatFromFileUrl"),u="tsx-namespace=",P=c(t=>{const a=t.indexOf(u);if(a===-1)return;const r=t[a-1];if(r!=="?"&&r!=="&")return;const e=a+u.length,s=t.indexOf("&",e);return s===-1?t.slice(e):t.slice(e,s)},"getNamespace"),y=g(v)?"importAttributes":"importAssertions",st=c(async(t,a,r)=>{if(!m.active)return r(t,a);const e=P(t);if(m.namespace&&m.namespace!==e)return r(t,a);if(m.port){const n=new URL(t);n.searchParams.delete("tsx-namespace"),m.port.postMessage({type:"load",url:n.toString()})}L.send&&L.send({type:"dependency",path:t}),I.test(t)&&(a[y]||(a[y]={}),a[y].type="json");const s=await r(t,a),o=t.startsWith(w)?R(t):t;if(s.format==="commonjs"&&g(M)&&s.responseURL?.startsWith("file:")&&!o.endsWith(".cjs")){const n=await K(new URL(t),"utf8");if(!o.endsWith(".js")||H(n)){const p=X(n,o,{tsconfigRaw:E?.(o)}),O=e?`${o}?namespace=${encodeURIComponent(e)}`:o;return s.responseURL=`data:text/javascript,${encodeURIComponent(p.code)}?filePath=${encodeURIComponent(O)}`,s}}if(!s.source)return s;const i=s.source.toString();if(s.format==="json"||l.test(t)){const n=await B(i,o,{tsconfigRaw:f.isAbsolute(o)?E?.(o):void 0});return{format:"module",source:T(n)}}if(s.format==="module"){const n=Q(o,i);n&&(s.source=T(n))}return s},"load"),F=c(t=>{if(t.url)return t.url;const a=t.message.match(/^Cannot find module '([^']+)'/);if(a){const[,e]=a;return e}const r=t.message.match(/^Cannot find package '([^']+)'/);if(r){const[,e]=r;if(!f.isAbsolute(e))return;const s=U(e);if(s.pathname.endsWith("/")&&(s.pathname+="package.json"),s.pathname.endsWith("/package.json")){const o=z(s);if(o?.main)return new URL(o.main,s).toString()}else return s.toString()}},"getMissingPathFromNotFound"),h=c(async(t,a,r,e)=>{const s=$(t);if(!s)return;let o;for(const i of s)try{return await r(i,a)}catch(n){const{code:p}=n;if(p!=="ERR_MODULE_NOT_FOUND"&&p!=="ERR_PACKAGE_PATH_NOT_EXPORTED")throw n;o=n}if(e)throw o},"resolveExtensions"),rt=c(async(t,a,r)=>{if((t.startsWith(w)||q(t))&&(l.test(a.parentURL)||G)){const e=await h(t,a,r);if(e)return e}try{return await r(t,a)}catch(e){if(e instanceof Error){const s=e;if(s.code==="ERR_MODULE_NOT_FOUND"){const o=F(s);if(o){const i=await h(o,a,r);if(i)return i}}}throw e}},"resolveBase"),J=c(async(t,a,r)=>{if((t==="."||t===".."||t.endsWith("/.."))&&(t+="/"),C.test(t)){const e=new URL(t,a.parentURL);return e.pathname=f.join(e.pathname,"index"),await h(e.toString(),a,r,!0)}try{return await rt(t,a,r)}catch(e){if(e instanceof Error){const s=e;if(s.code==="ERR_UNSUPPORTED_DIR_IMPORT"){const o=F(s);if(o)try{return await h(`${o}/index`,a,r,!0)}catch(i){const n=i,{message:p}=n;throw n.message=n.message.replace(`${"/index".replace("/",f.sep)}'`,"'"),n.stack=n.stack.replace(p,n.message),n}}}throw e}},"resolveDirectory"),nt=c(async(t,a,r)=>{if(!D(t)&&_&&!a.parentURL?.includes("/node_modules/")){const e=_(t);for(const s of e)try{return await J(U(s).toString(),a,r)}catch{}}return J(t,a,r)},"resolveTsPaths"),N="tsx://",ot=c(async(t,a,r)=>{if(!m.active||t.startsWith("node:"))return r(t,a);let e=P(t)??(a.parentURL&&P(a.parentURL));if(m.namespace){let n;if(t.startsWith(N)){try{n=JSON.parse(t.slice(N.length))}catch{}n?.namespace&&(e=n.namespace)}if(m.namespace!==e)return r(t,a);n&&(t=n.specifier,a.parentURL=n.parentURL)}const[s,o]=t.split("?"),i=await nt(s,a,r);return i.format==="builtin"||(!i.format&&i.url.startsWith(w)&&(i.format=await et(i.url)),o&&(i.url+=`?${o}`),e&&!i.url.includes(u)&&(i.url+=(i.url.includes("?")?"&":"?")+u+e)),i},"resolve");g(W)&&j&&A();export{V as globalPreload,x as initialize,st as load,ot as resolve};
