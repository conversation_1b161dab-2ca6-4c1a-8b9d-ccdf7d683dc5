!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ReactHookForm={},e.React)}(this,(function(e,t){"use strict";function r(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var s=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,s.get?s:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var s=r(t),a=e=>"checkbox"===e.type,i=e=>e instanceof Date,n=e=>null==e;const o=e=>"object"==typeof e;var l=e=>!n(e)&&!Array.isArray(e)&&o(e)&&!i(e),u=e=>l(e)&&e.target?a(e.target)?e.target.checked:e.target.value:e,d=(e,t)=>e.has((e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e)(t)),c="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function f(e){let t;const r=Array.isArray(e),s="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else{if(c&&(e instanceof Blob||s)||!r&&!l(e))return e;if(t=r?[]:{},r||(e=>{const t=e.constructor&&e.constructor.prototype;return l(t)&&t.hasOwnProperty("isPrototypeOf")})(e))for(const r in e)e.hasOwnProperty(r)&&(t[r]=f(e[r]));else t=e}return t}var m=e=>Array.isArray(e)?e.filter(Boolean):[],y=e=>void 0===e,g=(e,t,r)=>{if(!t||!l(e))return r;const s=m(t.split(/[,[\].]+?/)).reduce(((e,t)=>n(e)?e:e[t]),e);return y(s)||s===e?y(e[t])?r:e[t]:s},b=e=>"boolean"==typeof e,_=e=>/^\w*$/.test(e),h=e=>m(e.replace(/["|']|\]/g,"").split(/\.|\[/)),p=(e,t,r)=>{let s=-1;const a=_(t)?[t]:h(t),i=a.length,n=i-1;for(;++s<i;){const t=a[s];let i=r;if(s!==n){const r=e[t];i=l(r)||Array.isArray(r)?r:isNaN(+a[s+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=i,e=e[t]}};const v="blur",V="focusout",F="change",A="onBlur",x="onChange",S="onSubmit",w="onTouched",k="all",D="max",C="min",E="maxLength",O="minLength",j="pattern",M="required",T="validate",N=t.createContext(null),R=()=>t.useContext(N);var B=(e,t,r,s=!0)=>{const a={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(a,i,{get:()=>{const a=i;return t._proxyFormState[a]!==k&&(t._proxyFormState[a]=!s||k),r&&(r[a]=!0),e[a]}});return a},L=e=>n(e)||!o(e);function U(e,t){if(L(e)||L(t))return e===t;if(i(e)&&i(t))return e.getTime()===t.getTime();const r=Object.keys(e),s=Object.keys(t);if(r.length!==s.length)return!1;for(const a of r){const r=e[a];if(!s.includes(a))return!1;if("ref"!==a){const e=t[a];if(i(r)&&i(e)||l(r)&&l(e)||Array.isArray(r)&&Array.isArray(e)?!U(r,e):r!==e)return!1}}return!0}const P=(e,t)=>{const r=s.useRef(t);U(t,r.current)||(r.current=t),s.useEffect(e,r.current)};function q(e){const r=R(),{control:s=r.control,disabled:a,name:i,exact:n}=e||{},[o,l]=t.useState(s._formState),u=t.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return P((()=>s._subscribe({name:i,formState:u.current,exact:n,callback:e=>{!a&&l({...s._formState,...e})}})),[i,a,n]),t.useEffect((()=>{u.current.isValid&&s._setValid(!0)}),[s]),t.useMemo((()=>B(o,s,u.current,!1)),[o,s])}var W=e=>"string"==typeof e,$=(e,t,r,s,a)=>W(e)?(s&&t.watch.add(e),g(r,e,a)):Array.isArray(e)?e.map((e=>(s&&t.watch.add(e),g(r,e)))):(s&&(t.watchAll=!0),r);function I(e){const r=R(),{control:s=r.control,name:a,defaultValue:i,disabled:n,exact:o}=e||{},[l,u]=t.useState(s._getWatch(a,i));return P((()=>s._subscribe({name:a,formState:{values:!0},exact:o,callback:e=>!n&&u($(a,s._names,e.values||s._formValues,!1,i))})),[a,i,n,o]),t.useEffect((()=>s._removeUnmounted())),l}function H(e){const r=R(),{name:s,disabled:a,control:i=r.control,shouldUnregister:n}=e,o=d(i._names.array,s),l=I({control:i,name:s,defaultValue:g(i._formValues,s,g(i._defaultValues,s,e.defaultValue)),exact:!0}),c=q({control:i,name:s,exact:!0}),m=t.useRef(e),_=t.useRef(i.register(s,{...e.rules,value:l,...b(e.disabled)?{disabled:e.disabled}:{}})),h=t.useMemo((()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!g(c.errors,s)},isDirty:{enumerable:!0,get:()=>!!g(c.dirtyFields,s)},isTouched:{enumerable:!0,get:()=>!!g(c.touchedFields,s)},isValidating:{enumerable:!0,get:()=>!!g(c.validatingFields,s)},error:{enumerable:!0,get:()=>g(c.errors,s)}})),[c,s]),V=t.useCallback((e=>_.current.onChange({target:{value:u(e),name:s},type:F})),[s]),A=t.useCallback((()=>_.current.onBlur({target:{value:g(i._formValues,s),name:s},type:v})),[s,i._formValues]),x=t.useCallback((e=>{const t=g(i._fields,s);t&&e&&(t._f.ref={focus:()=>e.focus(),select:()=>e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()})}),[i._fields,s]),S=t.useMemo((()=>({name:s,value:l,...b(a)||c.disabled?{disabled:c.disabled||a}:{},onChange:V,onBlur:A,ref:x})),[s,a,c.disabled,V,A,x,l]);return t.useEffect((()=>{const e=i._options.shouldUnregister||n;i.register(s,{...m.current.rules,...b(m.current.disabled)?{disabled:m.current.disabled}:{}});const t=(e,t)=>{const r=g(i._fields,e);r&&r._f&&(r._f.mount=t)};if(t(s,!0),e){const e=f(g(i._options.defaultValues,s));p(i._defaultValues,s,e),y(g(i._formValues,s))&&p(i._formValues,s,e)}return!o&&i.register(s),()=>{(o?e&&!i._state.action:e)?i.unregister(s):t(s,!1)}}),[s,i,o,n]),t.useEffect((()=>{i._setDisabledField({disabled:a,name:s})}),[a,s,i]),t.useMemo((()=>({field:S,formState:c,fieldState:h})),[S,c,h])}const z=e=>{const t={};for(const r of Object.keys(e))if(o(e[r])&&null!==e[r]){const s=z(e[r]);for(const e of Object.keys(s))t[`${r}.${e}`]=s[e]}else t[r]=e[r];return t},J="post";var G=(e,t,r,s,a)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[s]:a||!0}}:{},K=e=>Array.isArray(e)?e:[e],Q=()=>{let e=[];return{get observers(){return e},next:t=>{for(const r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter((e=>e!==t))}}),unsubscribe:()=>{e=[]}}},X=e=>l(e)&&!Object.keys(e).length,Y=e=>"file"===e.type,Z=e=>"function"==typeof e,ee=e=>{if(!c)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},te=e=>"select-multiple"===e.type,re=e=>"radio"===e.type,se=e=>ee(e)&&e.isConnected;function ae(e,t){const r=Array.isArray(t)?t:_(t)?[t]:h(t),s=1===r.length?e:function(e,t){const r=t.slice(0,-1).length;let s=0;for(;s<r;)e=y(e)?s++:e[t[s++]];return e}(e,r),a=r.length-1,i=r[a];return s&&delete s[i],0!==a&&(l(s)&&X(s)||Array.isArray(s)&&function(e){for(const t in e)if(e.hasOwnProperty(t)&&!y(e[t]))return!1;return!0}(s))&&ae(e,r.slice(0,-1)),e}var ie=e=>{for(const t in e)if(Z(e[t]))return!0;return!1};function ne(e,t={}){const r=Array.isArray(e);if(l(e)||r)for(const r in e)Array.isArray(e[r])||l(e[r])&&!ie(e[r])?(t[r]=Array.isArray(e[r])?[]:{},ne(e[r],t[r])):n(e[r])||(t[r]=!0);return t}function oe(e,t,r){const s=Array.isArray(e);if(l(e)||s)for(const s in e)Array.isArray(e[s])||l(e[s])&&!ie(e[s])?y(t)||L(r[s])?r[s]=Array.isArray(e[s])?ne(e[s],[]):{...ne(e[s])}:oe(e[s],n(t)?{}:t[s],r[s]):r[s]=!U(e[s],t[s]);return r}var le=(e,t)=>oe(e,t,ne(t));const ue={value:!1,isValid:!1},de={value:!0,isValid:!0};var ce=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter((e=>e&&e.checked&&!e.disabled)).map((e=>e.value));return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!y(e[0].attributes.value)?y(e[0].value)||""===e[0].value?de:{value:e[0].value,isValid:!0}:de:ue}return ue},fe=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:s})=>y(e)?e:t?""===e?NaN:e?+e:e:r&&W(e)?new Date(e):s?s(e):e;const me={isValid:!1,value:null};var ye=e=>Array.isArray(e)?e.reduce(((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e),me):me;function ge(e){const t=e.ref;return Y(t)?t.files:re(t)?ye(e.refs).value:te(t)?[...t.selectedOptions].map((({value:e})=>e)):a(t)?ce(e.refs).value:fe(y(t.value)?e.ref.value:t.value,e)}var be=e=>e instanceof RegExp,_e=e=>y(e)?e:be(e)?e.source:l(e)?be(e.value)?e.value.source:e.value:e,he=e=>({isOnSubmit:!e||e===S,isOnBlur:e===A,isOnChange:e===x,isOnAll:e===k,isOnTouch:e===w});const pe="AsyncFunction";var ve=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some((t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length)))));const Ve=(e,t,r,s)=>{for(const a of r||Object.keys(e)){const r=g(e,a);if(r){const{_f:e,...i}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!s)return!0;if(e.ref&&t(e.ref,e.name)&&!s)return!0;if(Ve(i,t))break}else if(l(i)&&Ve(i,t))break}}};function Fe(e,t,r){const s=g(e,r);if(s||_(r))return{error:s,name:r};const a=r.split(".");for(;a.length;){const s=a.join("."),i=g(t,s),n=g(e,s);if(i&&!Array.isArray(i)&&r!==s)return{name:r};if(n&&n.type)return{name:s,error:n};a.pop()}return{name:r}}var Ae=(e,t,r)=>{const s=K(g(e,r));return p(s,"root",t[r]),p(e,r,s),e},xe=e=>W(e);function Se(e,t,r="validate"){if(xe(e)||Array.isArray(e)&&e.every(xe)||b(e)&&!e)return{type:r,message:xe(e)?e:"",ref:t}}var we=e=>l(e)&&!be(e)?e:{value:e,message:""},ke=async(e,t,r,s,i,o)=>{const{ref:u,refs:d,required:c,maxLength:f,minLength:m,min:_,max:h,pattern:p,validate:v,name:V,valueAsNumber:F,mount:A}=e._f,x=g(r,V);if(!A||t.has(V))return{};const S=d?d[0]:u,w=e=>{i&&S.reportValidity&&(S.setCustomValidity(b(e)?"":e||""),S.reportValidity())},k={},N=re(u),R=a(u),B=N||R,L=(F||Y(u))&&y(u.value)&&y(x)||ee(u)&&""===u.value||""===x||Array.isArray(x)&&!x.length,U=G.bind(null,V,s,k),P=(e,t,r,s=E,a=O)=>{const i=e?t:r;k[V]={type:e?s:a,message:i,ref:u,...U(e?s:a,i)}};if(o?!Array.isArray(x)||!x.length:c&&(!B&&(L||n(x))||b(x)&&!x||R&&!ce(d).isValid||N&&!ye(d).isValid)){const{value:e,message:t}=xe(c)?{value:!!c,message:c}:we(c);if(e&&(k[V]={type:M,message:t,ref:S,...U(M,t)},!s))return w(t),k}if(!(L||n(_)&&n(h))){let e,t;const r=we(h),a=we(_);if(n(x)||isNaN(x)){const s=u.valueAsDate||new Date(x),i=e=>new Date((new Date).toDateString()+" "+e),n="time"==u.type,o="week"==u.type;W(r.value)&&x&&(e=n?i(x)>i(r.value):o?x>r.value:s>new Date(r.value)),W(a.value)&&x&&(t=n?i(x)<i(a.value):o?x<a.value:s<new Date(a.value))}else{const s=u.valueAsNumber||(x?+x:x);n(r.value)||(e=s>r.value),n(a.value)||(t=s<a.value)}if((e||t)&&(P(!!e,r.message,a.message,D,C),!s))return w(k[V].message),k}if((f||m)&&!L&&(W(x)||o&&Array.isArray(x))){const e=we(f),t=we(m),r=!n(e.value)&&x.length>+e.value,a=!n(t.value)&&x.length<+t.value;if((r||a)&&(P(r,e.message,t.message),!s))return w(k[V].message),k}if(p&&!L&&W(x)){const{value:e,message:t}=we(p);if(be(e)&&!x.match(e)&&(k[V]={type:j,message:t,ref:u,...U(j,t)},!s))return w(t),k}if(v)if(Z(v)){const e=Se(await v(x,r),S);if(e&&(k[V]={...e,...U(T,e.message)},!s))return w(e.message),k}else if(l(v)){let e={};for(const t in v){if(!X(e)&&!s)break;const a=Se(await v[t](x,r),S,t);a&&(e={...a,...U(t,a.message)},w(a.message),s&&(k[V]=e))}if(!X(e)&&(k[V]={ref:S,...e},!s))return k}return w(!0),k};const De={mode:S,reValidateMode:x,shouldFocusError:!0};function Ce(e={}){let t={...De,...e},r={submitCount:0,isDirty:!1,isReady:!1,isLoading:Z(t.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1};const s={};let o,_=(l(t.defaultValues)||l(t.values))&&f(t.values||t.defaultValues)||{},h=t.shouldUnregister?{}:f(_),F={action:!1,mount:!1,watch:!1},A={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},x=0;const S={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let w={...S};const D={array:Q(),state:Q()},C=he(t.mode),E=he(t.reValidateMode),O=t.criteriaMode===k,j=async e=>{if(!t.disabled&&(S.isValid||w.isValid||e)){const e=t.resolver?X((await B()).errors):await L(s,!0);e!==r.isValid&&D.state.next({isValid:e})}},M=(e,s)=>{!t.disabled&&(S.isValidating||S.validatingFields||w.isValidating||w.validatingFields)&&((e||Array.from(A.mount)).forEach((e=>{e&&(s?p(r.validatingFields,e,s):ae(r.validatingFields,e))})),D.state.next({validatingFields:r.validatingFields,isValidating:!X(r.validatingFields)}))},T=(e,t,r,a)=>{const i=g(s,e);if(i){const s=g(h,e,y(r)?g(_,e):r);y(s)||a&&a.defaultChecked||t?p(h,e,t?s:ge(i._f)):I(e,s),F.mount&&j()}},N=(e,s,a,i,n)=>{let o=!1,l=!1;const u={name:e};if(!t.disabled){if(!a||i){(S.isDirty||w.isDirty)&&(l=r.isDirty,r.isDirty=u.isDirty=P(),o=l!==u.isDirty);const t=U(g(_,e),s);l=!!g(r.dirtyFields,e),t?ae(r.dirtyFields,e):p(r.dirtyFields,e,!0),u.dirtyFields=r.dirtyFields,o=o||(S.dirtyFields||w.dirtyFields)&&l!==!t}if(a){const t=g(r.touchedFields,e);t||(p(r.touchedFields,e,a),u.touchedFields=r.touchedFields,o=o||(S.touchedFields||w.touchedFields)&&t!==a)}o&&n&&D.state.next(u)}return o?u:{}},R=(e,s,a,i)=>{const n=g(r.errors,e),l=(S.isValid||w.isValid)&&b(s)&&r.isValid!==s;var u;if(t.delayError&&a?(u=()=>((e,t)=>{p(r.errors,e,t),D.state.next({errors:r.errors})})(e,a),o=e=>{clearTimeout(x),x=setTimeout(u,e)},o(t.delayError)):(clearTimeout(x),o=null,a?p(r.errors,e,a):ae(r.errors,e)),(a?!U(n,a):n)||!X(i)||l){const t={...i,...l&&b(s)?{isValid:s}:{},errors:r.errors,name:e};r={...r,...t},D.state.next(t)}},B=async e=>{M(e,!0);const r=await t.resolver(h,t.context,((e,t,r,s)=>{const a={};for(const r of e){const e=g(t,r);e&&p(a,r,e._f)}return{criteriaMode:r,names:[...e],fields:a,shouldUseNativeValidation:s}})(e||A.mount,s,t.criteriaMode,t.shouldUseNativeValidation));return M(e),r},L=async(e,s,a={valid:!0})=>{for(const n in e){const o=e[n];if(o){const{_f:e,...u}=o;if(e){const u=A.array.has(e.name),d=o._f&&(!!(i=o._f)&&!!i.validate&&!!(Z(i.validate)&&i.validate.constructor.name===pe||l(i.validate)&&Object.values(i.validate).find((e=>e.constructor.name===pe))));d&&S.validatingFields&&M([n],!0);const c=await ke(o,A.disabled,h,O,t.shouldUseNativeValidation&&!s,u);if(d&&S.validatingFields&&M([n]),c[e.name]&&(a.valid=!1,s))break;!s&&(g(c,e.name)?u?Ae(r.errors,c,e.name):p(r.errors,e.name,c[e.name]):ae(r.errors,e.name))}!X(u)&&await L(u,s,a)}}var i;return a.valid},P=(e,r)=>!t.disabled&&(e&&r&&p(h,e,r),!U(ne(),_)),q=(e,t,r)=>$(e,A,{...F.mount?h:y(t)?_:W(e)?{[e]:t}:t},r,t),I=(e,t,r={})=>{const i=g(s,e);let o=t;if(i){const r=i._f;r&&(!r.disabled&&p(h,e,fe(t,r)),o=ee(r.ref)&&n(t)?"":t,te(r.ref)?[...r.ref.options].forEach((e=>e.selected=o.includes(e.value))):r.refs?a(r.ref)?r.refs.length>1?r.refs.forEach((e=>(!e.defaultChecked||!e.disabled)&&(e.checked=Array.isArray(o)?!!o.find((t=>t===e.value)):o===e.value))):r.refs[0]&&(r.refs[0].checked=!!o):r.refs.forEach((e=>e.checked=e.value===o)):Y(r.ref)?r.ref.value="":(r.ref.value=o,r.ref.type||D.state.next({name:e,values:f(h)})))}(r.shouldDirty||r.shouldTouch)&&N(e,o,r.shouldTouch,r.shouldDirty,!0),r.shouldValidate&&ie(e)},H=(e,t,r)=>{for(const a in t){const n=t[a],o=`${e}.${a}`,u=g(s,o);(A.array.has(e)||l(n)||u&&!u._f)&&!i(n)?H(o,n,r):I(o,n,r)}},z=(e,t,a={})=>{const i=g(s,e),o=A.array.has(e),l=f(t);p(h,e,l),o?(D.array.next({name:e,values:f(h)}),(S.isDirty||S.dirtyFields||w.isDirty||w.dirtyFields)&&a.shouldDirty&&D.state.next({name:e,dirtyFields:le(_,h),isDirty:P(e,l)})):!i||i._f||n(l)?I(e,l,a):H(e,l,a),ve(e,A)&&D.state.next({...r}),D.state.next({name:F.mount?e:void 0,values:f(h)})},J=async e=>{F.mount=!0;const a=e.target;let n=a.name,l=!0;const d=g(s,n),c=e=>{l=Number.isNaN(e)||i(e)&&isNaN(e.getTime())||U(e,g(h,n,e))};if(d){let i,y;const b=a.type?ge(d._f):u(e),_=e.type===v||e.type===V,F=!((m=d._f).mount&&(m.required||m.min||m.max||m.maxLength||m.minLength||m.pattern||m.validate)||t.resolver||g(r.errors,n)||d._f.deps)||((e,t,r,s,a)=>!a.isOnAll&&(!r&&a.isOnTouch?!(t||e):(r?s.isOnBlur:a.isOnBlur)?!e:!(r?s.isOnChange:a.isOnChange)||e))(_,g(r.touchedFields,n),r.isSubmitted,E,C),x=ve(n,A,_);p(h,n,b),_?(d._f.onBlur&&d._f.onBlur(e),o&&o(0)):d._f.onChange&&d._f.onChange(e);const k=N(n,b,_),T=!X(k)||x;if(!_&&D.state.next({name:n,type:e.type,values:f(h)}),F)return(S.isValid||w.isValid)&&("onBlur"===t.mode?_&&j():_||j()),T&&D.state.next({name:n,...x?{}:k});if(!_&&x&&D.state.next({...r}),t.resolver){const{errors:e}=await B([n]);if(c(b),l){const t=Fe(r.errors,s,n),a=Fe(e,s,t.name||n);i=a.error,n=a.name,y=X(e)}}else M([n],!0),i=(await ke(d,A.disabled,h,O,t.shouldUseNativeValidation))[n],M([n]),c(b),l&&(i?y=!1:(S.isValid||w.isValid)&&(y=await L(s,!0)));l&&(d._f.deps&&ie(d._f.deps),R(n,y,i,k))}var m},G=(e,t)=>{if(g(r.errors,t)&&e.focus)return e.focus(),1},ie=async(e,a={})=>{let i,n;const o=K(e);if(t.resolver){const t=await(async e=>{const{errors:t}=await B(e);if(e)for(const s of e){const e=g(t,s);e?p(r.errors,s,e):ae(r.errors,s)}else r.errors=t;return t})(y(e)?e:o);i=X(t),n=e?!o.some((e=>g(t,e))):i}else e?(n=(await Promise.all(o.map((async e=>{const t=g(s,e);return await L(t&&t._f?{[e]:t}:t)})))).every(Boolean),(n||r.isValid)&&j()):n=i=await L(s);return D.state.next({...!W(e)||(S.isValid||w.isValid)&&i!==r.isValid?{}:{name:e},...t.resolver||!e?{isValid:i}:{},errors:r.errors}),a.shouldFocus&&!n&&Ve(s,G,e?o:A.mount),n},ne=e=>{const t={...F.mount?h:_};return y(e)?t:W(e)?g(t,e):e.map((e=>g(t,e)))},oe=(e,t)=>({invalid:!!g((t||r).errors,e),isDirty:!!g((t||r).dirtyFields,e),error:g((t||r).errors,e),isValidating:!!g(r.validatingFields,e),isTouched:!!g((t||r).touchedFields,e)}),ue=(e,t,a)=>{const i=(g(s,e,{_f:{}})._f||{}).ref,n=g(r.errors,e)||{},{ref:o,message:l,type:u,...d}=n;p(r.errors,e,{...d,...t,ref:i}),D.state.next({name:e,errors:r.errors,isValid:!1}),a&&a.shouldFocus&&i&&i.focus&&i.focus()},de=e=>D.state.subscribe({next:t=>{var s,a,i;s=e.name,a=t.name,i=e.exact,s&&a&&s!==a&&!K(s).some((e=>e&&(i?e===a:e.startsWith(a)||a.startsWith(e))))||!((e,t,r,s)=>{r(e);const{name:a,...i}=e;return X(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find((e=>t[e]===(!s||k)))})(t,e.formState||S,Ce,e.reRenderRoot)||e.callback({values:{...h},...r,...t})}}).unsubscribe,ce=(e,a={})=>{for(const i of e?K(e):A.mount)A.mount.delete(i),A.array.delete(i),a.keepValue||(ae(s,i),ae(h,i)),!a.keepError&&ae(r.errors,i),!a.keepDirty&&ae(r.dirtyFields,i),!a.keepTouched&&ae(r.touchedFields,i),!a.keepIsValidating&&ae(r.validatingFields,i),!t.shouldUnregister&&!a.keepDefaultValue&&ae(_,i);D.state.next({values:f(h)}),D.state.next({...r,...a.keepDirty?{isDirty:P()}:{}}),!a.keepIsValid&&j()},me=({disabled:e,name:t})=>{(b(e)&&F.mount||e||A.disabled.has(t))&&(e?A.disabled.add(t):A.disabled.delete(t))},ye=(e,r={})=>{let i=g(s,e);const n=b(r.disabled)||b(t.disabled);return p(s,e,{...i||{},_f:{...i&&i._f?i._f:{ref:{name:e}},name:e,mount:!0,...r}}),A.mount.add(e),i?me({disabled:b(r.disabled)?r.disabled:t.disabled,name:e}):T(e,!0,r.value),{...n?{disabled:r.disabled||t.disabled}:{},...t.progressive?{required:!!r.required,min:_e(r.min),max:_e(r.max),minLength:_e(r.minLength),maxLength:_e(r.maxLength),pattern:_e(r.pattern)}:{},name:e,onChange:J,onBlur:J,ref:n=>{if(n){ye(e,r),i=g(s,e);const t=y(n.value)&&n.querySelectorAll&&n.querySelectorAll("input,select,textarea")[0]||n,o=(e=>re(e)||a(e))(t),l=i._f.refs||[];if(o?l.find((e=>e===t)):t===i._f.ref)return;p(s,e,{_f:{...i._f,...o?{refs:[...l.filter(se),t,...Array.isArray(g(_,e))?[{}]:[]],ref:{type:t.type,name:e}}:{ref:t}}}),T(e,!1,void 0,t)}else i=g(s,e,{}),i._f&&(i._f.mount=!1),(t.shouldUnregister||r.shouldUnregister)&&(!d(A.array,e)||!F.action)&&A.unMount.add(e)}}},be=()=>t.shouldFocusError&&Ve(s,G,A.mount),xe=(e,a)=>async i=>{let n;i&&(i.preventDefault&&i.preventDefault(),i.persist&&i.persist());let o=f(h);if(D.state.next({isSubmitting:!0}),t.resolver){const{errors:e,values:t}=await B();r.errors=e,o=t}else await L(s);if(A.disabled.size)for(const e of A.disabled)p(o,e,void 0);if(ae(r.errors,"root"),X(r.errors)){D.state.next({errors:{}});try{await e(o,i)}catch(e){n=e}}else a&&await a({...r.errors},i),be(),setTimeout(be);if(D.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:X(r.errors)&&!n,submitCount:r.submitCount+1,errors:r.errors}),n)throw n},Se=(e,a={})=>{const i=e?f(e):_,n=f(i),o=X(e),l=o?_:n;if(a.keepDefaultValues||(_=i),!a.keepValues){if(a.keepDirtyValues){const e=new Set([...A.mount,...Object.keys(le(_,h))]);for(const t of Array.from(e))g(r.dirtyFields,t)?p(l,t,g(h,t)):z(t,g(l,t))}else{if(c&&y(e))for(const e of A.mount){const t=g(s,e);if(t&&t._f){const e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(ee(e)){const t=e.closest("form");if(t){t.reset();break}}}}for(const e of A.mount)z(e,g(l,e))}h=f(l),D.array.next({values:{...l}}),D.state.next({values:{...l}})}A={mount:a.keepDirtyValues?A.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},F.mount=!S.isValid||!!a.keepIsValid||!!a.keepDirtyValues,F.watch=!!t.shouldUnregister,D.state.next({submitCount:a.keepSubmitCount?r.submitCount:0,isDirty:!o&&(a.keepDirty?r.isDirty:!(!a.keepDefaultValues||U(e,_))),isSubmitted:!!a.keepIsSubmitted&&r.isSubmitted,dirtyFields:o?{}:a.keepDirtyValues?a.keepDefaultValues&&h?le(_,h):r.dirtyFields:a.keepDefaultValues&&e?le(_,e):a.keepDirty?r.dirtyFields:{},touchedFields:a.keepTouched?r.touchedFields:{},errors:a.keepErrors?r.errors:{},isSubmitSuccessful:!!a.keepIsSubmitSuccessful&&r.isSubmitSuccessful,isSubmitting:!1})},we=(e,t)=>Se(Z(e)?e(h):e,t),Ce=e=>{r={...r,...e}},Ee={control:{register:ye,unregister:ce,getFieldState:oe,handleSubmit:xe,setError:ue,_subscribe:de,_runSchema:B,_getWatch:q,_getDirty:P,_setValid:j,_setFieldArray:(e,a=[],i,n,o=!0,l=!0)=>{if(n&&i&&!t.disabled){if(F.action=!0,l&&Array.isArray(g(s,e))){const t=i(g(s,e),n.argA,n.argB);o&&p(s,e,t)}if(l&&Array.isArray(g(r.errors,e))){const t=i(g(r.errors,e),n.argA,n.argB);o&&p(r.errors,e,t),((e,t)=>{!m(g(e,t)).length&&ae(e,t)})(r.errors,e)}if((S.touchedFields||w.touchedFields)&&l&&Array.isArray(g(r.touchedFields,e))){const t=i(g(r.touchedFields,e),n.argA,n.argB);o&&p(r.touchedFields,e,t)}(S.dirtyFields||w.dirtyFields)&&(r.dirtyFields=le(_,h)),D.state.next({name:e,isDirty:P(e,a),dirtyFields:r.dirtyFields,errors:r.errors,isValid:r.isValid})}else p(h,e,a)},_setDisabledField:me,_setErrors:e=>{r.errors=e,D.state.next({errors:r.errors,isValid:!1})},_getFieldArray:e=>m(g(F.mount?h:_,e,t.shouldUnregister?g(_,e,[]):[])),_reset:Se,_resetDefaultValues:()=>Z(t.defaultValues)&&t.defaultValues().then((e=>{we(e,t.resetOptions),D.state.next({isLoading:!1})})),_removeUnmounted:()=>{for(const e of A.unMount){const t=g(s,e);t&&(t._f.refs?t._f.refs.every((e=>!se(e))):!se(t._f.ref))&&ce(e)}A.unMount=new Set},_disableForm:e=>{b(e)&&(D.state.next({disabled:e}),Ve(s,((t,r)=>{const a=g(s,r);a&&(t.disabled=a._f.disabled||e,Array.isArray(a._f.refs)&&a._f.refs.forEach((t=>{t.disabled=a._f.disabled||e})))}),0,!1))},_subjects:D,_proxyFormState:S,get _fields(){return s},get _formValues(){return h},get _state(){return F},set _state(e){F=e},get _defaultValues(){return _},get _names(){return A},set _names(e){A=e},get _formState(){return r},get _options(){return t},set _options(e){t={...t,...e}}},subscribe:e=>(F.mount=!0,w={...w,...e.formState},de({...e,formState:w})),trigger:ie,register:ye,handleSubmit:xe,watch:(e,t)=>Z(e)?D.state.subscribe({next:r=>e(q(void 0,t),r)}):q(e,t,!0),setValue:z,getValues:ne,reset:we,resetField:(e,t={})=>{g(s,e)&&(y(t.defaultValue)?z(e,f(g(_,e))):(z(e,t.defaultValue),p(_,e,f(t.defaultValue))),t.keepTouched||ae(r.touchedFields,e),t.keepDirty||(ae(r.dirtyFields,e),r.isDirty=t.defaultValue?P(e,f(g(_,e))):P()),t.keepError||(ae(r.errors,e),S.isValid&&j()),D.state.next({...r}))},clearErrors:e=>{e&&K(e).forEach((e=>ae(r.errors,e))),D.state.next({errors:e?r.errors:{}})},unregister:ce,setError:ue,setFocus:(e,t={})=>{const r=g(s,e),a=r&&r._f;if(a){const e=a.refs?a.refs[0]:a.ref;e.focus&&(e.focus(),t.shouldSelect&&Z(e.select)&&e.select())}},getFieldState:oe};return{...Ee,formControl:Ee}}var Ee=()=>{const e="undefined"==typeof performance?Date.now():1e3*performance.now();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(t=>{const r=(16*Math.random()+e)%16|0;return("x"==t?r:3&r|8).toString(16)}))},Oe=(e,t,r={})=>r.shouldFocus||y(r.shouldFocus)?r.focusName||`${e}.${y(r.focusIndex)?t:r.focusIndex}.`:"",je=(e,t)=>[...e,...K(t)],Me=e=>Array.isArray(e)?e.map((()=>{})):void 0;function Te(e,t,r){return[...e.slice(0,t),...K(r),...e.slice(t)]}var Ne=(e,t,r)=>Array.isArray(e)?(y(e[r])&&(e[r]=void 0),e.splice(r,0,e.splice(t,1)[0]),e):[],Re=(e,t)=>[...K(t),...K(e)];var Be=(e,t)=>y(t)?[]:function(e,t){let r=0;const s=[...e];for(const e of t)s.splice(e-r,1),r++;return m(s).length?s:[]}(e,K(t).sort(((e,t)=>e-t))),Le=(e,t,r)=>{[e[t],e[r]]=[e[r],e[t]]},Ue=(e,t,r)=>(e[t]=r,e);const Pe="undefined"!=typeof window?t.useLayoutEffect:t.useEffect;e.Controller=e=>e.render(H(e)),e.Form=function(e){const r=R(),[s,a]=t.useState(!1),{control:i=r.control,onSubmit:n,children:o,action:l,method:u=J,headers:d,encType:c,onError:f,render:m,onSuccess:y,validateStatus:g,...b}=e,_=async t=>{let r=!1,s="";await i.handleSubmit((async e=>{const a=new FormData;let o="";try{o=JSON.stringify(e)}catch(e){}const m=z(i._formValues);for(const e in m)a.append(e,m[e]);if(n&&await n({data:e,event:t,method:u,formData:a,formDataJson:o}),l)try{const e=[d&&d["Content-Type"],c].some((e=>e&&e.includes("json"))),t=await fetch(String(l),{method:u,headers:{...d,...c?{"Content-Type":c}:{}},body:e?o:a});t&&(g?!g(t.status):t.status<200||t.status>=300)?(r=!0,f&&f({response:t}),s=String(t.status)):y&&y({response:t})}catch(e){r=!0,f&&f({error:e})}}))(t),r&&e.control&&(e.control._subjects.state.next({isSubmitSuccessful:!1}),e.control.setError("root.server",{type:s}))};return t.useEffect((()=>{a(!0)}),[]),m?t.createElement(t.Fragment,null,m({submit:_})):t.createElement("form",{noValidate:s,action:l,method:u,encType:c,onSubmit:_,...b},o)},e.FormProvider=e=>{const{children:r,...s}=e;return t.createElement(N.Provider,{value:s},r)},e.appendErrors=G,e.createFormControl=Ce,e.get=g,e.set=p,e.useController=H,e.useFieldArray=function(e){const r=R(),{control:s=r.control,name:a,keyName:i="id",shouldUnregister:n,rules:o}=e,[l,u]=t.useState(s._getFieldArray(a)),d=t.useRef(s._getFieldArray(a).map(Ee)),c=t.useRef(l),m=t.useRef(a),y=t.useRef(!1);m.current=a,c.current=l,s._names.array.add(a),o&&s.register(a,o),t.useEffect((()=>s._subjects.array.subscribe({next:({values:e,name:t})=>{if(t===m.current||!t){const t=g(e,m.current);Array.isArray(t)&&(u(t),d.current=t.map(Ee))}}}).unsubscribe),[s]);const b=t.useCallback((e=>{y.current=!0,s._setFieldArray(a,e)}),[s,a]);return t.useEffect((()=>{if(s._state.action=!1,ve(a,s._names)&&s._subjects.state.next({...s._formState}),y.current&&(!he(s._options.mode).isOnSubmit||s._formState.isSubmitted)&&!he(s._options.reValidateMode).isOnSubmit)if(s._options.resolver)s._runSchema([a]).then((e=>{const t=g(e.errors,a),r=g(s._formState.errors,a);(r?!t&&r.type||t&&(r.type!==t.type||r.message!==t.message):t&&t.type)&&(t?p(s._formState.errors,a,t):ae(s._formState.errors,a),s._subjects.state.next({errors:s._formState.errors}))}));else{const e=g(s._fields,a);!e||!e._f||he(s._options.reValidateMode).isOnSubmit&&he(s._options.mode).isOnSubmit||ke(e,s._names.disabled,s._formValues,s._options.criteriaMode===k,s._options.shouldUseNativeValidation,!0).then((e=>!X(e)&&s._subjects.state.next({errors:Ae(s._formState.errors,e,a)})))}s._subjects.state.next({name:a,values:f(s._formValues)}),s._names.focus&&Ve(s._fields,((e,t)=>{if(s._names.focus&&t.startsWith(s._names.focus)&&e.focus)return e.focus(),1})),s._names.focus="",s._setValid(),y.current=!1}),[l,a,s]),t.useEffect((()=>(!g(s._formValues,a)&&s._setFieldArray(a),()=>{s._options.shouldUnregister||n?s.unregister(a):((e,t)=>{const r=g(s._fields,e);r&&r._f&&(r._f.mount=t)})(a,!1)})),[a,s,i,n]),{swap:t.useCallback(((e,t)=>{const r=s._getFieldArray(a);Le(r,e,t),Le(d.current,e,t),b(r),u(r),s._setFieldArray(a,r,Le,{argA:e,argB:t},!1)}),[b,a,s]),move:t.useCallback(((e,t)=>{const r=s._getFieldArray(a);Ne(r,e,t),Ne(d.current,e,t),b(r),u(r),s._setFieldArray(a,r,Ne,{argA:e,argB:t},!1)}),[b,a,s]),prepend:t.useCallback(((e,t)=>{const r=K(f(e)),i=Re(s._getFieldArray(a),r);s._names.focus=Oe(a,0,t),d.current=Re(d.current,r.map(Ee)),b(i),u(i),s._setFieldArray(a,i,Re,{argA:Me(e)})}),[b,a,s]),append:t.useCallback(((e,t)=>{const r=K(f(e)),i=je(s._getFieldArray(a),r);s._names.focus=Oe(a,i.length-1,t),d.current=je(d.current,r.map(Ee)),b(i),u(i),s._setFieldArray(a,i,je,{argA:Me(e)})}),[b,a,s]),remove:t.useCallback((e=>{const t=Be(s._getFieldArray(a),e);d.current=Be(d.current,e),b(t),u(t),!Array.isArray(g(s._fields,a))&&p(s._fields,a,void 0),s._setFieldArray(a,t,Be,{argA:e})}),[b,a,s]),insert:t.useCallback(((e,t,r)=>{const i=K(f(t)),n=Te(s._getFieldArray(a),e,i);s._names.focus=Oe(a,e,r),d.current=Te(d.current,e,i.map(Ee)),b(n),u(n),s._setFieldArray(a,n,Te,{argA:e,argB:Me(t)})}),[b,a,s]),update:t.useCallback(((e,t)=>{const r=f(t),i=Ue(s._getFieldArray(a),e,r);d.current=[...i].map(((t,r)=>t&&r!==e?d.current[r]:Ee())),b(i),u([...i]),s._setFieldArray(a,i,Ue,{argA:e,argB:r},!0,!1)}),[b,a,s]),replace:t.useCallback((e=>{const t=K(f(e));d.current=t.map(Ee),b([...t]),u([...t]),s._setFieldArray(a,[...t],(e=>e),{},!0,!1)}),[b,a,s]),fields:t.useMemo((()=>l.map(((e,t)=>({...e,[i]:d.current[t]||Ee()})))),[l,i])}},e.useForm=function(e={}){const r=t.useRef(void 0),s=t.useRef(void 0),[a,i]=t.useState({isDirty:!1,isValidating:!1,isLoading:Z(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:Z(e.defaultValues)?void 0:e.defaultValues});r.current||(r.current={...e.formControl?e.formControl:Ce(e),formState:a},e.formControl&&e.defaultValues&&!Z(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions));const n=r.current.control;return n._options=e,Pe((()=>{const e=n._subscribe({formState:n._proxyFormState,callback:()=>i({...n._formState}),reRenderRoot:!0});return i((e=>({...e,isReady:!0}))),n._formState.isReady=!0,e}),[n]),t.useEffect((()=>n._disableForm(e.disabled)),[n,e.disabled]),t.useEffect((()=>{e.mode&&(n._options.mode=e.mode),e.reValidateMode&&(n._options.reValidateMode=e.reValidateMode),e.errors&&!X(e.errors)&&n._setErrors(e.errors)}),[n,e.errors,e.mode,e.reValidateMode]),t.useEffect((()=>{e.shouldUnregister&&n._subjects.state.next({values:n._getWatch()})}),[n,e.shouldUnregister]),t.useEffect((()=>{if(n._proxyFormState.isDirty){const e=n._getDirty();e!==a.isDirty&&n._subjects.state.next({isDirty:e})}}),[n,a.isDirty]),t.useEffect((()=>{e.values&&!U(e.values,s.current)?(n._reset(e.values,n._options.resetOptions),s.current=e.values,i((e=>({...e})))):n._resetDefaultValues()}),[n,e.values]),t.useEffect((()=>{n._state.mount||(n._setValid(),n._state.mount=!0),n._state.watch&&(n._state.watch=!1,n._subjects.state.next({...n._formState})),n._removeUnmounted()})),r.current.formState=B(a,n),r.current},e.useFormContext=R,e.useFormState=q,e.useWatch=I}));
//# sourceMappingURL=index.umd.js.map
