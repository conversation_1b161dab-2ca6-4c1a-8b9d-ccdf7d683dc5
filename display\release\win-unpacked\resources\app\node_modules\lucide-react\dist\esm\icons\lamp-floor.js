/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 10v12", key: "6ubwww" }],
  [
    "path",
    {
      d: "M17.929 7.629A1 1 0 0 1 17 9H7a1 1 0 0 1-.928-1.371l2-5A1 1 0 0 1 9 2h6a1 1 0 0 1 .928.629z",
      key: "1o95gh"
    }
  ],
  ["path", { d: "M9 22h6", key: "1rlq3v" }]
];
const LampFloor = createLucideIcon("lamp-floor", __iconNode);

export { __iconNode, LampFloor as default };
//# sourceMappingURL=lamp-floor.js.map
