#!/usr/bin/env node
var $n=Object.defineProperty;var a=(t,e)=>$n(t,"name",{value:e,configurable:!0});import{constants as dt}from"node:os";import Tn from"tty";import{transformSync as xn}from"esbuild";import{v as On}from"./package--YiorJOq.mjs";import{r as We,g as Nn,i as Hn}from"./get-pipe-path-BHW2eJdv.mjs";import{pathToFileURL as Ln,fileURLToPath as In}from"node:url";import Pn from"child_process";import z from"path";import oe from"fs";import{i as wu,m as kn,t as Mn}from"./node-features-_8ZFwP_x.mjs";import Gn from"node:path";import Wn from"events";import _e from"util";import jn from"stream";import Ru from"os";import Un from"node:net";import Et from"node:fs";import{t as Kn}from"./temporary-directory-CwHp0_NW.mjs";import"module";const Vn="known-flag",zn="unknown-flag",Yn="argument",{stringify:Ae}=JSON,qn=/\B([A-Z])/g,Xn=a(t=>t.replace(qn,"-$1").toLowerCase(),"v$1"),{hasOwnProperty:Qn}=Object.prototype,ye=a((t,e)=>Qn.call(t,e),"w$2"),Zn=a(t=>Array.isArray(t),"L$2"),bu=a(t=>typeof t=="function"?[t,!1]:Zn(t)?[t[0],!0]:bu(t.type),"b$2"),Jn=a((t,e)=>t===Boolean?e!=="false":e,"d$2"),er=a((t,e)=>typeof e=="boolean"?e:t===Number&&e===""?Number.NaN:t(e),"m$1"),tr=/[\s.:=]/,ur=a(t=>{const e=`Flag name ${Ae(t)}`;if(t.length===0)throw new Error(`${e} cannot be empty`);if(t.length===1)throw new Error(`${e} must be longer than a character`);const u=t.match(tr);if(u)throw new Error(`${e} cannot contain ${Ae(u?.[0])}`)},"B"),sr=a(t=>{const e={},u=a((s,n)=>{if(ye(e,s))throw new Error(`Duplicate flags named ${Ae(s)}`);e[s]=n},"r");for(const s in t){if(!ye(t,s))continue;ur(s);const n=t[s],r=[[],...bu(n),n];u(s,r);const i=Xn(s);if(s!==i&&u(i,r),"alias"in n&&typeof n.alias=="string"){const{alias:o}=n,D=`Flag alias ${Ae(o)} for flag ${Ae(s)}`;if(o.length===0)throw new Error(`${D} cannot be empty`);if(o.length>1)throw new Error(`${D} must be a single character`);u(o,r)}}return e},"K$1"),nr=a((t,e)=>{const u={};for(const s in t){if(!ye(t,s))continue;const[n,,r,i]=e[s];if(n.length===0&&"default"in i){let{default:o}=i;typeof o=="function"&&(o=o()),u[s]=o}else u[s]=r?n:n.pop()}return u},"_$2"),je="--",rr=/[.:=]/,ir=/^-{1,2}\w/,or=a(t=>{if(!ir.test(t))return;const e=!t.startsWith(je);let u=t.slice(e?1:2),s;const n=u.match(rr);if(n){const{index:r}=n;s=u.slice(r+1),u=u.slice(0,r)}return[u,s,e]},"N"),Dr=a((t,{onFlag:e,onArgument:u})=>{let s;const n=a((r,i)=>{if(typeof s!="function")return!0;s(r,i),s=void 0},"o");for(let r=0;r<t.length;r+=1){const i=t[r];if(i===je){n();const D=t.slice(r+1);u?.(D,[r],!0);break}const o=or(i);if(o){if(n(),!e)continue;const[D,c,f]=o;if(f)for(let h=0;h<D.length;h+=1){n();const l=h===D.length-1;s=e(D[h],l?c:void 0,[r,h+1,l])}else s=e(D,c,[r])}else n(i,[r])&&u?.([i],[r])}n()},"$$1"),ar=a((t,e)=>{for(const[u,s,n]of e.reverse()){if(s){const r=t[u];let i=r.slice(0,s);if(n||(i+=r.slice(s+1)),i!=="-"){t[u]=i;continue}}t.splice(u,1)}},"E"),vu=a((t,e=process.argv.slice(2),{ignore:u}={})=>{const s=[],n=sr(t),r={},i=[];return i[je]=[],Dr(e,{onFlag(o,D,c){const f=ye(n,o);if(!u?.(f?Vn:zn,o,D)){if(f){const[h,l]=n[o],p=Jn(l,D),C=a((g,y)=>{s.push(c),y&&s.push(y),h.push(er(l,g||""))},"p");return p===void 0?C:C(p)}ye(r,o)||(r[o]=[]),r[o].push(D===void 0?!0:D),s.push(c)}},onArgument(o,D,c){u?.(Yn,e[D[0]])||(i.push(...o),c?(i[je]=o,e.splice(D[0])):s.push(D))}}),ar(e,s),{flags:nr(t,n),unknownFlags:r,_:i}},"U$2");var lr=Object.create,Ue=Object.defineProperty,cr=Object.defineProperties,fr=Object.getOwnPropertyDescriptor,hr=Object.getOwnPropertyDescriptors,dr=Object.getOwnPropertyNames,Su=Object.getOwnPropertySymbols,Er=Object.getPrototypeOf,Bu=Object.prototype.hasOwnProperty,pr=Object.prototype.propertyIsEnumerable,$u=a((t,e,u)=>e in t?Ue(t,e,{enumerable:!0,configurable:!0,writable:!0,value:u}):t[e]=u,"W$1"),Ke=a((t,e)=>{for(var u in e||(e={}))Bu.call(e,u)&&$u(t,u,e[u]);if(Su)for(var u of Su(e))pr.call(e,u)&&$u(t,u,e[u]);return t},"p"),pt=a((t,e)=>cr(t,hr(e)),"c"),Cr=a(t=>Ue(t,"__esModule",{value:!0}),"nD"),Fr=a((t,e)=>()=>(t&&(e=t(t=0)),e),"rD"),gr=a((t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),"iD"),mr=a((t,e,u,s)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of dr(e))!Bu.call(t,n)&&n!=="default"&&Ue(t,n,{get:a(()=>e[n],"get"),enumerable:!(s=fr(e,n))||s.enumerable});return t},"oD"),_r=a((t,e)=>mr(Cr(Ue(t!=null?lr(Er(t)):{},"default",{value:t,enumerable:!0})),t),"BD"),K=Fr(()=>{}),Ar=gr((t,e)=>{K(),e.exports=function(){return/\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67)\uDB40\uDC7F|(?:\uD83E\uDDD1\uD83C\uDFFF\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFC-\uDFFF])|\uD83D\uDC68(?:\uD83C\uDFFB(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|[\u2695\u2696\u2708]\uFE0F|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))?|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])\uFE0F|\u200D(?:(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D[\uDC66\uDC67])|\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC)?|(?:\uD83D\uDC69(?:\uD83C\uDFFB\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69]))|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC69(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83E\uDDD1(?:\u200D(?:\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDE36\u200D\uD83C\uDF2B|\uD83C\uDFF3\uFE0F\u200D\u26A7|\uD83D\uDC3B\u200D\u2744|(?:(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\uD83C\uDFF4\u200D\u2620|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])\u200D[\u2640\u2642]|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u2600-\u2604\u260E\u2611\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26B0\u26B1\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0\u26F1\u26F4\u26F7\u26F8\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u3030\u303D\u3297\u3299]|\uD83C[\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]|\uD83D[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3])\uFE0F|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDE35\u200D\uD83D\uDCAB|\uD83D\uDE2E\u200D\uD83D\uDCA8|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83E\uDDD1(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83D\uDC69(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF6\uD83C\uDDE6|\uD83C\uDDF4\uD83C\uDDF2|\uD83D\uDC08\u200D\u2B1B|\u2764\uFE0F\u200D(?:\uD83D\uDD25|\uD83E\uDE79)|\uD83D\uDC41\uFE0F|\uD83C\uDFF3\uFE0F|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|[#\*0-9]\uFE0F\u20E3|\u2764\uFE0F|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|\uD83C\uDFF4|(?:[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270C\u270D]|\uD83D[\uDD74\uDD90])(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC08\uDC15\uDC3B\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE2E\uDE35\uDE36\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5]|\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD]|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF]|[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0D\uDD0E\uDD10-\uDD17\uDD1D\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78\uDD7A-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCB\uDDD0\uDDE0-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6]|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26A7\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDED5-\uDED7\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])\uFE0F|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDC8F\uDC91\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1F\uDD26\uDD30-\uDD39\uDD3C-\uDD3E\uDD77\uDDB5\uDDB6\uDDB8\uDDB9\uDDBB\uDDCD-\uDDCF\uDDD1-\uDDDD])/g}});K(),K(),K();var yr=a(t=>{var e,u,s;let n=(e=process.stdout.columns)!=null?e:Number.POSITIVE_INFINITY;return typeof t=="function"&&(t=t(n)),t||(t={}),Array.isArray(t)?{columns:t,stdoutColumns:n}:{columns:(u=t.columns)!=null?u:[],stdoutColumns:(s=t.stdoutColumns)!=null?s:n}},"v");K(),K(),K(),K(),K();function wr({onlyFirst:t=!1}={}){let e=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(e,t?void 0:"g")}a(wr,"w$1");function Tu(t){if(typeof t!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof t}\``);return t.replace(wr(),"")}a(Tu,"d$1"),K();function Rr(t){return Number.isInteger(t)?t>=4352&&(t<=4447||t===9001||t===9002||11904<=t&&t<=12871&&t!==12351||12880<=t&&t<=19903||19968<=t&&t<=42182||43360<=t&&t<=43388||44032<=t&&t<=55203||63744<=t&&t<=64255||65040<=t&&t<=65049||65072<=t&&t<=65131||65281<=t&&t<=65376||65504<=t&&t<=65510||110592<=t&&t<=110593||127488<=t&&t<=127569||131072<=t&&t<=262141):!1}a(Rr,"y$1");var br=_r(Ar());function De(t){if(typeof t!="string"||t.length===0||(t=Tu(t),t.length===0))return 0;t=t.replace((0,br.default)(),"  ");let e=0;for(let u=0;u<t.length;u++){let s=t.codePointAt(u);s<=31||s>=127&&s<=159||s>=768&&s<=879||(s>65535&&u++,e+=Rr(s)?2:1)}return e}a(De,"g");var xu=a(t=>Math.max(...t.split(`
`).map(De)),"b$1"),vr=a(t=>{let e=[];for(let u of t){let{length:s}=u,n=s-e.length;for(let r=0;r<n;r+=1)e.push(0);for(let r=0;r<s;r+=1){let i=xu(u[r]);i>e[r]&&(e[r]=i)}}return e},"k$1");K();var Ou=/^\d+%$/,Nu={width:"auto",align:"left",contentWidth:0,paddingLeft:0,paddingRight:0,paddingTop:0,paddingBottom:0,horizontalPadding:0,paddingLeftString:"",paddingRightString:""},Sr=a((t,e)=>{var u;let s=[];for(let n=0;n<t.length;n+=1){let r=(u=e[n])!=null?u:"auto";if(typeof r=="number"||r==="auto"||r==="content-width"||typeof r=="string"&&Ou.test(r)){s.push(pt(Ke({},Nu),{width:r,contentWidth:t[n]}));continue}if(r&&typeof r=="object"){let i=pt(Ke(Ke({},Nu),r),{contentWidth:t[n]});i.horizontalPadding=i.paddingLeft+i.paddingRight,s.push(i);continue}throw new Error(`Invalid column width: ${JSON.stringify(r)}`)}return s},"sD");function Br(t,e){for(let u of t){let{width:s}=u;if(s==="content-width"&&(u.width=u.contentWidth),s==="auto"){let D=Math.min(20,u.contentWidth);u.width=D,u.autoOverflow=u.contentWidth-D}if(typeof s=="string"&&Ou.test(s)){let D=Number.parseFloat(s.slice(0,-1))/100;u.width=Math.floor(e*D)-(u.paddingLeft+u.paddingRight)}let{horizontalPadding:n}=u,r=1,i=r+n;if(i>=e){let D=i-e,c=Math.ceil(u.paddingLeft/n*D),f=D-c;u.paddingLeft-=c,u.paddingRight-=f,u.horizontalPadding=u.paddingLeft+u.paddingRight}u.paddingLeftString=u.paddingLeft?" ".repeat(u.paddingLeft):"",u.paddingRightString=u.paddingRight?" ".repeat(u.paddingRight):"";let o=e-u.horizontalPadding;u.width=Math.max(Math.min(u.width,o),r)}}a(Br,"aD");var Hu=a(()=>Object.assign([],{columns:0}),"G$1");function $r(t,e){let u=[Hu()],[s]=u;for(let n of t){let r=n.width+n.horizontalPadding;s.columns+r>e&&(s=Hu(),u.push(s)),s.push(n),s.columns+=r}for(let n of u){let r=n.reduce((l,p)=>l+p.width+p.horizontalPadding,0),i=e-r;if(i===0)continue;let o=n.filter(l=>"autoOverflow"in l),D=o.filter(l=>l.autoOverflow>0),c=D.reduce((l,p)=>l+p.autoOverflow,0),f=Math.min(c,i);for(let l of D){let p=Math.floor(l.autoOverflow/c*f);l.width+=p,i-=p}let h=Math.floor(i/o.length);for(let l=0;l<o.length;l+=1){let p=o[l];l===o.length-1?p.width+=i:p.width+=h,i-=h}}return u}a($r,"lD");function Tr(t,e,u){let s=Sr(u,e);return Br(s,t),$r(s,t)}a(Tr,"Z$1"),K(),K(),K();var Ct=10,Lu=a((t=0)=>e=>`\x1B[${e+t}m`,"U$1"),Iu=a((t=0)=>e=>`\x1B[${38+t};5;${e}m`,"V$1"),Pu=a((t=0)=>(e,u,s)=>`\x1B[${38+t};2;${e};${u};${s}m`,"Y");function xr(){let t=new Map,e={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};e.color.gray=e.color.blackBright,e.bgColor.bgGray=e.bgColor.bgBlackBright,e.color.grey=e.color.blackBright,e.bgColor.bgGrey=e.bgColor.bgBlackBright;for(let[u,s]of Object.entries(e)){for(let[n,r]of Object.entries(s))e[n]={open:`\x1B[${r[0]}m`,close:`\x1B[${r[1]}m`},s[n]=e[n],t.set(r[0],r[1]);Object.defineProperty(e,u,{value:s,enumerable:!1})}return Object.defineProperty(e,"codes",{value:t,enumerable:!1}),e.color.close="\x1B[39m",e.bgColor.close="\x1B[49m",e.color.ansi=Lu(),e.color.ansi256=Iu(),e.color.ansi16m=Pu(),e.bgColor.ansi=Lu(Ct),e.bgColor.ansi256=Iu(Ct),e.bgColor.ansi16m=Pu(Ct),Object.defineProperties(e,{rgbToAnsi256:{value:a((u,s,n)=>u===s&&s===n?u<8?16:u>248?231:Math.round((u-8)/247*24)+232:16+36*Math.round(u/255*5)+6*Math.round(s/255*5)+Math.round(n/255*5),"value"),enumerable:!1},hexToRgb:{value:a(u=>{let s=/(?<colorString>[a-f\d]{6}|[a-f\d]{3})/i.exec(u.toString(16));if(!s)return[0,0,0];let{colorString:n}=s.groups;n.length===3&&(n=n.split("").map(i=>i+i).join(""));let r=Number.parseInt(n,16);return[r>>16&255,r>>8&255,r&255]},"value"),enumerable:!1},hexToAnsi256:{value:a(u=>e.rgbToAnsi256(...e.hexToRgb(u)),"value"),enumerable:!1},ansi256ToAnsi:{value:a(u=>{if(u<8)return 30+u;if(u<16)return 90+(u-8);let s,n,r;if(u>=232)s=((u-232)*10+8)/255,n=s,r=s;else{u-=16;let D=u%36;s=Math.floor(u/36)/5,n=Math.floor(D/6)/5,r=D%6/5}let i=Math.max(s,n,r)*2;if(i===0)return 30;let o=30+(Math.round(r)<<2|Math.round(n)<<1|Math.round(s));return i===2&&(o+=60),o},"value"),enumerable:!1},rgbToAnsi:{value:a((u,s,n)=>e.ansi256ToAnsi(e.rgbToAnsi256(u,s,n)),"value"),enumerable:!1},hexToAnsi:{value:a(u=>e.ansi256ToAnsi(e.hexToAnsi256(u)),"value"),enumerable:!1}}),e}a(xr,"AD");var Or=xr(),Nr=Or,Ve=new Set(["\x1B","\x9B"]),Hr=39,Ft="\x07",ku="[",Lr="]",Mu="m",gt=`${Lr}8;;`,Gu=a(t=>`${Ve.values().next().value}${ku}${t}${Mu}`,"J$1"),Wu=a(t=>`${Ve.values().next().value}${gt}${t}${Ft}`,"Q"),Ir=a(t=>t.split(" ").map(e=>De(e)),"hD"),mt=a((t,e,u)=>{let s=[...e],n=!1,r=!1,i=De(Tu(t[t.length-1]));for(let[o,D]of s.entries()){let c=De(D);if(i+c<=u?t[t.length-1]+=D:(t.push(D),i=0),Ve.has(D)&&(n=!0,r=s.slice(o+1).join("").startsWith(gt)),n){r?D===Ft&&(n=!1,r=!1):D===Mu&&(n=!1);continue}i+=c,i===u&&o<s.length-1&&(t.push(""),i=0)}!i&&t[t.length-1].length>0&&t.length>1&&(t[t.length-2]+=t.pop())},"S$1"),Pr=a(t=>{let e=t.split(" "),u=e.length;for(;u>0&&!(De(e[u-1])>0);)u--;return u===e.length?t:e.slice(0,u).join(" ")+e.slice(u).join("")},"cD"),kr=a((t,e,u={})=>{if(u.trim!==!1&&t.trim()==="")return"";let s="",n,r,i=Ir(t),o=[""];for(let[c,f]of t.split(" ").entries()){u.trim!==!1&&(o[o.length-1]=o[o.length-1].trimStart());let h=De(o[o.length-1]);if(c!==0&&(h>=e&&(u.wordWrap===!1||u.trim===!1)&&(o.push(""),h=0),(h>0||u.trim===!1)&&(o[o.length-1]+=" ",h++)),u.hard&&i[c]>e){let l=e-h,p=1+Math.floor((i[c]-l-1)/e);Math.floor((i[c]-1)/e)<p&&o.push(""),mt(o,f,e);continue}if(h+i[c]>e&&h>0&&i[c]>0){if(u.wordWrap===!1&&h<e){mt(o,f,e);continue}o.push("")}if(h+i[c]>e&&u.wordWrap===!1){mt(o,f,e);continue}o[o.length-1]+=f}u.trim!==!1&&(o=o.map(c=>Pr(c)));let D=[...o.join(`
`)];for(let[c,f]of D.entries()){if(s+=f,Ve.has(f)){let{groups:l}=new RegExp(`(?:\\${ku}(?<code>\\d+)m|\\${gt}(?<uri>.*)${Ft})`).exec(D.slice(c).join(""))||{groups:{}};if(l.code!==void 0){let p=Number.parseFloat(l.code);n=p===Hr?void 0:p}else l.uri!==void 0&&(r=l.uri.length===0?void 0:l.uri)}let h=Nr.codes.get(Number(n));D[c+1]===`
`?(r&&(s+=Wu("")),n&&h&&(s+=Gu(h))):f===`
`&&(n&&h&&(s+=Gu(n)),r&&(s+=Wu(r)))}return s},"dD");function Mr(t,e,u){return String(t).normalize().replace(/\r\n/g,`
`).split(`
`).map(s=>kr(s,e,u)).join(`
`)}a(Mr,"T$1");var ju=a(t=>Array.from({length:t}).fill(""),"X");function Gr(t,e){let u=[],s=0;for(let n of t){let r=0,i=n.map(D=>{var c;let f=(c=e[s])!=null?c:"";s+=1,D.preprocess&&(f=D.preprocess(f)),xu(f)>D.width&&(f=Mr(f,D.width,{hard:!0}));let h=f.split(`
`);if(D.postprocess){let{postprocess:l}=D;h=h.map((p,C)=>l.call(D,p,C))}return D.paddingTop&&h.unshift(...ju(D.paddingTop)),D.paddingBottom&&h.push(...ju(D.paddingBottom)),h.length>r&&(r=h.length),pt(Ke({},D),{lines:h})}),o=[];for(let D=0;D<r;D+=1){let c=i.map(f=>{var h;let l=(h=f.lines[D])!=null?h:"",p=Number.isFinite(f.width)?" ".repeat(f.width-De(l)):"",C=f.paddingLeftString;return f.align==="right"&&(C+=p),C+=l,f.align==="left"&&(C+=p),C+f.paddingRightString}).join("");o.push(c)}u.push(o.join(`
`))}return u.join(`
`)}a(Gr,"P");function Wr(t,e){if(!t||t.length===0)return"";let u=vr(t),s=u.length;if(s===0)return"";let{stdoutColumns:n,columns:r}=yr(e);if(r.length>s)throw new Error(`${r.length} columns defined, but only ${s} columns found`);let i=Tr(n,r,u);return t.map(o=>Gr(i,o)).join(`
`)}a(Wr,"mD"),K();var jr=["<",">","=",">=","<="];function Ur(t){if(!jr.includes(t))throw new TypeError(`Invalid breakpoint operator: ${t}`)}a(Ur,"xD");function Kr(t){let e=Object.keys(t).map(u=>{let[s,n]=u.split(" ");Ur(s);let r=Number.parseInt(n,10);if(Number.isNaN(r))throw new TypeError(`Invalid breakpoint value: ${n}`);let i=t[u];return{operator:s,breakpoint:r,value:i}}).sort((u,s)=>s.breakpoint-u.breakpoint);return u=>{var s;return(s=e.find(({operator:n,breakpoint:r})=>n==="="&&u===r||n===">"&&u>r||n==="<"&&u<r||n===">="&&u>=r||n==="<="&&u<=r))==null?void 0:s.value}}a(Kr,"wD");const Vr=a(t=>t.replace(/[\W_]([a-z\d])?/gi,(e,u)=>u?u.toUpperCase():""),"S"),zr=a(t=>t.replace(/\B([A-Z])/g,"-$1").toLowerCase(),"q"),Yr={"> 80":[{width:"content-width",paddingLeft:2,paddingRight:8},{width:"auto"}],"> 40":[{width:"auto",paddingLeft:2,paddingRight:8,preprocess:a(t=>t.trim(),"preprocess")},{width:"100%",paddingLeft:2,paddingBottom:1}],"> 0":{stdoutColumns:1e3,columns:[{width:"content-width",paddingLeft:2,paddingRight:8},{width:"content-width"}]}};function qr(t){let e=!1;return{type:"table",data:{tableData:Object.keys(t).sort((u,s)=>u.localeCompare(s)).map(u=>{const s=t[u],n="alias"in s;return n&&(e=!0),{name:u,flag:s,flagFormatted:`--${zr(u)}`,aliasesEnabled:e,aliasFormatted:n?`-${s.alias}`:void 0}}).map(u=>(u.aliasesEnabled=e,[{type:"flagName",data:u},{type:"flagDescription",data:u}])),tableBreakpoints:Yr}}}a(qr,"D");const Uu=a(t=>!t||(t.version??(t.help?t.help.version:void 0)),"A"),Ku=a(t=>{const e="parent"in t&&t.parent?.name;return(e?`${e} `:"")+t.name},"C");function Xr(t){const e=[];t.name&&e.push(Ku(t));const u=Uu(t)??("parent"in t&&Uu(t.parent));if(u&&e.push(`v${u}`),e.length!==0)return{id:"name",type:"text",data:`${e.join(" ")}
`}}a(Xr,"R");function Qr(t){const{help:e}=t;if(!(!e||!e.description))return{id:"description",type:"text",data:`${e.description}
`}}a(Qr,"L");function Zr(t){const e=t.help||{};if("usage"in e)return e.usage?{id:"usage",type:"section",data:{title:"Usage:",body:Array.isArray(e.usage)?e.usage.join(`
`):e.usage}}:void 0;if(t.name){const u=[],s=[Ku(t)];if(t.flags&&Object.keys(t.flags).length>0&&s.push("[flags...]"),t.parameters&&t.parameters.length>0){const{parameters:n}=t,r=n.indexOf("--"),i=r>-1&&n.slice(r+1).some(o=>o.startsWith("<"));s.push(n.map(o=>o!=="--"?o:i?"--":"[--]").join(" "))}if(s.length>1&&u.push(s.join(" ")),"commands"in t&&t.commands?.length&&u.push(`${t.name} <command>`),u.length>0)return{id:"usage",type:"section",data:{title:"Usage:",body:u.join(`
`)}}}}a(Zr,"T");function Jr(t){return!("commands"in t)||!t.commands?.length?void 0:{id:"commands",type:"section",data:{title:"Commands:",body:{type:"table",data:{tableData:t.commands.map(e=>[e.options.name,e.options.help?e.options.help.description:""]),tableOptions:[{width:"content-width",paddingLeft:2,paddingRight:8}]}},indentBody:0}}}a(Jr,"_");function ei(t){if(!(!t.flags||Object.keys(t.flags).length===0))return{id:"flags",type:"section",data:{title:"Flags:",body:qr(t.flags),indentBody:0}}}a(ei,"k");function ti(t){const{help:e}=t;if(!e||!e.examples||e.examples.length===0)return;let{examples:u}=e;if(Array.isArray(u)&&(u=u.join(`
`)),u)return{id:"examples",type:"section",data:{title:"Examples:",body:u}}}a(ti,"F");function ui(t){if(!("alias"in t)||!t.alias)return;const{alias:e}=t;return{id:"aliases",type:"section",data:{title:"Aliases:",body:Array.isArray(e)?e.join(", "):e}}}a(ui,"H");const si=a(t=>[Xr,Qr,Zr,Jr,ei,ti,ui].map(e=>e(t)).filter(Boolean),"U"),ni=Tn.WriteStream.prototype.hasColors();class ri{static{a(this,"M")}text(e){return e}bold(e){return ni?`\x1B[1m${e}\x1B[22m`:e.toLocaleUpperCase()}indentText({text:e,spaces:u}){return e.replace(/^/gm," ".repeat(u))}heading(e){return this.bold(e)}section({title:e,body:u,indentBody:s=2}){return`${(e?`${this.heading(e)}
`:"")+(u?this.indentText({text:this.render(u),spaces:s}):"")}
`}table({tableData:e,tableOptions:u,tableBreakpoints:s}){return Wr(e.map(n=>n.map(r=>this.render(r))),s?Kr(s):u)}flagParameter(e){return e===Boolean?"":e===String?"<string>":e===Number?"<number>":Array.isArray(e)?this.flagParameter(e[0]):"<value>"}flagOperator(e){return" "}flagName(e){const{flag:u,flagFormatted:s,aliasesEnabled:n,aliasFormatted:r}=e;let i="";if(r?i+=`${r}, `:n&&(i+="    "),i+=s,"placeholder"in u&&typeof u.placeholder=="string")i+=`${this.flagOperator(e)}${u.placeholder}`;else{const o=this.flagParameter("type"in u?u.type:u);o&&(i+=`${this.flagOperator(e)}${o}`)}return i}flagDefault(e){return JSON.stringify(e)}flagDescription({flag:e}){let u="description"in e?e.description??"":"";if("default"in e){let{default:s}=e;typeof s=="function"&&(s=s()),s&&(u+=` (default: ${this.flagDefault(s)})`)}return u}render(e){if(typeof e=="string")return e;if(Array.isArray(e))return e.map(u=>this.render(u)).join(`
`);if("type"in e&&this[e.type]){const u=this[e.type];if(typeof u=="function")return u.call(this,e.data)}throw new Error(`Invalid node type: ${JSON.stringify(e)}`)}}const _t=/^[\w.-]+$/,{stringify:ee}=JSON,ii=/[|\\{}()[\]^$+*?.]/;function At(t){const e=[];let u,s;for(const n of t){if(s)throw new Error(`Invalid parameter: Spread parameter ${ee(s)} must be last`);const r=n[0],i=n[n.length-1];let o;if(r==="<"&&i===">"&&(o=!0,u))throw new Error(`Invalid parameter: Required parameter ${ee(n)} cannot come after optional parameter ${ee(u)}`);if(r==="["&&i==="]"&&(o=!1,u=n),o===void 0)throw new Error(`Invalid parameter: ${ee(n)}. Must be wrapped in <> (required parameter) or [] (optional parameter)`);let D=n.slice(1,-1);const c=D.slice(-3)==="...";c&&(s=n,D=D.slice(0,-3));const f=D.match(ii);if(f)throw new Error(`Invalid parameter: ${ee(n)}. Invalid character found ${ee(f[0])}`);e.push({name:D,required:o,spread:c})}return e}a(At,"w");function yt(t,e,u,s){for(let n=0;n<e.length;n+=1){const{name:r,required:i,spread:o}=e[n],D=Vr(r);if(D in t)throw new Error(`Invalid parameter: ${ee(r)} is used more than once.`);const c=o?u.slice(n):u[n];if(o&&(n=e.length),i&&(!c||o&&c.length===0))return console.error(`Error: Missing required parameter ${ee(r)}
`),s(),process.exit(1);t[D]=c}}a(yt,"b");function oi(t){return t===void 0||t!==!1}a(oi,"W");function Vu(t,e,u,s){const n={...e.flags},r=e.version;r&&(n.version={type:Boolean,description:"Show version"});const{help:i}=e,o=oi(i);o&&!("help"in n)&&(n.help={type:Boolean,alias:"h",description:"Show help"});const D=vu(n,s,{ignore:e.ignoreArgv}),c=a(()=>{console.log(e.version)},"f");if(r&&D.flags.version===!0)return c(),process.exit(0);const f=new ri,h=o&&i?.render?i.render:C=>f.render(C),l=a(C=>{const g=si({...e,...C?{help:C}:{},flags:n});console.log(h(g,f))},"u");if(o&&D.flags.help===!0)return l(),process.exit(0);if(e.parameters){let{parameters:C}=e,g=D._;const y=C.indexOf("--"),B=C.slice(y+1),H=Object.create(null);if(y>-1&&B.length>0){C=C.slice(0,y);const $=D._["--"];g=g.slice(0,-$.length||void 0),yt(H,At(C),g,l),yt(H,At(B),$,l)}else yt(H,At(C),g,l);Object.assign(D._,H)}const p={...D,showVersion:c,showHelp:l};return typeof u=="function"&&u(p),{command:t,...p}}a(Vu,"x");function Di(t,e){const u=new Map;for(const s of e){const n=[s.options.name],{alias:r}=s.options;r&&(Array.isArray(r)?n.push(...r):n.push(r));for(const i of n){if(u.has(i))throw new Error(`Duplicate command name found: ${ee(i)}`);u.set(i,s)}}return u.get(t)}a(Di,"z");function zu(t,e,u=process.argv.slice(2)){if(!t)throw new Error("Options is required");if("name"in t&&(!t.name||!_t.test(t.name)))throw new Error(`Invalid script name: ${ee(t.name)}`);const s=u[0];if(t.commands&&_t.test(s)){const n=Di(s,t.commands);if(n)return Vu(n.options.name,{...n.options,parent:t},n.callback,u.slice(1))}return Vu(void 0,t,e,u)}a(zu,"Z");function ai(t,e){if(!t)throw new Error("Command options are required");const{name:u}=t;if(t.name===void 0)throw new Error("Command name is required");if(!_t.test(u))throw new Error(`Invalid command name ${JSON.stringify(u)}. Command names must be one word.`);return{options:t,callback:e}}a(ai,"G");var li=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function ci(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}a(ci,"getDefaultExportFromCjs");var he={exports:{}},wt,Yu;function fi(){if(Yu)return wt;Yu=1,wt=s,s.sync=n;var t=oe;function e(r,i){var o=i.pathExt!==void 0?i.pathExt:process.env.PATHEXT;if(!o||(o=o.split(";"),o.indexOf("")!==-1))return!0;for(var D=0;D<o.length;D++){var c=o[D].toLowerCase();if(c&&r.substr(-c.length).toLowerCase()===c)return!0}return!1}a(e,"checkPathExt");function u(r,i,o){return!r.isSymbolicLink()&&!r.isFile()?!1:e(i,o)}a(u,"checkStat");function s(r,i,o){t.stat(r,function(D,c){o(D,D?!1:u(c,r,i))})}a(s,"isexe");function n(r,i){return u(t.statSync(r),r,i)}return a(n,"sync"),wt}a(fi,"requireWindows");var Rt,qu;function hi(){if(qu)return Rt;qu=1,Rt=e,e.sync=u;var t=oe;function e(r,i,o){t.stat(r,function(D,c){o(D,D?!1:s(c,i))})}a(e,"isexe");function u(r,i){return s(t.statSync(r),i)}a(u,"sync");function s(r,i){return r.isFile()&&n(r,i)}a(s,"checkStat");function n(r,i){var o=r.mode,D=r.uid,c=r.gid,f=i.uid!==void 0?i.uid:process.getuid&&process.getuid(),h=i.gid!==void 0?i.gid:process.getgid&&process.getgid(),l=parseInt("100",8),p=parseInt("010",8),C=parseInt("001",8),g=l|p,y=o&C||o&p&&c===h||o&l&&D===f||o&g&&f===0;return y}return a(n,"checkMode"),Rt}a(hi,"requireMode");var ze;process.platform==="win32"||li.TESTING_WINDOWS?ze=fi():ze=hi();var di=bt;bt.sync=Ei;function bt(t,e,u){if(typeof e=="function"&&(u=e,e={}),!u){if(typeof Promise!="function")throw new TypeError("callback not provided");return new Promise(function(s,n){bt(t,e||{},function(r,i){r?n(r):s(i)})})}ze(t,e||{},function(s,n){s&&(s.code==="EACCES"||e&&e.ignoreErrors)&&(s=null,n=!1),u(s,n)})}a(bt,"isexe$1");function Ei(t,e){try{return ze.sync(t,e||{})}catch(u){if(e&&e.ignoreErrors||u.code==="EACCES")return!1;throw u}}a(Ei,"sync");const de=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",Xu=z,pi=de?";":":",Qu=di,Zu=a(t=>Object.assign(new Error(`not found: ${t}`),{code:"ENOENT"}),"getNotFoundError"),Ju=a((t,e)=>{const u=e.colon||pi,s=t.match(/\//)||de&&t.match(/\\/)?[""]:[...de?[process.cwd()]:[],...(e.path||process.env.PATH||"").split(u)],n=de?e.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"",r=de?n.split(u):[""];return de&&t.indexOf(".")!==-1&&r[0]!==""&&r.unshift(""),{pathEnv:s,pathExt:r,pathExtExe:n}},"getPathInfo"),es=a((t,e,u)=>{typeof e=="function"&&(u=e,e={}),e||(e={});const{pathEnv:s,pathExt:n,pathExtExe:r}=Ju(t,e),i=[],o=a(c=>new Promise((f,h)=>{if(c===s.length)return e.all&&i.length?f(i):h(Zu(t));const l=s[c],p=/^".*"$/.test(l)?l.slice(1,-1):l,C=Xu.join(p,t),g=!p&&/^\.[\\\/]/.test(t)?t.slice(0,2)+C:C;f(D(g,c,0))}),"step"),D=a((c,f,h)=>new Promise((l,p)=>{if(h===n.length)return l(o(f+1));const C=n[h];Qu(c+C,{pathExt:r},(g,y)=>{if(!g&&y)if(e.all)i.push(c+C);else return l(c+C);return l(D(c,f,h+1))})}),"subStep");return u?o(0).then(c=>u(null,c),u):o(0)},"which$1"),Ci=a((t,e)=>{e=e||{};const{pathEnv:u,pathExt:s,pathExtExe:n}=Ju(t,e),r=[];for(let i=0;i<u.length;i++){const o=u[i],D=/^".*"$/.test(o)?o.slice(1,-1):o,c=Xu.join(D,t),f=!D&&/^\.[\\\/]/.test(t)?t.slice(0,2)+c:c;for(let h=0;h<s.length;h++){const l=f+s[h];try{if(Qu.sync(l,{pathExt:n}))if(e.all)r.push(l);else return l}catch{}}}if(e.all&&r.length)return r;if(e.nothrow)return null;throw Zu(t)},"whichSync");var Fi=es;es.sync=Ci;var vt={exports:{}};const ts=a((t={})=>{const e=t.env||process.env;return(t.platform||process.platform)!=="win32"?"PATH":Object.keys(e).reverse().find(s=>s.toUpperCase()==="PATH")||"Path"},"pathKey");vt.exports=ts,vt.exports.default=ts;var gi=vt.exports;const us=z,mi=Fi,_i=gi;function ss(t,e){const u=t.options.env||process.env,s=process.cwd(),n=t.options.cwd!=null,r=n&&process.chdir!==void 0&&!process.chdir.disabled;if(r)try{process.chdir(t.options.cwd)}catch{}let i;try{i=mi.sync(t.command,{path:u[_i({env:u})],pathExt:e?us.delimiter:void 0})}catch{}finally{r&&process.chdir(s)}return i&&(i=us.resolve(n?t.options.cwd:"",i)),i}a(ss,"resolveCommandAttempt");function Ai(t){return ss(t)||ss(t,!0)}a(Ai,"resolveCommand$1");var yi=Ai,St={};const Bt=/([()\][%!^"`<>&|;, *?])/g;function wi(t){return t=t.replace(Bt,"^$1"),t}a(wi,"escapeCommand");function Ri(t,e){return t=`${t}`,t=t.replace(/(\\*)"/g,'$1$1\\"'),t=t.replace(/(\\*)$/,"$1$1"),t=`"${t}"`,t=t.replace(Bt,"^$1"),e&&(t=t.replace(Bt,"^$1")),t}a(Ri,"escapeArgument"),St.command=wi,St.argument=Ri;var bi=/^#!(.*)/;const vi=bi;var Si=a((t="")=>{const e=t.match(vi);if(!e)return null;const[u,s]=e[0].replace(/#! ?/,"").split(" "),n=u.split("/").pop();return n==="env"?s:s?`${n} ${s}`:n},"shebangCommand$1");const $t=oe,Bi=Si;function $i(t){const u=Buffer.alloc(150);let s;try{s=$t.openSync(t,"r"),$t.readSync(s,u,0,150,0),$t.closeSync(s)}catch{}return Bi(u.toString())}a($i,"readShebang$1");var Ti=$i;const xi=z,ns=yi,rs=St,Oi=Ti,Ni=process.platform==="win32",Hi=/\.(?:com|exe)$/i,Li=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function Ii(t){t.file=ns(t);const e=t.file&&Oi(t.file);return e?(t.args.unshift(t.file),t.command=e,ns(t)):t.file}a(Ii,"detectShebang");function Pi(t){if(!Ni)return t;const e=Ii(t),u=!Hi.test(e);if(t.options.forceShell||u){const s=Li.test(e);t.command=xi.normalize(t.command),t.command=rs.command(t.command),t.args=t.args.map(r=>rs.argument(r,s));const n=[t.command].concat(t.args).join(" ");t.args=["/d","/s","/c",`"${n}"`],t.command=process.env.comspec||"cmd.exe",t.options.windowsVerbatimArguments=!0}return t}a(Pi,"parseNonShell");function ki(t,e,u){e&&!Array.isArray(e)&&(u=e,e=null),e=e?e.slice(0):[],u=Object.assign({},u);const s={command:t,args:e,options:u,file:void 0,original:{command:t,args:e}};return u.shell?s:Pi(s)}a(ki,"parse$5");var Mi=ki;const Tt=process.platform==="win32";function xt(t,e){return Object.assign(new Error(`${e} ${t.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${e} ${t.command}`,path:t.command,spawnargs:t.args})}a(xt,"notFoundError");function Gi(t,e){if(!Tt)return;const u=t.emit;t.emit=function(s,n){if(s==="exit"){const r=is(n,e);if(r)return u.call(t,"error",r)}return u.apply(t,arguments)}}a(Gi,"hookChildProcess");function is(t,e){return Tt&&t===1&&!e.file?xt(e.original,"spawn"):null}a(is,"verifyENOENT");function Wi(t,e){return Tt&&t===1&&!e.file?xt(e.original,"spawnSync"):null}a(Wi,"verifyENOENTSync");var ji={hookChildProcess:Gi,verifyENOENT:is,verifyENOENTSync:Wi,notFoundError:xt};const os=Pn,Ot=Mi,Nt=ji;function Ds(t,e,u){const s=Ot(t,e,u),n=os.spawn(s.command,s.args,s.options);return Nt.hookChildProcess(n,s),n}a(Ds,"spawn");function Ui(t,e,u){const s=Ot(t,e,u),n=os.spawnSync(s.command,s.args,s.options);return n.error=n.error||Nt.verifyENOENTSync(n.status,s),n}a(Ui,"spawnSync"),he.exports=Ds,he.exports.spawn=Ds,he.exports.sync=Ui,he.exports._parse=Ot,he.exports._enoent=Nt;var Ki=he.exports,Vi=ci(Ki);const as=a((t,e)=>{const u={...process.env},s=["inherit","inherit","inherit"];process.send&&s.push("ipc"),e&&(e.noCache&&(u.TSX_DISABLE_CACHE="1"),e.tsconfigPath&&(u.TSX_TSCONFIG_PATH=e.tsconfigPath));const n=t.filter(r=>r!=="-i"&&r!=="--interactive").length===0;return Vi(process.execPath,["--require",We.resolve("./preflight.cjs"),...n?["--require",We.resolve("./patch-repl.cjs")]:[],wu(kn)?"--import":"--loader",Ln(We.resolve("./loader.mjs")).toString(),...t],{stdio:s,env:u})},"run");var Ye={};const zi=z,te="\\\\/",ls=`[^${te}]`,ue="\\.",Yi="\\+",qi="\\?",qe="\\/",Xi="(?=.)",cs="[^/]",Ht=`(?:${qe}|$)`,fs=`(?:^|${qe})`,Lt=`${ue}{1,2}${Ht}`,Qi=`(?!${ue})`,Zi=`(?!${fs}${Lt})`,Ji=`(?!${ue}{0,1}${Ht})`,eo=`(?!${Lt})`,to=`[^.${qe}]`,uo=`${cs}*?`,hs={DOT_LITERAL:ue,PLUS_LITERAL:Yi,QMARK_LITERAL:qi,SLASH_LITERAL:qe,ONE_CHAR:Xi,QMARK:cs,END_ANCHOR:Ht,DOTS_SLASH:Lt,NO_DOT:Qi,NO_DOTS:Zi,NO_DOT_SLASH:Ji,NO_DOTS_SLASH:eo,QMARK_NO_DOT:to,STAR:uo,START_ANCHOR:fs},so={...hs,SLASH_LITERAL:`[${te}]`,QMARK:ls,STAR:`${ls}*?`,DOTS_SLASH:`${ue}{1,2}(?:[${te}]|$)`,NO_DOT:`(?!${ue})`,NO_DOTS:`(?!(?:^|[${te}])${ue}{1,2}(?:[${te}]|$))`,NO_DOT_SLASH:`(?!${ue}{0,1}(?:[${te}]|$))`,NO_DOTS_SLASH:`(?!${ue}{1,2}(?:[${te}]|$))`,QMARK_NO_DOT:`[^.${te}]`,START_ANCHOR:`(?:^|[${te}])`,END_ANCHOR:`(?:[${te}]|$)`},no={alnum:"a-zA-Z0-9",alpha:"a-zA-Z",ascii:"\\x00-\\x7F",blank:" \\t",cntrl:"\\x00-\\x1F\\x7F",digit:"0-9",graph:"\\x21-\\x7E",lower:"a-z",print:"\\x20-\\x7E ",punct:"\\-!\"#$%&'()\\*+,./:;<=>?@[\\]^_`{|}~",space:" \\t\\r\\n\\v\\f",upper:"A-Z",word:"A-Za-z0-9_",xdigit:"A-Fa-f0-9"};var Xe={MAX_LENGTH:1024*64,POSIX_REGEX_SOURCE:no,REGEX_BACKSLASH:/\\(?![*+?^${}(|)[\]])/g,REGEX_NON_SPECIAL_CHARS:/^[^@![\].,$*+?^{}()|\\/]+/,REGEX_SPECIAL_CHARS:/[-*+?.^${}(|)[\]]/,REGEX_SPECIAL_CHARS_BACKREF:/(\\?)((\W)(\3*))/g,REGEX_SPECIAL_CHARS_GLOBAL:/([-*+?.^${}(|)[\]])/g,REGEX_REMOVE_BACKSLASH:/(?:\[.*?[^\\]\]|\\(?=.))/g,REPLACEMENTS:{"***":"*","**/**":"**","**/**/**":"**"},CHAR_0:48,CHAR_9:57,CHAR_UPPERCASE_A:65,CHAR_LOWERCASE_A:97,CHAR_UPPERCASE_Z:90,CHAR_LOWERCASE_Z:122,CHAR_LEFT_PARENTHESES:40,CHAR_RIGHT_PARENTHESES:41,CHAR_ASTERISK:42,CHAR_AMPERSAND:38,CHAR_AT:64,CHAR_BACKWARD_SLASH:92,CHAR_CARRIAGE_RETURN:13,CHAR_CIRCUMFLEX_ACCENT:94,CHAR_COLON:58,CHAR_COMMA:44,CHAR_DOT:46,CHAR_DOUBLE_QUOTE:34,CHAR_EQUAL:61,CHAR_EXCLAMATION_MARK:33,CHAR_FORM_FEED:12,CHAR_FORWARD_SLASH:47,CHAR_GRAVE_ACCENT:96,CHAR_HASH:35,CHAR_HYPHEN_MINUS:45,CHAR_LEFT_ANGLE_BRACKET:60,CHAR_LEFT_CURLY_BRACE:123,CHAR_LEFT_SQUARE_BRACKET:91,CHAR_LINE_FEED:10,CHAR_NO_BREAK_SPACE:160,CHAR_PERCENT:37,CHAR_PLUS:43,CHAR_QUESTION_MARK:63,CHAR_RIGHT_ANGLE_BRACKET:62,CHAR_RIGHT_CURLY_BRACE:125,CHAR_RIGHT_SQUARE_BRACKET:93,CHAR_SEMICOLON:59,CHAR_SINGLE_QUOTE:39,CHAR_SPACE:32,CHAR_TAB:9,CHAR_UNDERSCORE:95,CHAR_VERTICAL_LINE:124,CHAR_ZERO_WIDTH_NOBREAK_SPACE:65279,SEP:zi.sep,extglobChars(t){return{"!":{type:"negate",open:"(?:(?!(?:",close:`))${t.STAR})`},"?":{type:"qmark",open:"(?:",close:")?"},"+":{type:"plus",open:"(?:",close:")+"},"*":{type:"star",open:"(?:",close:")*"},"@":{type:"at",open:"(?:",close:")"}}},globChars(t){return t===!0?so:hs}};(function(t){const e=z,u=process.platform==="win32",{REGEX_BACKSLASH:s,REGEX_REMOVE_BACKSLASH:n,REGEX_SPECIAL_CHARS:r,REGEX_SPECIAL_CHARS_GLOBAL:i}=Xe;t.isObject=o=>o!==null&&typeof o=="object"&&!Array.isArray(o),t.hasRegexChars=o=>r.test(o),t.isRegexChar=o=>o.length===1&&t.hasRegexChars(o),t.escapeRegex=o=>o.replace(i,"\\$1"),t.toPosixSlashes=o=>o.replace(s,"/"),t.removeBackslashes=o=>o.replace(n,D=>D==="\\"?"":D),t.supportsLookbehinds=()=>{const o=process.version.slice(1).split(".").map(Number);return o.length===3&&o[0]>=9||o[0]===8&&o[1]>=10},t.isWindows=o=>o&&typeof o.windows=="boolean"?o.windows:u===!0||e.sep==="\\",t.escapeLast=(o,D,c)=>{const f=o.lastIndexOf(D,c);return f===-1?o:o[f-1]==="\\"?t.escapeLast(o,D,f-1):`${o.slice(0,f)}\\${o.slice(f)}`},t.removePrefix=(o,D={})=>{let c=o;return c.startsWith("./")&&(c=c.slice(2),D.prefix="./"),c},t.wrapOutput=(o,D={},c={})=>{const f=c.contains?"":"^",h=c.contains?"":"$";let l=`${f}(?:${o})${h}`;return D.negated===!0&&(l=`(?:^(?!${l}).*$)`),l}})(Ye);const ds=Ye,{CHAR_ASTERISK:It,CHAR_AT:ro,CHAR_BACKWARD_SLASH:we,CHAR_COMMA:io,CHAR_DOT:Pt,CHAR_EXCLAMATION_MARK:kt,CHAR_FORWARD_SLASH:Es,CHAR_LEFT_CURLY_BRACE:Mt,CHAR_LEFT_PARENTHESES:Gt,CHAR_LEFT_SQUARE_BRACKET:oo,CHAR_PLUS:Do,CHAR_QUESTION_MARK:ps,CHAR_RIGHT_CURLY_BRACE:ao,CHAR_RIGHT_PARENTHESES:Cs,CHAR_RIGHT_SQUARE_BRACKET:lo}=Xe,Fs=a(t=>t===Es||t===we,"isPathSeparator"),gs=a(t=>{t.isPrefix!==!0&&(t.depth=t.isGlobstar?1/0:1)},"depth"),co=a((t,e)=>{const u=e||{},s=t.length-1,n=u.parts===!0||u.scanToEnd===!0,r=[],i=[],o=[];let D=t,c=-1,f=0,h=0,l=!1,p=!1,C=!1,g=!1,y=!1,B=!1,H=!1,$=!1,Q=!1,G=!1,ne=0,W,A,v={value:"",depth:0,isGlob:!1};const M=a(()=>c>=s,"eos"),F=a(()=>D.charCodeAt(c+1),"peek"),O=a(()=>(W=A,D.charCodeAt(++c)),"advance");for(;c<s;){A=O();let j;if(A===we){H=v.backslashes=!0,A=O(),A===Mt&&(B=!0);continue}if(B===!0||A===Mt){for(ne++;M()!==!0&&(A=O());){if(A===we){H=v.backslashes=!0,O();continue}if(A===Mt){ne++;continue}if(B!==!0&&A===Pt&&(A=O())===Pt){if(l=v.isBrace=!0,C=v.isGlob=!0,G=!0,n===!0)continue;break}if(B!==!0&&A===io){if(l=v.isBrace=!0,C=v.isGlob=!0,G=!0,n===!0)continue;break}if(A===ao&&(ne--,ne===0)){B=!1,l=v.isBrace=!0,G=!0;break}}if(n===!0)continue;break}if(A===Es){if(r.push(c),i.push(v),v={value:"",depth:0,isGlob:!1},G===!0)continue;if(W===Pt&&c===f+1){f+=2;continue}h=c+1;continue}if(u.noext!==!0&&(A===Do||A===ro||A===It||A===ps||A===kt)===!0&&F()===Gt){if(C=v.isGlob=!0,g=v.isExtglob=!0,G=!0,A===kt&&c===f&&(Q=!0),n===!0){for(;M()!==!0&&(A=O());){if(A===we){H=v.backslashes=!0,A=O();continue}if(A===Cs){C=v.isGlob=!0,G=!0;break}}continue}break}if(A===It){if(W===It&&(y=v.isGlobstar=!0),C=v.isGlob=!0,G=!0,n===!0)continue;break}if(A===ps){if(C=v.isGlob=!0,G=!0,n===!0)continue;break}if(A===oo){for(;M()!==!0&&(j=O());){if(j===we){H=v.backslashes=!0,O();continue}if(j===lo){p=v.isBracket=!0,C=v.isGlob=!0,G=!0;break}}if(n===!0)continue;break}if(u.nonegate!==!0&&A===kt&&c===f){$=v.negated=!0,f++;continue}if(u.noparen!==!0&&A===Gt){if(C=v.isGlob=!0,n===!0){for(;M()!==!0&&(A=O());){if(A===Gt){H=v.backslashes=!0,A=O();continue}if(A===Cs){G=!0;break}}continue}break}if(C===!0){if(G=!0,n===!0)continue;break}}u.noext===!0&&(g=!1,C=!1);let T=D,re="",d="";f>0&&(re=D.slice(0,f),D=D.slice(f),h-=f),T&&C===!0&&h>0?(T=D.slice(0,h),d=D.slice(h)):C===!0?(T="",d=D):T=D,T&&T!==""&&T!=="/"&&T!==D&&Fs(T.charCodeAt(T.length-1))&&(T=T.slice(0,-1)),u.unescape===!0&&(d&&(d=ds.removeBackslashes(d)),T&&H===!0&&(T=ds.removeBackslashes(T)));const E={prefix:re,input:t,start:f,base:T,glob:d,isBrace:l,isBracket:p,isGlob:C,isExtglob:g,isGlobstar:y,negated:$,negatedExtglob:Q};if(u.tokens===!0&&(E.maxDepth=0,Fs(A)||i.push(v),E.tokens=i),u.parts===!0||u.tokens===!0){let j;for(let b=0;b<r.length;b++){const Z=j?j+1:f,J=r[b],V=t.slice(Z,J);u.tokens&&(b===0&&f!==0?(i[b].isPrefix=!0,i[b].value=re):i[b].value=V,gs(i[b]),E.maxDepth+=i[b].depth),(b!==0||V!=="")&&o.push(V),j=J}if(j&&j+1<t.length){const b=t.slice(j+1);o.push(b),u.tokens&&(i[i.length-1].value=b,gs(i[i.length-1]),E.maxDepth+=i[i.length-1].depth)}E.slashes=r,E.parts=o}return E},"scan$1");var fo=co;const Qe=Xe,Y=Ye,{MAX_LENGTH:Ze,POSIX_REGEX_SOURCE:ho,REGEX_NON_SPECIAL_CHARS:Eo,REGEX_SPECIAL_CHARS_BACKREF:po,REPLACEMENTS:ms}=Qe,Co=a((t,e)=>{if(typeof e.expandRange=="function")return e.expandRange(...t,e);t.sort();const u=`[${t.join("-")}]`;try{new RegExp(u)}catch{return t.map(n=>Y.escapeRegex(n)).join("..")}return u},"expandRange"),Ee=a((t,e)=>`Missing ${t}: "${e}" - use "\\\\${e}" to match literal characters`,"syntaxError"),Wt=a((t,e)=>{if(typeof t!="string")throw new TypeError("Expected a string");t=ms[t]||t;const u={...e},s=typeof u.maxLength=="number"?Math.min(Ze,u.maxLength):Ze;let n=t.length;if(n>s)throw new SyntaxError(`Input length: ${n}, exceeds maximum allowed length: ${s}`);const r={type:"bos",value:"",output:u.prepend||""},i=[r],o=u.capture?"":"?:",D=Y.isWindows(e),c=Qe.globChars(D),f=Qe.extglobChars(c),{DOT_LITERAL:h,PLUS_LITERAL:l,SLASH_LITERAL:p,ONE_CHAR:C,DOTS_SLASH:g,NO_DOT:y,NO_DOT_SLASH:B,NO_DOTS_SLASH:H,QMARK:$,QMARK_NO_DOT:Q,STAR:G,START_ANCHOR:ne}=c,W=a(_=>`(${o}(?:(?!${ne}${_.dot?g:h}).)*?)`,"globstar"),A=u.dot?"":y,v=u.dot?$:Q;let M=u.bash===!0?W(u):G;u.capture&&(M=`(${M})`),typeof u.noext=="boolean"&&(u.noextglob=u.noext);const F={input:t,index:-1,start:0,dot:u.dot===!0,consumed:"",output:"",prefix:"",backtrack:!1,negated:!1,brackets:0,braces:0,parens:0,quotes:0,globstar:!1,tokens:i};t=Y.removePrefix(t,F),n=t.length;const O=[],T=[],re=[];let d=r,E;const j=a(()=>F.index===n-1,"eos"),b=F.peek=(_=1)=>t[F.index+_],Z=F.advance=()=>t[++F.index]||"",J=a(()=>t.slice(F.index+1),"remaining"),V=a((_="",x=0)=>{F.consumed+=_,F.index+=x},"consume"),Pe=a(_=>{F.output+=_.output!=null?_.output:_.value,V(_.value)},"append"),Sn=a(()=>{let _=1;for(;b()==="!"&&(b(2)!=="("||b(3)==="?");)Z(),F.start++,_++;return _%2===0?!1:(F.negated=!0,F.start++,!0)},"negate"),ke=a(_=>{F[_]++,re.push(_)},"increment"),ie=a(_=>{F[_]--,re.pop()},"decrement"),R=a(_=>{if(d.type==="globstar"){const x=F.braces>0&&(_.type==="comma"||_.type==="brace"),m=_.extglob===!0||O.length&&(_.type==="pipe"||_.type==="paren");_.type!=="slash"&&_.type!=="paren"&&!x&&!m&&(F.output=F.output.slice(0,-d.output.length),d.type="star",d.value="*",d.output=M,F.output+=d.output)}if(O.length&&_.type!=="paren"&&(O[O.length-1].inner+=_.value),(_.value||_.output)&&Pe(_),d&&d.type==="text"&&_.type==="text"){d.value+=_.value,d.output=(d.output||"")+_.value;return}_.prev=d,i.push(_),d=_},"push"),Me=a((_,x)=>{const m={...f[x],conditions:1,inner:""};m.prev=d,m.parens=F.parens,m.output=F.output;const w=(u.capture?"(":"")+m.open;ke("parens"),R({type:_,value:x,output:F.output?"":C}),R({type:"paren",extglob:!0,value:Z(),output:w}),O.push(m)},"extglobOpen"),Bn=a(_=>{let x=_.close+(u.capture?")":""),m;if(_.type==="negate"){let w=M;if(_.inner&&_.inner.length>1&&_.inner.includes("/")&&(w=W(u)),(w!==M||j()||/^\)+$/.test(J()))&&(x=_.close=`)$))${w}`),_.inner.includes("*")&&(m=J())&&/^\.[^\\/.]+$/.test(m)){const N=Wt(m,{...e,fastpaths:!1}).output;x=_.close=`)${N})${w})`}_.prev.type==="bos"&&(F.negatedExtglob=!0)}R({type:"paren",extglob:!0,value:E,output:x}),ie("parens")},"extglobClose");if(u.fastpaths!==!1&&!/(^[*!]|[/()[\]{}"])/.test(t)){let _=!1,x=t.replace(po,(m,w,N,U,I,ht)=>U==="\\"?(_=!0,m):U==="?"?w?w+U+(I?$.repeat(I.length):""):ht===0?v+(I?$.repeat(I.length):""):$.repeat(N.length):U==="."?h.repeat(N.length):U==="*"?w?w+U+(I?M:""):M:w?m:`\\${m}`);return _===!0&&(u.unescape===!0?x=x.replace(/\\/g,""):x=x.replace(/\\+/g,m=>m.length%2===0?"\\\\":m?"\\":"")),x===t&&u.contains===!0?(F.output=t,F):(F.output=Y.wrapOutput(x,F,e),F)}for(;!j();){if(E=Z(),E==="\0")continue;if(E==="\\"){const m=b();if(m==="/"&&u.bash!==!0||m==="."||m===";")continue;if(!m){E+="\\",R({type:"text",value:E});continue}const w=/^\\+/.exec(J());let N=0;if(w&&w[0].length>2&&(N=w[0].length,F.index+=N,N%2!==0&&(E+="\\")),u.unescape===!0?E=Z():E+=Z(),F.brackets===0){R({type:"text",value:E});continue}}if(F.brackets>0&&(E!=="]"||d.value==="["||d.value==="[^")){if(u.posix!==!1&&E===":"){const m=d.value.slice(1);if(m.includes("[")&&(d.posix=!0,m.includes(":"))){const w=d.value.lastIndexOf("["),N=d.value.slice(0,w),U=d.value.slice(w+2),I=ho[U];if(I){d.value=N+I,F.backtrack=!0,Z(),!r.output&&i.indexOf(d)===1&&(r.output=C);continue}}}(E==="["&&b()!==":"||E==="-"&&b()==="]")&&(E=`\\${E}`),E==="]"&&(d.value==="["||d.value==="[^")&&(E=`\\${E}`),u.posix===!0&&E==="!"&&d.value==="["&&(E="^"),d.value+=E,Pe({value:E});continue}if(F.quotes===1&&E!=='"'){E=Y.escapeRegex(E),d.value+=E,Pe({value:E});continue}if(E==='"'){F.quotes=F.quotes===1?0:1,u.keepQuotes===!0&&R({type:"text",value:E});continue}if(E==="("){ke("parens"),R({type:"paren",value:E});continue}if(E===")"){if(F.parens===0&&u.strictBrackets===!0)throw new SyntaxError(Ee("opening","("));const m=O[O.length-1];if(m&&F.parens===m.parens+1){Bn(O.pop());continue}R({type:"paren",value:E,output:F.parens?")":"\\)"}),ie("parens");continue}if(E==="["){if(u.nobracket===!0||!J().includes("]")){if(u.nobracket!==!0&&u.strictBrackets===!0)throw new SyntaxError(Ee("closing","]"));E=`\\${E}`}else ke("brackets");R({type:"bracket",value:E});continue}if(E==="]"){if(u.nobracket===!0||d&&d.type==="bracket"&&d.value.length===1){R({type:"text",value:E,output:`\\${E}`});continue}if(F.brackets===0){if(u.strictBrackets===!0)throw new SyntaxError(Ee("opening","["));R({type:"text",value:E,output:`\\${E}`});continue}ie("brackets");const m=d.value.slice(1);if(d.posix!==!0&&m[0]==="^"&&!m.includes("/")&&(E=`/${E}`),d.value+=E,Pe({value:E}),u.literalBrackets===!1||Y.hasRegexChars(m))continue;const w=Y.escapeRegex(d.value);if(F.output=F.output.slice(0,-d.value.length),u.literalBrackets===!0){F.output+=w,d.value=w;continue}d.value=`(${o}${w}|${d.value})`,F.output+=d.value;continue}if(E==="{"&&u.nobrace!==!0){ke("braces");const m={type:"brace",value:E,output:"(",outputIndex:F.output.length,tokensIndex:F.tokens.length};T.push(m),R(m);continue}if(E==="}"){const m=T[T.length-1];if(u.nobrace===!0||!m){R({type:"text",value:E,output:E});continue}let w=")";if(m.dots===!0){const N=i.slice(),U=[];for(let I=N.length-1;I>=0&&(i.pop(),N[I].type!=="brace");I--)N[I].type!=="dots"&&U.unshift(N[I].value);w=Co(U,u),F.backtrack=!0}if(m.comma!==!0&&m.dots!==!0){const N=F.output.slice(0,m.outputIndex),U=F.tokens.slice(m.tokensIndex);m.value=m.output="\\{",E=w="\\}",F.output=N;for(const I of U)F.output+=I.output||I.value}R({type:"brace",value:E,output:w}),ie("braces"),T.pop();continue}if(E==="|"){O.length>0&&O[O.length-1].conditions++,R({type:"text",value:E});continue}if(E===","){let m=E;const w=T[T.length-1];w&&re[re.length-1]==="braces"&&(w.comma=!0,m="|"),R({type:"comma",value:E,output:m});continue}if(E==="/"){if(d.type==="dot"&&F.index===F.start+1){F.start=F.index+1,F.consumed="",F.output="",i.pop(),d=r;continue}R({type:"slash",value:E,output:p});continue}if(E==="."){if(F.braces>0&&d.type==="dot"){d.value==="."&&(d.output=h);const m=T[T.length-1];d.type="dots",d.output+=E,d.value+=E,m.dots=!0;continue}if(F.braces+F.parens===0&&d.type!=="bos"&&d.type!=="slash"){R({type:"text",value:E,output:h});continue}R({type:"dot",value:E,output:h});continue}if(E==="?"){if(!(d&&d.value==="(")&&u.noextglob!==!0&&b()==="("&&b(2)!=="?"){Me("qmark",E);continue}if(d&&d.type==="paren"){const w=b();let N=E;if(w==="<"&&!Y.supportsLookbehinds())throw new Error("Node.js v10 or higher is required for regex lookbehinds");(d.value==="("&&!/[!=<:]/.test(w)||w==="<"&&!/<([!=]|\w+>)/.test(J()))&&(N=`\\${E}`),R({type:"text",value:E,output:N});continue}if(u.dot!==!0&&(d.type==="slash"||d.type==="bos")){R({type:"qmark",value:E,output:Q});continue}R({type:"qmark",value:E,output:$});continue}if(E==="!"){if(u.noextglob!==!0&&b()==="("&&(b(2)!=="?"||!/[!=<:]/.test(b(3)))){Me("negate",E);continue}if(u.nonegate!==!0&&F.index===0){Sn();continue}}if(E==="+"){if(u.noextglob!==!0&&b()==="("&&b(2)!=="?"){Me("plus",E);continue}if(d&&d.value==="("||u.regex===!1){R({type:"plus",value:E,output:l});continue}if(d&&(d.type==="bracket"||d.type==="paren"||d.type==="brace")||F.parens>0){R({type:"plus",value:E});continue}R({type:"plus",value:l});continue}if(E==="@"){if(u.noextglob!==!0&&b()==="("&&b(2)!=="?"){R({type:"at",extglob:!0,value:E,output:""});continue}R({type:"text",value:E});continue}if(E!=="*"){(E==="$"||E==="^")&&(E=`\\${E}`);const m=Eo.exec(J());m&&(E+=m[0],F.index+=m[0].length),R({type:"text",value:E});continue}if(d&&(d.type==="globstar"||d.star===!0)){d.type="star",d.star=!0,d.value+=E,d.output=M,F.backtrack=!0,F.globstar=!0,V(E);continue}let _=J();if(u.noextglob!==!0&&/^\([^?]/.test(_)){Me("star",E);continue}if(d.type==="star"){if(u.noglobstar===!0){V(E);continue}const m=d.prev,w=m.prev,N=m.type==="slash"||m.type==="bos",U=w&&(w.type==="star"||w.type==="globstar");if(u.bash===!0&&(!N||_[0]&&_[0]!=="/")){R({type:"star",value:E,output:""});continue}const I=F.braces>0&&(m.type==="comma"||m.type==="brace"),ht=O.length&&(m.type==="pipe"||m.type==="paren");if(!N&&m.type!=="paren"&&!I&&!ht){R({type:"star",value:E,output:""});continue}for(;_.slice(0,3)==="/**";){const Ge=t[F.index+4];if(Ge&&Ge!=="/")break;_=_.slice(3),V("/**",3)}if(m.type==="bos"&&j()){d.type="globstar",d.value+=E,d.output=W(u),F.output=d.output,F.globstar=!0,V(E);continue}if(m.type==="slash"&&m.prev.type!=="bos"&&!U&&j()){F.output=F.output.slice(0,-(m.output+d.output).length),m.output=`(?:${m.output}`,d.type="globstar",d.output=W(u)+(u.strictSlashes?")":"|$)"),d.value+=E,F.globstar=!0,F.output+=m.output+d.output,V(E);continue}if(m.type==="slash"&&m.prev.type!=="bos"&&_[0]==="/"){const Ge=_[1]!==void 0?"|$":"";F.output=F.output.slice(0,-(m.output+d.output).length),m.output=`(?:${m.output}`,d.type="globstar",d.output=`${W(u)}${p}|${p}${Ge})`,d.value+=E,F.output+=m.output+d.output,F.globstar=!0,V(E+Z()),R({type:"slash",value:"/",output:""});continue}if(m.type==="bos"&&_[0]==="/"){d.type="globstar",d.value+=E,d.output=`(?:^|${p}|${W(u)}${p})`,F.output=d.output,F.globstar=!0,V(E+Z()),R({type:"slash",value:"/",output:""});continue}F.output=F.output.slice(0,-d.output.length),d.type="globstar",d.output=W(u),d.value+=E,F.output+=d.output,F.globstar=!0,V(E);continue}const x={type:"star",value:E,output:M};if(u.bash===!0){x.output=".*?",(d.type==="bos"||d.type==="slash")&&(x.output=A+x.output),R(x);continue}if(d&&(d.type==="bracket"||d.type==="paren")&&u.regex===!0){x.output=E,R(x);continue}(F.index===F.start||d.type==="slash"||d.type==="dot")&&(d.type==="dot"?(F.output+=B,d.output+=B):u.dot===!0?(F.output+=H,d.output+=H):(F.output+=A,d.output+=A),b()!=="*"&&(F.output+=C,d.output+=C)),R(x)}for(;F.brackets>0;){if(u.strictBrackets===!0)throw new SyntaxError(Ee("closing","]"));F.output=Y.escapeLast(F.output,"["),ie("brackets")}for(;F.parens>0;){if(u.strictBrackets===!0)throw new SyntaxError(Ee("closing",")"));F.output=Y.escapeLast(F.output,"("),ie("parens")}for(;F.braces>0;){if(u.strictBrackets===!0)throw new SyntaxError(Ee("closing","}"));F.output=Y.escapeLast(F.output,"{"),ie("braces")}if(u.strictSlashes!==!0&&(d.type==="star"||d.type==="bracket")&&R({type:"maybe_slash",value:"",output:`${p}?`}),F.backtrack===!0){F.output="";for(const _ of F.tokens)F.output+=_.output!=null?_.output:_.value,_.suffix&&(F.output+=_.suffix)}return F},"parse$3");Wt.fastpaths=(t,e)=>{const u={...e},s=typeof u.maxLength=="number"?Math.min(Ze,u.maxLength):Ze,n=t.length;if(n>s)throw new SyntaxError(`Input length: ${n}, exceeds maximum allowed length: ${s}`);t=ms[t]||t;const r=Y.isWindows(e),{DOT_LITERAL:i,SLASH_LITERAL:o,ONE_CHAR:D,DOTS_SLASH:c,NO_DOT:f,NO_DOTS:h,NO_DOTS_SLASH:l,STAR:p,START_ANCHOR:C}=Qe.globChars(r),g=u.dot?h:f,y=u.dot?l:f,B=u.capture?"":"?:",H={negated:!1,prefix:""};let $=u.bash===!0?".*?":p;u.capture&&($=`(${$})`);const Q=a(A=>A.noglobstar===!0?$:`(${B}(?:(?!${C}${A.dot?c:i}).)*?)`,"globstar"),G=a(A=>{switch(A){case"*":return`${g}${D}${$}`;case".*":return`${i}${D}${$}`;case"*.*":return`${g}${$}${i}${D}${$}`;case"*/*":return`${g}${$}${o}${D}${y}${$}`;case"**":return g+Q(u);case"**/*":return`(?:${g}${Q(u)}${o})?${y}${D}${$}`;case"**/*.*":return`(?:${g}${Q(u)}${o})?${y}${$}${i}${D}${$}`;case"**/.*":return`(?:${g}${Q(u)}${o})?${i}${D}${$}`;default:{const v=/^(.*?)\.(\w+)$/.exec(A);if(!v)return;const M=G(v[1]);return M?M+i+v[2]:void 0}}},"create"),ne=Y.removePrefix(t,H);let W=G(ne);return W&&u.strictSlashes!==!0&&(W+=`${o}?`),W};var Fo=Wt;const go=z,mo=fo,jt=Fo,Ut=Ye,_o=Xe,Ao=a(t=>t&&typeof t=="object"&&!Array.isArray(t),"isObject$1"),L=a((t,e,u=!1)=>{if(Array.isArray(t)){const f=t.map(l=>L(l,e,u));return a(l=>{for(const p of f){const C=p(l);if(C)return C}return!1},"arrayMatcher")}const s=Ao(t)&&t.tokens&&t.input;if(t===""||typeof t!="string"&&!s)throw new TypeError("Expected pattern to be a non-empty string");const n=e||{},r=Ut.isWindows(e),i=s?L.compileRe(t,e):L.makeRe(t,e,!1,!0),o=i.state;delete i.state;let D=a(()=>!1,"isIgnored");if(n.ignore){const f={...e,ignore:null,onMatch:null,onResult:null};D=L(n.ignore,f,u)}const c=a((f,h=!1)=>{const{isMatch:l,match:p,output:C}=L.test(f,i,e,{glob:t,posix:r}),g={glob:t,state:o,regex:i,posix:r,input:f,output:C,match:p,isMatch:l};return typeof n.onResult=="function"&&n.onResult(g),l===!1?(g.isMatch=!1,h?g:!1):D(f)?(typeof n.onIgnore=="function"&&n.onIgnore(g),g.isMatch=!1,h?g:!1):(typeof n.onMatch=="function"&&n.onMatch(g),h?g:!0)},"matcher");return u&&(c.state=o),c},"picomatch$3");L.test=(t,e,u,{glob:s,posix:n}={})=>{if(typeof t!="string")throw new TypeError("Expected input to be a string");if(t==="")return{isMatch:!1,output:""};const r=u||{},i=r.format||(n?Ut.toPosixSlashes:null);let o=t===s,D=o&&i?i(t):t;return o===!1&&(D=i?i(t):t,o=D===s),(o===!1||r.capture===!0)&&(r.matchBase===!0||r.basename===!0?o=L.matchBase(t,e,u,n):o=e.exec(D)),{isMatch:!!o,match:o,output:D}},L.matchBase=(t,e,u,s=Ut.isWindows(u))=>(e instanceof RegExp?e:L.makeRe(e,u)).test(go.basename(t)),L.isMatch=(t,e,u)=>L(e,u)(t),L.parse=(t,e)=>Array.isArray(t)?t.map(u=>L.parse(u,e)):jt(t,{...e,fastpaths:!1}),L.scan=(t,e)=>mo(t,e),L.compileRe=(t,e,u=!1,s=!1)=>{if(u===!0)return t.output;const n=e||{},r=n.contains?"":"^",i=n.contains?"":"$";let o=`${r}(?:${t.output})${i}`;t&&t.negated===!0&&(o=`^(?!${o}).*$`);const D=L.toRegex(o,e);return s===!0&&(D.state=t),D},L.makeRe=(t,e={},u=!1,s=!1)=>{if(!t||typeof t!="string")throw new TypeError("Expected a non-empty string");let n={negated:!1,fastpaths:!0};return e.fastpaths!==!1&&(t[0]==="."||t[0]==="*")&&(n.output=jt.fastpaths(t,e)),n.output||(n=jt(t,e)),L.compileRe(n,e,u,s)},L.toRegex=(t,e)=>{try{const u=e||{};return new RegExp(t,u.flags||(u.nocase?"i":""))}catch(u){if(e&&e.debug===!0)throw u;return/$^/}},L.constants=_o;var yo=L,_s=yo;const Re=oe,{Readable:wo}=jn,be=z,{promisify:Je}=_e,Kt=_s,Ro=Je(Re.readdir),bo=Je(Re.stat),As=Je(Re.lstat),vo=Je(Re.realpath),So="!",ys="READDIRP_RECURSIVE_ERROR",Bo=new Set(["ENOENT","EPERM","EACCES","ELOOP",ys]),Vt="files",ws="directories",et="files_directories",tt="all",Rs=[Vt,ws,et,tt],$o=a(t=>Bo.has(t.code),"isNormalFlowError"),[bs,To]=process.versions.node.split(".").slice(0,2).map(t=>Number.parseInt(t,10)),xo=process.platform==="win32"&&(bs>10||bs===10&&To>=5),vs=a(t=>{if(t!==void 0){if(typeof t=="function")return t;if(typeof t=="string"){const e=Kt(t.trim());return u=>e(u.basename)}if(Array.isArray(t)){const e=[],u=[];for(const s of t){const n=s.trim();n.charAt(0)===So?u.push(Kt(n.slice(1))):e.push(Kt(n))}return u.length>0?e.length>0?s=>e.some(n=>n(s.basename))&&!u.some(n=>n(s.basename)):s=>!u.some(n=>n(s.basename)):s=>e.some(n=>n(s.basename))}}},"normalizeFilter");class ft extends wo{static{a(this,"ReaddirpStream")}static get defaultOptions(){return{root:".",fileFilter:a(e=>!0,"fileFilter"),directoryFilter:a(e=>!0,"directoryFilter"),type:Vt,lstat:!1,depth:2147483648,alwaysStat:!1}}constructor(e={}){super({objectMode:!0,autoDestroy:!0,highWaterMark:e.highWaterMark||4096});const u={...ft.defaultOptions,...e},{root:s,type:n}=u;this._fileFilter=vs(u.fileFilter),this._directoryFilter=vs(u.directoryFilter);const r=u.lstat?As:bo;xo?this._stat=i=>r(i,{bigint:!0}):this._stat=r,this._maxDepth=u.depth,this._wantsDir=[ws,et,tt].includes(n),this._wantsFile=[Vt,et,tt].includes(n),this._wantsEverything=n===tt,this._root=be.resolve(s),this._isDirent="Dirent"in Re&&!u.alwaysStat,this._statsProp=this._isDirent?"dirent":"stats",this._rdOptions={encoding:"utf8",withFileTypes:this._isDirent},this.parents=[this._exploreDir(s,1)],this.reading=!1,this.parent=void 0}async _read(e){if(!this.reading){this.reading=!0;try{for(;!this.destroyed&&e>0;){const{path:u,depth:s,files:n=[]}=this.parent||{};if(n.length>0){const r=n.splice(0,e).map(i=>this._formatEntry(i,u));for(const i of await Promise.all(r)){if(this.destroyed)return;const o=await this._getEntryType(i);o==="directory"&&this._directoryFilter(i)?(s<=this._maxDepth&&this.parents.push(this._exploreDir(i.fullPath,s+1)),this._wantsDir&&(this.push(i),e--)):(o==="file"||this._includeAsFile(i))&&this._fileFilter(i)&&this._wantsFile&&(this.push(i),e--)}}else{const r=this.parents.pop();if(!r){this.push(null);break}if(this.parent=await r,this.destroyed)return}}}catch(u){this.destroy(u)}finally{this.reading=!1}}}async _exploreDir(e,u){let s;try{s=await Ro(e,this._rdOptions)}catch(n){this._onError(n)}return{files:s,depth:u,path:e}}async _formatEntry(e,u){let s;try{const n=this._isDirent?e.name:e,r=be.resolve(be.join(u,n));s={path:be.relative(this._root,r),fullPath:r,basename:n},s[this._statsProp]=this._isDirent?e:await this._stat(r)}catch(n){this._onError(n)}return s}_onError(e){$o(e)&&!this.destroyed?this.emit("warn",e):this.destroy(e)}async _getEntryType(e){const u=e&&e[this._statsProp];if(u){if(u.isFile())return"file";if(u.isDirectory())return"directory";if(u&&u.isSymbolicLink()){const s=e.fullPath;try{const n=await vo(s),r=await As(n);if(r.isFile())return"file";if(r.isDirectory()){const i=n.length;if(s.startsWith(n)&&s.substr(i,1)===be.sep){const o=new Error(`Circular symlink detected: "${s}" points to "${n}"`);return o.code=ys,this._onError(o)}return"directory"}}catch(n){this._onError(n)}}}}_includeAsFile(e){const u=e&&e[this._statsProp];return u&&this._wantsEverything&&!u.isDirectory()}}const pe=a((t,e={})=>{let u=e.entryType||e.type;if(u==="both"&&(u=et),u&&(e.type=u),t){if(typeof t!="string")throw new TypeError("readdirp: root argument must be a string. Usage: readdirp(root, options)");if(u&&!Rs.includes(u))throw new Error(`readdirp: Invalid type passed. Use one of ${Rs.join(", ")}`)}else throw new Error("readdirp: root argument is required. Usage: readdirp(root, options)");return e.root=t,new ft(e)},"readdirp$1"),Oo=a((t,e={})=>new Promise((u,s)=>{const n=[];pe(t,e).on("data",r=>n.push(r)).on("end",()=>u(n)).on("error",r=>s(r))}),"readdirpPromise");pe.promise=Oo,pe.ReaddirpStream=ft,pe.default=pe;var No=pe,zt={exports:{}};/*!
 * normalize-path <https://github.com/jonschlinkert/normalize-path>
 *
 * Copyright (c) 2014-2018, Jon Schlinkert.
 * Released under the MIT License.
 */var Ss=a(function(t,e){if(typeof t!="string")throw new TypeError("expected path to be a string");if(t==="\\"||t==="/")return"/";var u=t.length;if(u<=1)return t;var s="";if(u>4&&t[3]==="\\"){var n=t[2];(n==="?"||n===".")&&t.slice(0,2)==="\\\\"&&(t=t.slice(2),s="//")}var r=t.split(/[/\\]+/);return e!==!1&&r[r.length-1]===""&&r.pop(),s+r.join("/")},"normalizePath$2"),Ho=zt.exports;Object.defineProperty(Ho,"__esModule",{value:!0});const Bs=_s,Lo=Ss,$s="!",Io={returnIndex:!1},Po=a(t=>Array.isArray(t)?t:[t],"arrify$1"),ko=a((t,e)=>{if(typeof t=="function")return t;if(typeof t=="string"){const u=Bs(t,e);return s=>t===s||u(s)}return t instanceof RegExp?u=>t.test(u):u=>!1},"createPattern"),Ts=a((t,e,u,s)=>{const n=Array.isArray(u),r=n?u[0]:u;if(!n&&typeof r!="string")throw new TypeError("anymatch: second argument must be a string: got "+Object.prototype.toString.call(r));const i=Lo(r,!1);for(let D=0;D<e.length;D++){const c=e[D];if(c(i))return s?-1:!1}const o=n&&[i].concat(u.slice(1));for(let D=0;D<t.length;D++){const c=t[D];if(n?c(...o):c(i))return s?D:!0}return s?-1:!1},"matchPatterns"),Yt=a((t,e,u=Io)=>{if(t==null)throw new TypeError("anymatch: specify first argument");const s=typeof u=="boolean"?{returnIndex:u}:u,n=s.returnIndex||!1,r=Po(t),i=r.filter(D=>typeof D=="string"&&D.charAt(0)===$s).map(D=>D.slice(1)).map(D=>Bs(D,s)),o=r.filter(D=>typeof D!="string"||typeof D=="string"&&D.charAt(0)!==$s).map(D=>ko(D,s));return e==null?(D,c=!1)=>Ts(o,i,D,typeof c=="boolean"?c:!1):Ts(o,i,e,n)},"anymatch$1");Yt.default=Yt,zt.exports=Yt;var Mo=zt.exports;/*!
 * is-extglob <https://github.com/jonschlinkert/is-extglob>
 *
 * Copyright (c) 2014-2016, Jon Schlinkert.
 * Licensed under the MIT License.
 */var Go=a(function(e){if(typeof e!="string"||e==="")return!1;for(var u;u=/(\\).|([@?!+*]\(.*\))/g.exec(e);){if(u[2])return!0;e=e.slice(u.index+u[0].length)}return!1},"isExtglob");/*!
 * is-glob <https://github.com/jonschlinkert/is-glob>
 *
 * Copyright (c) 2014-2017, Jon Schlinkert.
 * Released under the MIT License.
 */var Wo=Go,xs={"{":"}","(":")","[":"]"},jo=a(function(t){if(t[0]==="!")return!0;for(var e=0,u=-2,s=-2,n=-2,r=-2,i=-2;e<t.length;){if(t[e]==="*"||t[e+1]==="?"&&/[\].+)]/.test(t[e])||s!==-1&&t[e]==="["&&t[e+1]!=="]"&&(s<e&&(s=t.indexOf("]",e)),s>e&&(i===-1||i>s||(i=t.indexOf("\\",e),i===-1||i>s)))||n!==-1&&t[e]==="{"&&t[e+1]!=="}"&&(n=t.indexOf("}",e),n>e&&(i=t.indexOf("\\",e),i===-1||i>n))||r!==-1&&t[e]==="("&&t[e+1]==="?"&&/[:!=]/.test(t[e+2])&&t[e+3]!==")"&&(r=t.indexOf(")",e),r>e&&(i=t.indexOf("\\",e),i===-1||i>r))||u!==-1&&t[e]==="("&&t[e+1]!=="|"&&(u<e&&(u=t.indexOf("|",e)),u!==-1&&t[u+1]!==")"&&(r=t.indexOf(")",u),r>u&&(i=t.indexOf("\\",u),i===-1||i>r))))return!0;if(t[e]==="\\"){var o=t[e+1];e+=2;var D=xs[o];if(D){var c=t.indexOf(D,e);c!==-1&&(e=c+1)}if(t[e]==="!")return!0}else e++}return!1},"strictCheck"),Uo=a(function(t){if(t[0]==="!")return!0;for(var e=0;e<t.length;){if(/[*?{}()[\]]/.test(t[e]))return!0;if(t[e]==="\\"){var u=t[e+1];e+=2;var s=xs[u];if(s){var n=t.indexOf(s,e);n!==-1&&(e=n+1)}if(t[e]==="!")return!0}else e++}return!1},"relaxedCheck"),Os=a(function(e,u){if(typeof e!="string"||e==="")return!1;if(Wo(e))return!0;var s=jo;return u&&u.strict===!1&&(s=Uo),s(e)},"isGlob"),Ko=Os,Vo=z.posix.dirname,zo=Ru.platform()==="win32",qt="/",Yo=/\\/g,qo=/[\{\[].*[\}\]]$/,Xo=/(^|[^\\])([\{\[]|\([^\)]+$)/,Qo=/\\([\!\*\?\|\[\]\(\)\{\}])/g,Zo=a(function(e,u){var s=Object.assign({flipBackslashes:!0},u);s.flipBackslashes&&zo&&e.indexOf(qt)<0&&(e=e.replace(Yo,qt)),qo.test(e)&&(e+=qt),e+="a";do e=Vo(e);while(Ko(e)||Xo.test(e));return e.replace(Qo,"$1")},"globParent"),ut={};(function(t){t.isInteger=e=>typeof e=="number"?Number.isInteger(e):typeof e=="string"&&e.trim()!==""?Number.isInteger(Number(e)):!1,t.find=(e,u)=>e.nodes.find(s=>s.type===u),t.exceedsLimit=(e,u,s=1,n)=>n===!1||!t.isInteger(e)||!t.isInteger(u)?!1:(Number(u)-Number(e))/Number(s)>=n,t.escapeNode=(e,u=0,s)=>{let n=e.nodes[u];n&&(s&&n.type===s||n.type==="open"||n.type==="close")&&n.escaped!==!0&&(n.value="\\"+n.value,n.escaped=!0)},t.encloseBrace=e=>e.type!=="brace"||e.commas>>0+e.ranges>>0?!1:(e.invalid=!0,!0),t.isInvalidBrace=e=>e.type!=="brace"?!1:e.invalid===!0||e.dollar?!0:!(e.commas>>0+e.ranges>>0)||e.open!==!0||e.close!==!0?(e.invalid=!0,!0):!1,t.isOpenOrClose=e=>e.type==="open"||e.type==="close"?!0:e.open===!0||e.close===!0,t.reduce=e=>e.reduce((u,s)=>(s.type==="text"&&u.push(s.value),s.type==="range"&&(s.type="text"),u),[]),t.flatten=(...e)=>{const u=[],s=a(n=>{for(let r=0;r<n.length;r++){let i=n[r];Array.isArray(i)?s(i):i!==void 0&&u.push(i)}return u},"flat");return s(e),u}})(ut);const Ns=ut;var Xt=a((t,e={})=>{let u=a((s,n={})=>{let r=e.escapeInvalid&&Ns.isInvalidBrace(n),i=s.invalid===!0&&e.escapeInvalid===!0,o="";if(s.value)return(r||i)&&Ns.isOpenOrClose(s)?"\\"+s.value:s.value;if(s.value)return s.value;if(s.nodes)for(let D of s.nodes)o+=u(D);return o},"stringify");return u(t)},"stringify$4");/*!
 * is-number <https://github.com/jonschlinkert/is-number>
 *
 * Copyright (c) 2014-present, Jon Schlinkert.
 * Released under the MIT License.
 */var Jo=a(function(t){return typeof t=="number"?t-t===0:typeof t=="string"&&t.trim()!==""?Number.isFinite?Number.isFinite(+t):isFinite(+t):!1},"isNumber$2");/*!
 * to-regex-range <https://github.com/micromatch/to-regex-range>
 *
 * Copyright (c) 2015-present, Jon Schlinkert.
 * Released under the MIT License.
 */const Hs=Jo,ae=a((t,e,u)=>{if(Hs(t)===!1)throw new TypeError("toRegexRange: expected the first argument to be a number");if(e===void 0||t===e)return String(t);if(Hs(e)===!1)throw new TypeError("toRegexRange: expected the second argument to be a number.");let s={relaxZeros:!0,...u};typeof s.strictZeros=="boolean"&&(s.relaxZeros=s.strictZeros===!1);let n=String(s.relaxZeros),r=String(s.shorthand),i=String(s.capture),o=String(s.wrap),D=t+":"+e+"="+n+r+i+o;if(ae.cache.hasOwnProperty(D))return ae.cache[D].result;let c=Math.min(t,e),f=Math.max(t,e);if(Math.abs(c-f)===1){let g=t+"|"+e;return s.capture?`(${g})`:s.wrap===!1?g:`(?:${g})`}let h=Gs(t)||Gs(e),l={min:t,max:e,a:c,b:f},p=[],C=[];if(h&&(l.isPadded=h,l.maxLen=String(l.max).length),c<0){let g=f<0?Math.abs(f):1;C=Ls(g,Math.abs(c),l,s),c=l.a=0}return f>=0&&(p=Ls(c,f,l,s)),l.negatives=C,l.positives=p,l.result=eD(C,p),s.capture===!0?l.result=`(${l.result})`:s.wrap!==!1&&p.length+C.length>1&&(l.result=`(?:${l.result})`),ae.cache[D]=l,l.result},"toRegexRange$1");function eD(t,e,u){let s=Qt(t,e,"-",!1)||[],n=Qt(e,t,"",!1)||[],r=Qt(t,e,"-?",!0)||[];return s.concat(r).concat(n).join("|")}a(eD,"collatePatterns");function tD(t,e){let u=1,s=1,n=Ps(t,u),r=new Set([e]);for(;t<=n&&n<=e;)r.add(n),u+=1,n=Ps(t,u);for(n=ks(e+1,s)-1;t<n&&n<=e;)r.add(n),s+=1,n=ks(e+1,s)-1;return r=[...r],r.sort(nD),r}a(tD,"splitToRanges");function uD(t,e,u){if(t===e)return{pattern:t,count:[],digits:0};let s=sD(t,e),n=s.length,r="",i=0;for(let o=0;o<n;o++){let[D,c]=s[o];D===c?r+=D:D!=="0"||c!=="9"?r+=rD(D,c):i++}return i&&(r+=u.shorthand===!0?"\\d":"[0-9]"),{pattern:r,count:[i],digits:n}}a(uD,"rangeToPattern");function Ls(t,e,u,s){let n=tD(t,e),r=[],i=t,o;for(let D=0;D<n.length;D++){let c=n[D],f=uD(String(i),String(c),s),h="";if(!u.isPadded&&o&&o.pattern===f.pattern){o.count.length>1&&o.count.pop(),o.count.push(f.count[0]),o.string=o.pattern+Ms(o.count),i=c+1;continue}u.isPadded&&(h=iD(c,u,s)),f.string=h+f.pattern+Ms(f.count),r.push(f),i=c+1,o=f}return r}a(Ls,"splitToPatterns");function Qt(t,e,u,s,n){let r=[];for(let i of t){let{string:o}=i;!s&&!Is(e,"string",o)&&r.push(u+o),s&&Is(e,"string",o)&&r.push(u+o)}return r}a(Qt,"filterPatterns");function sD(t,e){let u=[];for(let s=0;s<t.length;s++)u.push([t[s],e[s]]);return u}a(sD,"zip");function nD(t,e){return t>e?1:e>t?-1:0}a(nD,"compare");function Is(t,e,u){return t.some(s=>s[e]===u)}a(Is,"contains");function Ps(t,e){return Number(String(t).slice(0,-e)+"9".repeat(e))}a(Ps,"countNines");function ks(t,e){return t-t%Math.pow(10,e)}a(ks,"countZeros");function Ms(t){let[e=0,u=""]=t;return u||e>1?`{${e+(u?","+u:"")}}`:""}a(Ms,"toQuantifier");function rD(t,e,u){return`[${t}${e-t===1?"":"-"}${e}]`}a(rD,"toCharacterClass");function Gs(t){return/^-?(0+)\d/.test(t)}a(Gs,"hasPadding");function iD(t,e,u){if(!e.isPadded)return t;let s=Math.abs(e.maxLen-String(t).length),n=u.relaxZeros!==!1;switch(s){case 0:return"";case 1:return n?"0?":"0";case 2:return n?"0{0,2}":"00";default:return n?`0{0,${s}}`:`0{${s}}`}}a(iD,"padZeros"),ae.cache={},ae.clearCache=()=>ae.cache={};var oD=ae;/*!
 * fill-range <https://github.com/jonschlinkert/fill-range>
 *
 * Copyright (c) 2014-present, Jon Schlinkert.
 * Licensed under the MIT License.
 */const DD=_e,Ws=oD,js=a(t=>t!==null&&typeof t=="object"&&!Array.isArray(t),"isObject"),aD=a(t=>e=>t===!0?Number(e):String(e),"transform"),Zt=a(t=>typeof t=="number"||typeof t=="string"&&t!=="","isValidValue"),ve=a(t=>Number.isInteger(+t),"isNumber"),Jt=a(t=>{let e=`${t}`,u=-1;if(e[0]==="-"&&(e=e.slice(1)),e==="0")return!1;for(;e[++u]==="0";);return u>0},"zeros"),lD=a((t,e,u)=>typeof t=="string"||typeof e=="string"?!0:u.stringify===!0,"stringify$3"),cD=a((t,e,u)=>{if(e>0){let s=t[0]==="-"?"-":"";s&&(t=t.slice(1)),t=s+t.padStart(s?e-1:e,"0")}return u===!1?String(t):t},"pad"),Us=a((t,e)=>{let u=t[0]==="-"?"-":"";for(u&&(t=t.slice(1),e--);t.length<e;)t="0"+t;return u?"-"+t:t},"toMaxLen"),fD=a((t,e)=>{t.negatives.sort((i,o)=>i<o?-1:i>o?1:0),t.positives.sort((i,o)=>i<o?-1:i>o?1:0);let u=e.capture?"":"?:",s="",n="",r;return t.positives.length&&(s=t.positives.join("|")),t.negatives.length&&(n=`-(${u}${t.negatives.join("|")})`),s&&n?r=`${s}|${n}`:r=s||n,e.wrap?`(${u}${r})`:r},"toSequence"),Ks=a((t,e,u,s)=>{if(u)return Ws(t,e,{wrap:!1,...s});let n=String.fromCharCode(t);if(t===e)return n;let r=String.fromCharCode(e);return`[${n}-${r}]`},"toRange"),Vs=a((t,e,u)=>{if(Array.isArray(t)){let s=u.wrap===!0,n=u.capture?"":"?:";return s?`(${n}${t.join("|")})`:t.join("|")}return Ws(t,e,u)},"toRegex"),zs=a((...t)=>new RangeError("Invalid range arguments: "+DD.inspect(...t)),"rangeError"),Ys=a((t,e,u)=>{if(u.strictRanges===!0)throw zs([t,e]);return[]},"invalidRange"),hD=a((t,e)=>{if(e.strictRanges===!0)throw new TypeError(`Expected step "${t}" to be a number`);return[]},"invalidStep"),dD=a((t,e,u=1,s={})=>{let n=Number(t),r=Number(e);if(!Number.isInteger(n)||!Number.isInteger(r)){if(s.strictRanges===!0)throw zs([t,e]);return[]}n===0&&(n=0),r===0&&(r=0);let i=n>r,o=String(t),D=String(e),c=String(u);u=Math.max(Math.abs(u),1);let f=Jt(o)||Jt(D)||Jt(c),h=f?Math.max(o.length,D.length,c.length):0,l=f===!1&&lD(t,e,s)===!1,p=s.transform||aD(l);if(s.toRegex&&u===1)return Ks(Us(t,h),Us(e,h),!0,s);let C={negatives:[],positives:[]},g=a(H=>C[H<0?"negatives":"positives"].push(Math.abs(H)),"push"),y=[],B=0;for(;i?n>=r:n<=r;)s.toRegex===!0&&u>1?g(n):y.push(cD(p(n,B),h,l)),n=i?n-u:n+u,B++;return s.toRegex===!0?u>1?fD(C,s):Vs(y,null,{wrap:!1,...s}):y},"fillNumbers"),ED=a((t,e,u=1,s={})=>{if(!ve(t)&&t.length>1||!ve(e)&&e.length>1)return Ys(t,e,s);let n=s.transform||(l=>String.fromCharCode(l)),r=`${t}`.charCodeAt(0),i=`${e}`.charCodeAt(0),o=r>i,D=Math.min(r,i),c=Math.max(r,i);if(s.toRegex&&u===1)return Ks(D,c,!1,s);let f=[],h=0;for(;o?r>=i:r<=i;)f.push(n(r,h)),r=o?r-u:r+u,h++;return s.toRegex===!0?Vs(f,null,{wrap:!1,options:s}):f},"fillLetters"),st=a((t,e,u,s={})=>{if(e==null&&Zt(t))return[t];if(!Zt(t)||!Zt(e))return Ys(t,e,s);if(typeof u=="function")return st(t,e,1,{transform:u});if(js(u))return st(t,e,0,u);let n={...s};return n.capture===!0&&(n.wrap=!0),u=u||n.step||1,ve(u)?ve(t)&&ve(e)?dD(t,e,u,n):ED(t,e,Math.max(Math.abs(u),1),n):u!=null&&!js(u)?hD(u,n):st(t,e,1,u)},"fill$2");var qs=st;const pD=qs,Xs=ut,CD=a((t,e={})=>{let u=a((s,n={})=>{let r=Xs.isInvalidBrace(n),i=s.invalid===!0&&e.escapeInvalid===!0,o=r===!0||i===!0,D=e.escapeInvalid===!0?"\\":"",c="";if(s.isOpen===!0||s.isClose===!0)return D+s.value;if(s.type==="open")return o?D+s.value:"(";if(s.type==="close")return o?D+s.value:")";if(s.type==="comma")return s.prev.type==="comma"?"":o?s.value:"|";if(s.value)return s.value;if(s.nodes&&s.ranges>0){let f=Xs.reduce(s.nodes),h=pD(...f,{...e,wrap:!1,toRegex:!0});if(h.length!==0)return f.length>1&&h.length>1?`(${h})`:h}if(s.nodes)for(let f of s.nodes)c+=u(f,s);return c},"walk");return u(t)},"compile$1");var FD=CD;const gD=qs,Qs=Xt,Ce=ut,le=a((t="",e="",u=!1)=>{let s=[];if(t=[].concat(t),e=[].concat(e),!e.length)return t;if(!t.length)return u?Ce.flatten(e).map(n=>`{${n}}`):e;for(let n of t)if(Array.isArray(n))for(let r of n)s.push(le(r,e,u));else for(let r of e)u===!0&&typeof r=="string"&&(r=`{${r}}`),s.push(Array.isArray(r)?le(n,r,u):n+r);return Ce.flatten(s)},"append"),mD=a((t,e={})=>{let u=e.rangeLimit===void 0?1e3:e.rangeLimit,s=a((n,r={})=>{n.queue=[];let i=r,o=r.queue;for(;i.type!=="brace"&&i.type!=="root"&&i.parent;)i=i.parent,o=i.queue;if(n.invalid||n.dollar){o.push(le(o.pop(),Qs(n,e)));return}if(n.type==="brace"&&n.invalid!==!0&&n.nodes.length===2){o.push(le(o.pop(),["{}"]));return}if(n.nodes&&n.ranges>0){let h=Ce.reduce(n.nodes);if(Ce.exceedsLimit(...h,e.step,u))throw new RangeError("expanded array length exceeds range limit. Use options.rangeLimit to increase or disable the limit.");let l=gD(...h,e);l.length===0&&(l=Qs(n,e)),o.push(le(o.pop(),l)),n.nodes=[];return}let D=Ce.encloseBrace(n),c=n.queue,f=n;for(;f.type!=="brace"&&f.type!=="root"&&f.parent;)f=f.parent,c=f.queue;for(let h=0;h<n.nodes.length;h++){let l=n.nodes[h];if(l.type==="comma"&&n.type==="brace"){h===1&&c.push(""),c.push("");continue}if(l.type==="close"){o.push(le(o.pop(),c,D));continue}if(l.value&&l.type!=="open"){c.push(le(c.pop(),l.value));continue}l.nodes&&s(l,n)}return c},"walk");return Ce.flatten(s(t))},"expand$1");var _D=mD,AD={MAX_LENGTH:1024*64,CHAR_0:"0",CHAR_9:"9",CHAR_UPPERCASE_A:"A",CHAR_LOWERCASE_A:"a",CHAR_UPPERCASE_Z:"Z",CHAR_LOWERCASE_Z:"z",CHAR_LEFT_PARENTHESES:"(",CHAR_RIGHT_PARENTHESES:")",CHAR_ASTERISK:"*",CHAR_AMPERSAND:"&",CHAR_AT:"@",CHAR_BACKSLASH:"\\",CHAR_BACKTICK:"`",CHAR_CARRIAGE_RETURN:"\r",CHAR_CIRCUMFLEX_ACCENT:"^",CHAR_COLON:":",CHAR_COMMA:",",CHAR_DOLLAR:"$",CHAR_DOT:".",CHAR_DOUBLE_QUOTE:'"',CHAR_EQUAL:"=",CHAR_EXCLAMATION_MARK:"!",CHAR_FORM_FEED:"\f",CHAR_FORWARD_SLASH:"/",CHAR_HASH:"#",CHAR_HYPHEN_MINUS:"-",CHAR_LEFT_ANGLE_BRACKET:"<",CHAR_LEFT_CURLY_BRACE:"{",CHAR_LEFT_SQUARE_BRACKET:"[",CHAR_LINE_FEED:`
`,CHAR_NO_BREAK_SPACE:"\xA0",CHAR_PERCENT:"%",CHAR_PLUS:"+",CHAR_QUESTION_MARK:"?",CHAR_RIGHT_ANGLE_BRACKET:">",CHAR_RIGHT_CURLY_BRACE:"}",CHAR_RIGHT_SQUARE_BRACKET:"]",CHAR_SEMICOLON:";",CHAR_SINGLE_QUOTE:"'",CHAR_SPACE:" ",CHAR_TAB:"	",CHAR_UNDERSCORE:"_",CHAR_VERTICAL_LINE:"|",CHAR_ZERO_WIDTH_NOBREAK_SPACE:"\uFEFF"};const yD=Xt,{MAX_LENGTH:Zs,CHAR_BACKSLASH:eu,CHAR_BACKTICK:wD,CHAR_COMMA:RD,CHAR_DOT:bD,CHAR_LEFT_PARENTHESES:vD,CHAR_RIGHT_PARENTHESES:SD,CHAR_LEFT_CURLY_BRACE:BD,CHAR_RIGHT_CURLY_BRACE:$D,CHAR_LEFT_SQUARE_BRACKET:Js,CHAR_RIGHT_SQUARE_BRACKET:en,CHAR_DOUBLE_QUOTE:TD,CHAR_SINGLE_QUOTE:xD,CHAR_NO_BREAK_SPACE:OD,CHAR_ZERO_WIDTH_NOBREAK_SPACE:ND}=AD,HD=a((t,e={})=>{if(typeof t!="string")throw new TypeError("Expected a string");let u=e||{},s=typeof u.maxLength=="number"?Math.min(Zs,u.maxLength):Zs;if(t.length>s)throw new SyntaxError(`Input length (${t.length}), exceeds max characters (${s})`);let n={type:"root",input:t,nodes:[]},r=[n],i=n,o=n,D=0,c=t.length,f=0,h=0,l;const p=a(()=>t[f++],"advance"),C=a(g=>{if(g.type==="text"&&o.type==="dot"&&(o.type="text"),o&&o.type==="text"&&g.type==="text"){o.value+=g.value;return}return i.nodes.push(g),g.parent=i,g.prev=o,o=g,g},"push");for(C({type:"bos"});f<c;)if(i=r[r.length-1],l=p(),!(l===ND||l===OD)){if(l===eu){C({type:"text",value:(e.keepEscaping?l:"")+p()});continue}if(l===en){C({type:"text",value:"\\"+l});continue}if(l===Js){D++;let g;for(;f<c&&(g=p());){if(l+=g,g===Js){D++;continue}if(g===eu){l+=p();continue}if(g===en&&(D--,D===0))break}C({type:"text",value:l});continue}if(l===vD){i=C({type:"paren",nodes:[]}),r.push(i),C({type:"text",value:l});continue}if(l===SD){if(i.type!=="paren"){C({type:"text",value:l});continue}i=r.pop(),C({type:"text",value:l}),i=r[r.length-1];continue}if(l===TD||l===xD||l===wD){let g=l,y;for(e.keepQuotes!==!0&&(l="");f<c&&(y=p());){if(y===eu){l+=y+p();continue}if(y===g){e.keepQuotes===!0&&(l+=y);break}l+=y}C({type:"text",value:l});continue}if(l===BD){h++;let y={type:"brace",open:!0,close:!1,dollar:o.value&&o.value.slice(-1)==="$"||i.dollar===!0,depth:h,commas:0,ranges:0,nodes:[]};i=C(y),r.push(i),C({type:"open",value:l});continue}if(l===$D){if(i.type!=="brace"){C({type:"text",value:l});continue}let g="close";i=r.pop(),i.close=!0,C({type:g,value:l}),h--,i=r[r.length-1];continue}if(l===RD&&h>0){if(i.ranges>0){i.ranges=0;let g=i.nodes.shift();i.nodes=[g,{type:"text",value:yD(i)}]}C({type:"comma",value:l}),i.commas++;continue}if(l===bD&&h>0&&i.commas===0){let g=i.nodes;if(h===0||g.length===0){C({type:"text",value:l});continue}if(o.type==="dot"){if(i.range=[],o.value+=l,o.type="range",i.nodes.length!==3&&i.nodes.length!==5){i.invalid=!0,i.ranges=0,o.type="text";continue}i.ranges++,i.args=[];continue}if(o.type==="range"){g.pop();let y=g[g.length-1];y.value+=o.value+l,o=y,i.ranges--;continue}C({type:"dot",value:l});continue}C({type:"text",value:l})}do if(i=r.pop(),i.type!=="root"){i.nodes.forEach(B=>{B.nodes||(B.type==="open"&&(B.isOpen=!0),B.type==="close"&&(B.isClose=!0),B.nodes||(B.type="text"),B.invalid=!0)});let g=r[r.length-1],y=g.nodes.indexOf(i);g.nodes.splice(y,1,...i.nodes)}while(r.length>0);return C({type:"eos"}),n},"parse$1");var LD=HD;const tn=Xt,ID=FD,PD=_D,kD=LD,q=a((t,e={})=>{let u=[];if(Array.isArray(t))for(let s of t){let n=q.create(s,e);Array.isArray(n)?u.push(...n):u.push(n)}else u=[].concat(q.create(t,e));return e&&e.expand===!0&&e.nodupes===!0&&(u=[...new Set(u)]),u},"braces$1");q.parse=(t,e={})=>kD(t,e),q.stringify=(t,e={})=>tn(typeof t=="string"?q.parse(t,e):t,e),q.compile=(t,e={})=>(typeof t=="string"&&(t=q.parse(t,e)),ID(t,e)),q.expand=(t,e={})=>{typeof t=="string"&&(t=q.parse(t,e));let u=PD(t,e);return e.noempty===!0&&(u=u.filter(Boolean)),e.nodupes===!0&&(u=[...new Set(u)]),u},q.create=(t,e={})=>t===""||t.length<3?[t]:e.expand!==!0?q.compile(t,e):q.expand(t,e);var MD=q,GD=["3dm","3ds","3g2","3gp","7z","a","aac","adp","afdesign","afphoto","afpub","ai","aif","aiff","alz","ape","apk","appimage","ar","arj","asf","au","avi","bak","baml","bh","bin","bk","bmp","btif","bz2","bzip2","cab","caf","cgm","class","cmx","cpio","cr2","cur","dat","dcm","deb","dex","djvu","dll","dmg","dng","doc","docm","docx","dot","dotm","dra","DS_Store","dsk","dts","dtshd","dvb","dwg","dxf","ecelp4800","ecelp7470","ecelp9600","egg","eol","eot","epub","exe","f4v","fbs","fh","fla","flac","flatpak","fli","flv","fpx","fst","fvt","g3","gh","gif","graffle","gz","gzip","h261","h263","h264","icns","ico","ief","img","ipa","iso","jar","jpeg","jpg","jpgv","jpm","jxr","key","ktx","lha","lib","lvp","lz","lzh","lzma","lzo","m3u","m4a","m4v","mar","mdi","mht","mid","midi","mj2","mka","mkv","mmr","mng","mobi","mov","movie","mp3","mp4","mp4a","mpeg","mpg","mpga","mxu","nef","npx","numbers","nupkg","o","odp","ods","odt","oga","ogg","ogv","otf","ott","pages","pbm","pcx","pdb","pdf","pea","pgm","pic","png","pnm","pot","potm","potx","ppa","ppam","ppm","pps","ppsm","ppsx","ppt","pptm","pptx","psd","pya","pyc","pyo","pyv","qt","rar","ras","raw","resources","rgb","rip","rlc","rmf","rmvb","rpm","rtf","rz","s3m","s7z","scpt","sgi","shar","snap","sil","sketch","slk","smv","snk","so","stl","suo","sub","swf","tar","tbz","tbz2","tga","tgz","thmx","tif","tiff","tlz","ttc","ttf","txz","udf","uvh","uvi","uvm","uvp","uvs","uvu","viv","vob","war","wav","wax","wbmp","wdp","weba","webm","webp","whl","wim","wm","wma","wmv","wmx","woff","woff2","wrm","wvx","xbm","xif","xla","xlam","xls","xlsb","xlsm","xlsx","xlt","xltm","xltx","xm","xmind","xpi","xpm","xwd","xz","z","zip","zipx"],WD=GD;const jD=z,UD=WD,KD=new Set(UD);var VD=a(t=>KD.has(jD.extname(t).slice(1).toLowerCase()),"isBinaryPath$1"),nt={};(function(t){const{sep:e}=z,{platform:u}=process,s=Ru;t.EV_ALL="all",t.EV_READY="ready",t.EV_ADD="add",t.EV_CHANGE="change",t.EV_ADD_DIR="addDir",t.EV_UNLINK="unlink",t.EV_UNLINK_DIR="unlinkDir",t.EV_RAW="raw",t.EV_ERROR="error",t.STR_DATA="data",t.STR_END="end",t.STR_CLOSE="close",t.FSEVENT_CREATED="created",t.FSEVENT_MODIFIED="modified",t.FSEVENT_DELETED="deleted",t.FSEVENT_MOVED="moved",t.FSEVENT_CLONED="cloned",t.FSEVENT_UNKNOWN="unknown",t.FSEVENT_FLAG_MUST_SCAN_SUBDIRS=1,t.FSEVENT_TYPE_FILE="file",t.FSEVENT_TYPE_DIRECTORY="directory",t.FSEVENT_TYPE_SYMLINK="symlink",t.KEY_LISTENERS="listeners",t.KEY_ERR="errHandlers",t.KEY_RAW="rawEmitters",t.HANDLER_KEYS=[t.KEY_LISTENERS,t.KEY_ERR,t.KEY_RAW],t.DOT_SLASH=`.${e}`,t.BACK_SLASH_RE=/\\/g,t.DOUBLE_SLASH_RE=/\/\//,t.SLASH_OR_BACK_SLASH_RE=/[/\\]/,t.DOT_RE=/\..*\.(sw[px])$|~$|\.subl.*\.tmp/,t.REPLACER_RE=/^\.[/\\]/,t.SLASH="/",t.SLASH_SLASH="//",t.BRACE_START="{",t.BANG="!",t.ONE_DOT=".",t.TWO_DOTS="..",t.STAR="*",t.GLOBSTAR="**",t.ROOT_GLOBSTAR="/**/*",t.SLASH_GLOBSTAR="/**",t.DIR_SUFFIX="Dir",t.ANYMATCH_OPTS={dot:!0},t.STRING_TYPE="string",t.FUNCTION_TYPE="function",t.EMPTY_STR="",t.EMPTY_FN=()=>{},t.IDENTITY_FN=n=>n,t.isWindows=u==="win32",t.isMacos=u==="darwin",t.isLinux=u==="linux",t.isIBMi=s.type()==="OS400"})(nt);const se=oe,P=z,{promisify:Se}=_e,zD=VD,{isWindows:YD,isLinux:qD,EMPTY_FN:XD,EMPTY_STR:QD,KEY_LISTENERS:Fe,KEY_ERR:tu,KEY_RAW:Be,HANDLER_KEYS:ZD,EV_CHANGE:rt,EV_ADD:it,EV_ADD_DIR:JD,EV_ERROR:un,STR_DATA:ea,STR_END:ta,BRACE_START:ua,STAR:sa}=nt,na="watch",ra=Se(se.open),sn=Se(se.stat),ia=Se(se.lstat),oa=Se(se.close),uu=Se(se.realpath),Da={lstat:ia,stat:sn},su=a((t,e)=>{t instanceof Set?t.forEach(e):e(t)},"foreach"),$e=a((t,e,u)=>{let s=t[e];s instanceof Set||(t[e]=s=new Set([s])),s.add(u)},"addAndConvert"),aa=a(t=>e=>{const u=t[e];u instanceof Set?u.clear():delete t[e]},"clearItem"),Te=a((t,e,u)=>{const s=t[e];s instanceof Set?s.delete(u):s===u&&delete t[e]},"delFromSet"),nn=a(t=>t instanceof Set?t.size===0:!t,"isEmptySet"),ot=new Map;function rn(t,e,u,s,n){const r=a((i,o)=>{u(t),n(i,o,{watchedPath:t}),o&&t!==o&&Dt(P.resolve(t,o),Fe,P.join(t,o))},"handleEvent");try{return se.watch(t,e,r)}catch(i){s(i)}}a(rn,"createFsWatchInstance");const Dt=a((t,e,u,s,n)=>{const r=ot.get(t);r&&su(r[e],i=>{i(u,s,n)})},"fsWatchBroadcast"),la=a((t,e,u,s)=>{const{listener:n,errHandler:r,rawEmitter:i}=s;let o=ot.get(e),D;if(!u.persistent)return D=rn(t,u,n,r,i),D.close.bind(D);if(o)$e(o,Fe,n),$e(o,tu,r),$e(o,Be,i);else{if(D=rn(t,u,Dt.bind(null,e,Fe),r,Dt.bind(null,e,Be)),!D)return;D.on(un,async c=>{const f=Dt.bind(null,e,tu);if(o.watcherUnusable=!0,YD&&c.code==="EPERM")try{const h=await ra(t,"r");await oa(h),f(c)}catch{}else f(c)}),o={listeners:n,errHandlers:r,rawEmitters:i,watcher:D},ot.set(e,o)}return()=>{Te(o,Fe,n),Te(o,tu,r),Te(o,Be,i),nn(o.listeners)&&(o.watcher.close(),ot.delete(e),ZD.forEach(aa(o)),o.watcher=void 0,Object.freeze(o))}},"setFsWatchListener"),nu=new Map,ca=a((t,e,u,s)=>{const{listener:n,rawEmitter:r}=s;let i=nu.get(e);const o=i&&i.options;return o&&(o.persistent<u.persistent||o.interval>u.interval)&&(i.listeners,i.rawEmitters,se.unwatchFile(e),i=void 0),i?($e(i,Fe,n),$e(i,Be,r)):(i={listeners:n,rawEmitters:r,options:u,watcher:se.watchFile(e,u,(D,c)=>{su(i.rawEmitters,h=>{h(rt,e,{curr:D,prev:c})});const f=D.mtimeMs;(D.size!==c.size||f>c.mtimeMs||f===0)&&su(i.listeners,h=>h(t,D))})},nu.set(e,i)),()=>{Te(i,Fe,n),Te(i,Be,r),nn(i.listeners)&&(nu.delete(e),se.unwatchFile(e),i.options=i.watcher=void 0,Object.freeze(i))}},"setFsWatchFileListener");let fa=class{static{a(this,"NodeFsHandler")}constructor(e){this.fsw=e,this._boundHandleError=u=>e._handleError(u)}_watchWithNodeFs(e,u){const s=this.fsw.options,n=P.dirname(e),r=P.basename(e);this.fsw._getWatchedDir(n).add(r);const o=P.resolve(e),D={persistent:s.persistent};u||(u=XD);let c;return s.usePolling?(D.interval=s.enableBinaryInterval&&zD(r)?s.binaryInterval:s.interval,c=ca(e,o,D,{listener:u,rawEmitter:this.fsw._emitRaw})):c=la(e,o,D,{listener:u,errHandler:this._boundHandleError,rawEmitter:this.fsw._emitRaw}),c}_handleFile(e,u,s){if(this.fsw.closed)return;const n=P.dirname(e),r=P.basename(e),i=this.fsw._getWatchedDir(n);let o=u;if(i.has(r))return;const D=a(async(f,h)=>{if(this.fsw._throttle(na,e,5)){if(!h||h.mtimeMs===0)try{const l=await sn(e);if(this.fsw.closed)return;const p=l.atimeMs,C=l.mtimeMs;(!p||p<=C||C!==o.mtimeMs)&&this.fsw._emit(rt,e,l),qD&&o.ino!==l.ino?(this.fsw._closeFile(f),o=l,this.fsw._addPathCloser(f,this._watchWithNodeFs(e,D))):o=l}catch{this.fsw._remove(n,r)}else if(i.has(r)){const l=h.atimeMs,p=h.mtimeMs;(!l||l<=p||p!==o.mtimeMs)&&this.fsw._emit(rt,e,h),o=h}}},"listener"),c=this._watchWithNodeFs(e,D);if(!(s&&this.fsw.options.ignoreInitial)&&this.fsw._isntIgnored(e)){if(!this.fsw._throttle(it,e,0))return;this.fsw._emit(it,e,u)}return c}async _handleSymlink(e,u,s,n){if(this.fsw.closed)return;const r=e.fullPath,i=this.fsw._getWatchedDir(u);if(!this.fsw.options.followSymlinks){this.fsw._incrReadyCount();let o;try{o=await uu(s)}catch{return this.fsw._emitReady(),!0}return this.fsw.closed?void 0:(i.has(n)?this.fsw._symlinkPaths.get(r)!==o&&(this.fsw._symlinkPaths.set(r,o),this.fsw._emit(rt,s,e.stats)):(i.add(n),this.fsw._symlinkPaths.set(r,o),this.fsw._emit(it,s,e.stats)),this.fsw._emitReady(),!0)}if(this.fsw._symlinkPaths.has(r))return!0;this.fsw._symlinkPaths.set(r,!0)}_handleRead(e,u,s,n,r,i,o){if(e=P.join(e,QD),!s.hasGlob&&(o=this.fsw._throttle("readdir",e,1e3),!o))return;const D=this.fsw._getWatchedDir(s.path),c=new Set;let f=this.fsw._readdirp(e,{fileFilter:a(h=>s.filterPath(h),"fileFilter"),directoryFilter:a(h=>s.filterDir(h),"directoryFilter"),depth:0}).on(ea,async h=>{if(this.fsw.closed){f=void 0;return}const l=h.path;let p=P.join(e,l);if(c.add(l),!(h.stats.isSymbolicLink()&&await this._handleSymlink(h,e,p,l))){if(this.fsw.closed){f=void 0;return}(l===n||!n&&!D.has(l))&&(this.fsw._incrReadyCount(),p=P.join(r,P.relative(r,p)),this._addToNodeFs(p,u,s,i+1))}}).on(un,this._boundHandleError);return new Promise(h=>f.once(ta,()=>{if(this.fsw.closed){f=void 0;return}const l=o?o.clear():!1;h(),D.getChildren().filter(p=>p!==e&&!c.has(p)&&(!s.hasGlob||s.filterPath({fullPath:P.resolve(e,p)}))).forEach(p=>{this.fsw._remove(e,p)}),f=void 0,l&&this._handleRead(e,!1,s,n,r,i,o)}))}async _handleDir(e,u,s,n,r,i,o){const D=this.fsw._getWatchedDir(P.dirname(e)),c=D.has(P.basename(e));!(s&&this.fsw.options.ignoreInitial)&&!r&&!c&&(!i.hasGlob||i.globFilter(e))&&this.fsw._emit(JD,e,u),D.add(P.basename(e)),this.fsw._getWatchedDir(e);let f,h;const l=this.fsw.options.depth;if((l==null||n<=l)&&!this.fsw._symlinkPaths.has(o)){if(!r&&(await this._handleRead(e,s,i,r,e,n,f),this.fsw.closed))return;h=this._watchWithNodeFs(e,(p,C)=>{C&&C.mtimeMs===0||this._handleRead(p,!1,i,r,e,n,f)})}return h}async _addToNodeFs(e,u,s,n,r){const i=this.fsw._emitReady;if(this.fsw._isIgnored(e)||this.fsw.closed)return i(),!1;const o=this.fsw._getWatchHelpers(e,n);!o.hasGlob&&s&&(o.hasGlob=s.hasGlob,o.globFilter=s.globFilter,o.filterPath=D=>s.filterPath(D),o.filterDir=D=>s.filterDir(D));try{const D=await Da[o.statMethod](o.watchPath);if(this.fsw.closed)return;if(this.fsw._isIgnored(o.watchPath,D))return i(),!1;const c=this.fsw.options.followSymlinks&&!e.includes(sa)&&!e.includes(ua);let f;if(D.isDirectory()){const h=P.resolve(e),l=c?await uu(e):e;if(this.fsw.closed||(f=await this._handleDir(o.watchPath,D,u,n,r,o,l),this.fsw.closed))return;h!==l&&l!==void 0&&this.fsw._symlinkPaths.set(h,l)}else if(D.isSymbolicLink()){const h=c?await uu(e):e;if(this.fsw.closed)return;const l=P.dirname(o.watchPath);if(this.fsw._getWatchedDir(l).add(o.watchPath),this.fsw._emit(it,o.watchPath,D),f=await this._handleDir(l,D,u,n,e,o,h),this.fsw.closed)return;h!==void 0&&this.fsw._symlinkPaths.set(P.resolve(e),h)}else f=this._handleFile(o.watchPath,D,u);return i(),this.fsw._addPathCloser(e,f),!1}catch(D){if(this.fsw._handleError(D))return i(),e}}};var ha=fa,ru={exports:{}};const iu=oe,k=z,{promisify:ou}=_e;let ge;try{ge=We("fsevents")}catch(t){process.env.CHOKIDAR_PRINT_FSEVENTS_REQUIRE_ERROR&&console.error(t)}if(ge){const t=process.version.match(/v(\d+)\.(\d+)/);if(t&&t[1]&&t[2]){const e=Number.parseInt(t[1],10),u=Number.parseInt(t[2],10);e===8&&u<16&&(ge=void 0)}}const{EV_ADD:Du,EV_CHANGE:da,EV_ADD_DIR:on,EV_UNLINK:at,EV_ERROR:Ea,STR_DATA:pa,STR_END:Ca,FSEVENT_CREATED:Fa,FSEVENT_MODIFIED:ga,FSEVENT_DELETED:ma,FSEVENT_MOVED:_a,FSEVENT_UNKNOWN:Aa,FSEVENT_FLAG_MUST_SCAN_SUBDIRS:ya,FSEVENT_TYPE_FILE:wa,FSEVENT_TYPE_DIRECTORY:xe,FSEVENT_TYPE_SYMLINK:Dn,ROOT_GLOBSTAR:an,DIR_SUFFIX:Ra,DOT_SLASH:ln,FUNCTION_TYPE:au,EMPTY_FN:ba,IDENTITY_FN:va}=nt,Sa=a(t=>isNaN(t)?{}:{depth:t},"Depth"),lu=ou(iu.stat),Ba=ou(iu.lstat),cn=ou(iu.realpath),$a={stat:lu,lstat:Ba},ce=new Map,Ta=10,xa=new Set([69888,70400,71424,72704,73472,131328,131840,262912]),Oa=a((t,e)=>({stop:ge.watch(t,e)}),"createFSEventsInstance");function Na(t,e,u,s){let n=k.extname(e)?k.dirname(e):e;const r=k.dirname(n);let i=ce.get(n);Ha(r)&&(n=r);const o=k.resolve(t),D=o!==e,c=a((h,l,p)=>{D&&(h=h.replace(e,o)),(h===o||!h.indexOf(o+k.sep))&&u(h,l,p)},"filteredListener");let f=!1;for(const h of ce.keys())if(e.indexOf(k.resolve(h)+k.sep)===0){n=h,i=ce.get(n),f=!0;break}return i||f?i.listeners.add(c):(i={listeners:new Set([c]),rawEmitter:s,watcher:Oa(n,(h,l)=>{if(!i.listeners.size||l&ya)return;const p=ge.getInfo(h,l);i.listeners.forEach(C=>{C(h,l,p)}),i.rawEmitter(p.event,h,p)})},ce.set(n,i)),()=>{const h=i.listeners;if(h.delete(c),!h.size&&(ce.delete(n),i.watcher))return i.watcher.stop().then(()=>{i.rawEmitter=i.watcher=void 0,Object.freeze(i)})}}a(Na,"setFSEventsListener");const Ha=a(t=>{let e=0;for(const u of ce.keys())if(u.indexOf(t)===0&&(e++,e>=Ta))return!0;return!1},"couldConsolidate"),La=a(()=>ge&&ce.size<128,"canUse"),cu=a((t,e)=>{let u=0;for(;!t.indexOf(e)&&(t=k.dirname(t))!==e;)u++;return u},"calcDepth"),fn=a((t,e)=>t.type===xe&&e.isDirectory()||t.type===Dn&&e.isSymbolicLink()||t.type===wa&&e.isFile(),"sameTypes");let Ia=class{static{a(this,"FsEventsHandler")}constructor(e){this.fsw=e}checkIgnored(e,u){const s=this.fsw._ignoredPaths;if(this.fsw._isIgnored(e,u))return s.add(e),u&&u.isDirectory()&&s.add(e+an),!0;s.delete(e),s.delete(e+an)}addOrChange(e,u,s,n,r,i,o,D){const c=r.has(i)?da:Du;this.handleEvent(c,e,u,s,n,r,i,o,D)}async checkExists(e,u,s,n,r,i,o,D){try{const c=await lu(e);if(this.fsw.closed)return;fn(o,c)?this.addOrChange(e,u,s,n,r,i,o,D):this.handleEvent(at,e,u,s,n,r,i,o,D)}catch(c){c.code==="EACCES"?this.addOrChange(e,u,s,n,r,i,o,D):this.handleEvent(at,e,u,s,n,r,i,o,D)}}handleEvent(e,u,s,n,r,i,o,D,c){if(!(this.fsw.closed||this.checkIgnored(u)))if(e===at){const f=D.type===xe;(f||i.has(o))&&this.fsw._remove(r,o,f)}else{if(e===Du){if(D.type===xe&&this.fsw._getWatchedDir(u),D.type===Dn&&c.followSymlinks){const h=c.depth===void 0?void 0:cu(s,n)+1;return this._addToFsEvents(u,!1,!0,h)}this.fsw._getWatchedDir(r).add(o)}const f=D.type===xe?e+Ra:e;this.fsw._emit(f,u),f===on&&this._addToFsEvents(u,!1,!0)}}_watchWithFsEvents(e,u,s,n){if(this.fsw.closed||this.fsw._isIgnored(e))return;const r=this.fsw.options,o=Na(e,u,a(async(D,c,f)=>{if(this.fsw.closed||r.depth!==void 0&&cu(D,u)>r.depth)return;const h=s(k.join(e,k.relative(e,D)));if(n&&!n(h))return;const l=k.dirname(h),p=k.basename(h),C=this.fsw._getWatchedDir(f.type===xe?h:l);if(xa.has(c)||f.event===Aa)if(typeof r.ignored===au){let g;try{g=await lu(h)}catch{}if(this.fsw.closed||this.checkIgnored(h,g))return;fn(f,g)?this.addOrChange(h,D,u,l,C,p,f,r):this.handleEvent(at,h,D,u,l,C,p,f,r)}else this.checkExists(h,D,u,l,C,p,f,r);else switch(f.event){case Fa:case ga:return this.addOrChange(h,D,u,l,C,p,f,r);case ma:case _a:return this.checkExists(h,D,u,l,C,p,f,r)}},"watchCallback"),this.fsw._emitRaw);return this.fsw._emitReady(),o}async _handleFsEventsSymlink(e,u,s,n){if(!(this.fsw.closed||this.fsw._symlinkPaths.has(u))){this.fsw._symlinkPaths.set(u,!0),this.fsw._incrReadyCount();try{const r=await cn(e);if(this.fsw.closed)return;if(this.fsw._isIgnored(r))return this.fsw._emitReady();this.fsw._incrReadyCount(),this._addToFsEvents(r||e,i=>{let o=e;return r&&r!==ln?o=i.replace(r,e):i!==ln&&(o=k.join(e,i)),s(o)},!1,n)}catch(r){if(this.fsw._handleError(r))return this.fsw._emitReady()}}}emitAdd(e,u,s,n,r){const i=s(e),o=u.isDirectory(),D=this.fsw._getWatchedDir(k.dirname(i)),c=k.basename(i);o&&this.fsw._getWatchedDir(i),!D.has(c)&&(D.add(c),(!n.ignoreInitial||r===!0)&&this.fsw._emit(o?on:Du,i,u))}initWatch(e,u,s,n){if(this.fsw.closed)return;const r=this._watchWithFsEvents(s.watchPath,k.resolve(e||s.watchPath),n,s.globFilter);this.fsw._addPathCloser(u,r)}async _addToFsEvents(e,u,s,n){if(this.fsw.closed)return;const r=this.fsw.options,i=typeof u===au?u:va,o=this.fsw._getWatchHelpers(e);try{const D=await $a[o.statMethod](o.watchPath);if(this.fsw.closed)return;if(this.fsw._isIgnored(o.watchPath,D))throw null;if(D.isDirectory()){if(o.globFilter||this.emitAdd(i(e),D,i,r,s),n&&n>r.depth)return;this.fsw._readdirp(o.watchPath,{fileFilter:a(c=>o.filterPath(c),"fileFilter"),directoryFilter:a(c=>o.filterDir(c),"directoryFilter"),...Sa(r.depth-(n||0))}).on(pa,c=>{if(this.fsw.closed||c.stats.isDirectory()&&!o.filterPath(c))return;const f=k.join(o.watchPath,c.path),{fullPath:h}=c;if(o.followSymlinks&&c.stats.isSymbolicLink()){const l=r.depth===void 0?void 0:cu(f,k.resolve(o.watchPath))+1;this._handleFsEventsSymlink(f,h,i,l)}else this.emitAdd(f,c.stats,i,r,s)}).on(Ea,ba).on(Ca,()=>{this.fsw._emitReady()})}else this.emitAdd(o.watchPath,D,i,r,s),this.fsw._emitReady()}catch(D){(!D||this.fsw._handleError(D))&&(this.fsw._emitReady(),this.fsw._emitReady())}if(r.persistent&&s!==!0)if(typeof u===au)this.initWatch(void 0,e,o,i);else{let D;try{D=await cn(o.watchPath)}catch{}this.initWatch(D,e,o,i)}}};ru.exports=Ia,ru.exports.canUse=La;var Pa=ru.exports;const{EventEmitter:ka}=Wn,fu=oe,S=z,{promisify:hn}=_e,Ma=No,hu=Mo.default,Ga=Zo,du=Os,Wa=MD,ja=Ss,Ua=ha,dn=Pa,{EV_ALL:Eu,EV_READY:Ka,EV_ADD:lt,EV_CHANGE:Oe,EV_UNLINK:En,EV_ADD_DIR:Va,EV_UNLINK_DIR:za,EV_RAW:Ya,EV_ERROR:pu,STR_CLOSE:qa,STR_END:Xa,BACK_SLASH_RE:Qa,DOUBLE_SLASH_RE:pn,SLASH_OR_BACK_SLASH_RE:Za,DOT_RE:Ja,REPLACER_RE:el,SLASH:Cu,SLASH_SLASH:tl,BRACE_START:ul,BANG:Fu,ONE_DOT:Cn,TWO_DOTS:sl,GLOBSTAR:nl,SLASH_GLOBSTAR:gu,ANYMATCH_OPTS:mu,STRING_TYPE:_u,FUNCTION_TYPE:rl,EMPTY_STR:Au,EMPTY_FN:il,isWindows:ol,isMacos:Dl,isIBMi:al}=nt,ll=hn(fu.stat),cl=hn(fu.readdir),yu=a((t=[])=>Array.isArray(t)?t:[t],"arrify"),Fn=a((t,e=[])=>(t.forEach(u=>{Array.isArray(u)?Fn(u,e):e.push(u)}),e),"flatten"),gn=a(t=>{const e=Fn(yu(t));if(!e.every(u=>typeof u===_u))throw new TypeError(`Non-string provided as watch path: ${e}`);return e.map(_n)},"unifyPaths"),mn=a(t=>{let e=t.replace(Qa,Cu),u=!1;for(e.startsWith(tl)&&(u=!0);e.match(pn);)e=e.replace(pn,Cu);return u&&(e=Cu+e),e},"toUnix"),_n=a(t=>mn(S.normalize(mn(t))),"normalizePathToUnix"),An=a((t=Au)=>e=>typeof e!==_u?e:_n(S.isAbsolute(e)?e:S.join(t,e)),"normalizeIgnored"),fl=a((t,e)=>S.isAbsolute(t)?t:t.startsWith(Fu)?Fu+S.join(e,t.slice(1)):S.join(e,t),"getAbsolutePath"),X=a((t,e)=>t[e]===void 0,"undef");class hl{static{a(this,"DirEntry")}constructor(e,u){this.path=e,this._removeWatcher=u,this.items=new Set}add(e){const{items:u}=this;u&&e!==Cn&&e!==sl&&u.add(e)}async remove(e){const{items:u}=this;if(!u||(u.delete(e),u.size>0))return;const s=this.path;try{await cl(s)}catch{this._removeWatcher&&this._removeWatcher(S.dirname(s),S.basename(s))}}has(e){const{items:u}=this;if(u)return u.has(e)}getChildren(){const{items:e}=this;if(e)return[...e.values()]}dispose(){this.items.clear(),delete this.path,delete this._removeWatcher,delete this.items,Object.freeze(this)}}const dl="stat",El="lstat";class pl{static{a(this,"WatchHelper")}constructor(e,u,s,n){this.fsw=n,this.path=e=e.replace(el,Au),this.watchPath=u,this.fullWatchPath=S.resolve(u),this.hasGlob=u!==e,e===Au&&(this.hasGlob=!1),this.globSymlink=this.hasGlob&&s?void 0:!1,this.globFilter=this.hasGlob?hu(e,void 0,mu):!1,this.dirParts=this.getDirParts(e),this.dirParts.forEach(r=>{r.length>1&&r.pop()}),this.followSymlinks=s,this.statMethod=s?dl:El}checkGlobSymlink(e){return this.globSymlink===void 0&&(this.globSymlink=e.fullParentDir===this.fullWatchPath?!1:{realPath:e.fullParentDir,linkPath:this.fullWatchPath}),this.globSymlink?e.fullPath.replace(this.globSymlink.realPath,this.globSymlink.linkPath):e.fullPath}entryPath(e){return S.join(this.watchPath,S.relative(this.watchPath,this.checkGlobSymlink(e)))}filterPath(e){const{stats:u}=e;if(u&&u.isSymbolicLink())return this.filterDir(e);const s=this.entryPath(e);return(this.hasGlob&&typeof this.globFilter===rl?this.globFilter(s):!0)&&this.fsw._isntIgnored(s,u)&&this.fsw._hasReadPermissions(u)}getDirParts(e){if(!this.hasGlob)return[];const u=[];return(e.includes(ul)?Wa.expand(e):[e]).forEach(n=>{u.push(S.relative(this.watchPath,n).split(Za))}),u}filterDir(e){if(this.hasGlob){const u=this.getDirParts(this.checkGlobSymlink(e));let s=!1;this.unmatchedGlob=!this.dirParts.some(n=>n.every((r,i)=>(r===nl&&(s=!0),s||!u[0][i]||hu(r,u[0][i],mu))))}return!this.unmatchedGlob&&this.fsw._isntIgnored(this.entryPath(e),e.stats)}}class Cl extends ka{static{a(this,"FSWatcher")}constructor(e){super();const u={};e&&Object.assign(u,e),this._watched=new Map,this._closers=new Map,this._ignoredPaths=new Set,this._throttled=new Map,this._symlinkPaths=new Map,this._streams=new Set,this.closed=!1,X(u,"persistent")&&(u.persistent=!0),X(u,"ignoreInitial")&&(u.ignoreInitial=!1),X(u,"ignorePermissionErrors")&&(u.ignorePermissionErrors=!1),X(u,"interval")&&(u.interval=100),X(u,"binaryInterval")&&(u.binaryInterval=300),X(u,"disableGlobbing")&&(u.disableGlobbing=!1),u.enableBinaryInterval=u.binaryInterval!==u.interval,X(u,"useFsEvents")&&(u.useFsEvents=!u.usePolling),dn.canUse()||(u.useFsEvents=!1),X(u,"usePolling")&&!u.useFsEvents&&(u.usePolling=Dl),al&&(u.usePolling=!0);const n=process.env.CHOKIDAR_USEPOLLING;if(n!==void 0){const D=n.toLowerCase();D==="false"||D==="0"?u.usePolling=!1:D==="true"||D==="1"?u.usePolling=!0:u.usePolling=!!D}const r=process.env.CHOKIDAR_INTERVAL;r&&(u.interval=Number.parseInt(r,10)),X(u,"atomic")&&(u.atomic=!u.usePolling&&!u.useFsEvents),u.atomic&&(this._pendingUnlinks=new Map),X(u,"followSymlinks")&&(u.followSymlinks=!0),X(u,"awaitWriteFinish")&&(u.awaitWriteFinish=!1),u.awaitWriteFinish===!0&&(u.awaitWriteFinish={});const i=u.awaitWriteFinish;i&&(i.stabilityThreshold||(i.stabilityThreshold=2e3),i.pollInterval||(i.pollInterval=100),this._pendingWrites=new Map),u.ignored&&(u.ignored=yu(u.ignored));let o=0;this._emitReady=()=>{o++,o>=this._readyCount&&(this._emitReady=il,this._readyEmitted=!0,process.nextTick(()=>this.emit(Ka)))},this._emitRaw=(...D)=>this.emit(Ya,...D),this._readyEmitted=!1,this.options=u,u.useFsEvents?this._fsEventsHandler=new dn(this):this._nodeFsHandler=new Ua(this),Object.freeze(u)}add(e,u,s){const{cwd:n,disableGlobbing:r}=this.options;this.closed=!1;let i=gn(e);return n&&(i=i.map(o=>{const D=fl(o,n);return r||!du(o)?D:ja(D)})),i=i.filter(o=>o.startsWith(Fu)?(this._ignoredPaths.add(o.slice(1)),!1):(this._ignoredPaths.delete(o),this._ignoredPaths.delete(o+gu),this._userIgnored=void 0,!0)),this.options.useFsEvents&&this._fsEventsHandler?(this._readyCount||(this._readyCount=i.length),this.options.persistent&&(this._readyCount+=i.length),i.forEach(o=>this._fsEventsHandler._addToFsEvents(o))):(this._readyCount||(this._readyCount=0),this._readyCount+=i.length,Promise.all(i.map(async o=>{const D=await this._nodeFsHandler._addToNodeFs(o,!s,0,0,u);return D&&this._emitReady(),D})).then(o=>{this.closed||o.filter(D=>D).forEach(D=>{this.add(S.dirname(D),S.basename(u||D))})})),this}unwatch(e){if(this.closed)return this;const u=gn(e),{cwd:s}=this.options;return u.forEach(n=>{!S.isAbsolute(n)&&!this._closers.has(n)&&(s&&(n=S.join(s,n)),n=S.resolve(n)),this._closePath(n),this._ignoredPaths.add(n),this._watched.has(n)&&this._ignoredPaths.add(n+gu),this._userIgnored=void 0}),this}close(){if(this.closed)return this._closePromise;this.closed=!0,this.removeAllListeners();const e=[];return this._closers.forEach(u=>u.forEach(s=>{const n=s();n instanceof Promise&&e.push(n)})),this._streams.forEach(u=>u.destroy()),this._userIgnored=void 0,this._readyCount=0,this._readyEmitted=!1,this._watched.forEach(u=>u.dispose()),["closers","watched","streams","symlinkPaths","throttled"].forEach(u=>{this[`_${u}`].clear()}),this._closePromise=e.length?Promise.all(e).then(()=>{}):Promise.resolve(),this._closePromise}getWatched(){const e={};return this._watched.forEach((u,s)=>{const n=this.options.cwd?S.relative(this.options.cwd,s):s;e[n||Cn]=u.getChildren().sort()}),e}emitWithAll(e,u){this.emit(...u),e!==pu&&this.emit(Eu,...u)}async _emit(e,u,s,n,r){if(this.closed)return;const i=this.options;ol&&(u=S.normalize(u)),i.cwd&&(u=S.relative(i.cwd,u));const o=[e,u];r!==void 0?o.push(s,n,r):n!==void 0?o.push(s,n):s!==void 0&&o.push(s);const D=i.awaitWriteFinish;let c;if(D&&(c=this._pendingWrites.get(u)))return c.lastChange=new Date,this;if(i.atomic){if(e===En)return this._pendingUnlinks.set(u,o),setTimeout(()=>{this._pendingUnlinks.forEach((f,h)=>{this.emit(...f),this.emit(Eu,...f),this._pendingUnlinks.delete(h)})},typeof i.atomic=="number"?i.atomic:100),this;e===lt&&this._pendingUnlinks.has(u)&&(e=o[0]=Oe,this._pendingUnlinks.delete(u))}if(D&&(e===lt||e===Oe)&&this._readyEmitted){const f=a((h,l)=>{h?(e=o[0]=pu,o[1]=h,this.emitWithAll(e,o)):l&&(o.length>2?o[2]=l:o.push(l),this.emitWithAll(e,o))},"awfEmit");return this._awaitWriteFinish(u,D.stabilityThreshold,e,f),this}if(e===Oe&&!this._throttle(Oe,u,50))return this;if(i.alwaysStat&&s===void 0&&(e===lt||e===Va||e===Oe)){const f=i.cwd?S.join(i.cwd,u):u;let h;try{h=await ll(f)}catch{}if(!h||this.closed)return;o.push(h)}return this.emitWithAll(e,o),this}_handleError(e){const u=e&&e.code;return e&&u!=="ENOENT"&&u!=="ENOTDIR"&&(!this.options.ignorePermissionErrors||u!=="EPERM"&&u!=="EACCES")&&this.emit(pu,e),e||this.closed}_throttle(e,u,s){this._throttled.has(e)||this._throttled.set(e,new Map);const n=this._throttled.get(e),r=n.get(u);if(r)return r.count++,!1;let i;const o=a(()=>{const c=n.get(u),f=c?c.count:0;return n.delete(u),clearTimeout(i),c&&clearTimeout(c.timeoutObject),f},"clear");i=setTimeout(o,s);const D={timeoutObject:i,clear:o,count:0};return n.set(u,D),D}_incrReadyCount(){return this._readyCount++}_awaitWriteFinish(e,u,s,n){let r,i=e;this.options.cwd&&!S.isAbsolute(e)&&(i=S.join(this.options.cwd,e));const o=new Date,D=a(c=>{fu.stat(i,(f,h)=>{if(f||!this._pendingWrites.has(e)){f&&f.code!=="ENOENT"&&n(f);return}const l=Number(new Date);c&&h.size!==c.size&&(this._pendingWrites.get(e).lastChange=l);const p=this._pendingWrites.get(e);l-p.lastChange>=u?(this._pendingWrites.delete(e),n(void 0,h)):r=setTimeout(D,this.options.awaitWriteFinish.pollInterval,h)})},"awaitWriteFinish");this._pendingWrites.has(e)||(this._pendingWrites.set(e,{lastChange:o,cancelWait:a(()=>(this._pendingWrites.delete(e),clearTimeout(r),s),"cancelWait")}),r=setTimeout(D,this.options.awaitWriteFinish.pollInterval))}_getGlobIgnored(){return[...this._ignoredPaths.values()]}_isIgnored(e,u){if(this.options.atomic&&Ja.test(e))return!0;if(!this._userIgnored){const{cwd:s}=this.options,n=this.options.ignored,r=n&&n.map(An(s)),i=yu(r).filter(D=>typeof D===_u&&!du(D)).map(D=>D+gu),o=this._getGlobIgnored().map(An(s)).concat(r,i);this._userIgnored=hu(o,void 0,mu)}return this._userIgnored([e,u])}_isntIgnored(e,u){return!this._isIgnored(e,u)}_getWatchHelpers(e,u){const s=u||this.options.disableGlobbing||!du(e)?e:Ga(e),n=this.options.followSymlinks;return new pl(e,s,n,this)}_getWatchedDir(e){this._boundRemove||(this._boundRemove=this._remove.bind(this));const u=S.resolve(e);return this._watched.has(u)||this._watched.set(u,new hl(u,this._boundRemove)),this._watched.get(u)}_hasReadPermissions(e){if(this.options.ignorePermissionErrors)return!0;const s=(e&&Number.parseInt(e.mode,10))&511;return!!(4&Number.parseInt(s.toString(8)[0],10))}_remove(e,u,s){const n=S.join(e,u),r=S.resolve(n);if(s=s??(this._watched.has(n)||this._watched.has(r)),!this._throttle("remove",n,100))return;!s&&!this.options.useFsEvents&&this._watched.size===1&&this.add(e,u,!0),this._getWatchedDir(n).getChildren().forEach(l=>this._remove(n,l));const D=this._getWatchedDir(e),c=D.has(u);D.remove(u),this._symlinkPaths.has(r)&&this._symlinkPaths.delete(r);let f=n;if(this.options.cwd&&(f=S.relative(this.options.cwd,n)),this.options.awaitWriteFinish&&this._pendingWrites.has(f)&&this._pendingWrites.get(f).cancelWait()===lt)return;this._watched.delete(n),this._watched.delete(r);const h=s?za:En;c&&!this._isIgnored(n)&&this._emit(h,n),this.options.useFsEvents||this._closePath(n)}_closePath(e){this._closeFile(e);const u=S.dirname(e);this._getWatchedDir(u).remove(S.basename(e))}_closeFile(e){const u=this._closers.get(e);u&&(u.forEach(s=>s()),this._closers.delete(e))}_addPathCloser(e,u){if(!u)return;let s=this._closers.get(e);s||(s=[],this._closers.set(e,s)),s.push(u)}_readdirp(e,u){if(this.closed)return;const s={type:Eu,alwaysStat:!0,lstat:!0,...u};let n=Ma(e,s);return this._streams.add(n),n.once(qa,()=>{n=void 0}),n.once(Xa,()=>{n&&(this._streams.delete(n),n=void 0)}),n}}const Fl=a((t,e)=>{const u=new Cl(e);return u.add(t),u},"watch");var gl=Fl;let fe=!0;const me=typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{};let Ne=0;if(me.process&&me.process.env&&me.process.stdout){const{FORCE_COLOR:t,NODE_DISABLE_COLORS:e,NO_COLOR:u,TERM:s,COLORTERM:n}=me.process.env;e||u||t==="0"?fe=!1:t==="1"||t==="2"||t==="3"?fe=!0:s==="dumb"?fe=!1:"CI"in me.process.env&&["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE","DRONE"].some(r=>r in me.process.env)?fe=!0:fe=process.stdout.isTTY,fe&&(process.platform==="win32"||n&&(n==="truecolor"||n==="24bit")?Ne=3:s&&(s.endsWith("-256color")||s.endsWith("256"))?Ne=2:Ne=1)}let yn={enabled:fe,supportLevel:Ne};function He(t,e,u=1){const s=`\x1B[${t}m`,n=`\x1B[${e}m`,r=new RegExp(`\\x1b\\[${e}m`,"g");return i=>yn.enabled&&yn.supportLevel>=u?s+(""+i).replace(r,s)+n:""+i}a(He,"kolorist");const Le=He(33,39),ml=He(90,39),_l=He(92,39),Al=He(95,39),yl=He(96,39),ct=a((t=!0)=>{let e=!1;return u=>{if(e||u==="unknown-flag")return!0;if(u==="argument")return e=!0,t}},"ignoreAfterArgument"),wn=a((t,e=process.argv.slice(2))=>(vu(t,e,{ignore:ct()}),e),"removeArgvFlags"),wl=a(t=>{let e=Buffer.alloc(0);return u=>{for(e=Buffer.concat([e,u]);e.length>4;){const s=e.readInt32BE(0);if(e.length>=4+s){const n=e.slice(4,4+s);t(n),e=e.slice(4+s)}else break}}},"bufferData"),Rn=a(async()=>{const t=Un.createServer(u=>{u.on("data",wl(s=>{const n=JSON.parse(s.toString());t.emit("data",n)}))}),e=Nn(process.pid);return await Et.promises.mkdir(Kn,{recursive:!0}),await Et.promises.rm(e,{force:!0}),await new Promise((u,s)=>{t.listen(e,u),t.on("error",s)}),t.unref(),process.on("exit",()=>{if(t.close(),!Hn)try{Et.rmSync(e)}catch{}}),t},"createIpcServer"),Rl=a(()=>new Date().toLocaleTimeString(),"currentTime"),Ie=a((...t)=>console.log(ml(Rl()),yl("[tsx]"),...t),"log"),bl="\x1Bc",vl=a((t,e)=>{let u;return function(){u&&clearTimeout(u),u=setTimeout(()=>Reflect.apply(t,this,arguments),e)}},"debounce"),bn={noCache:{type:Boolean,description:"Disable caching",default:!1},tsconfig:{type:String,description:"Custom tsconfig.json path"},clearScreen:{type:Boolean,description:"Clearing the screen on rerun",default:!0},ignore:{type:[String],description:"Paths & globs to exclude from being watched (Deprecated: use --exclude)"},include:{type:[String],description:"Additional paths & globs to watch"},exclude:{type:[String],description:"Paths & globs to exclude from being watched"}},Sl=ai({name:"watch",parameters:["<script path>"],flags:bn,help:{description:"Run the script and watch for changes"},ignoreArgv:ct(!1)},async t=>{const e=wn(bn,process.argv.slice(3)),u={noCache:t.flags.noCache,tsconfigPath:t.flags.tsconfig,clearScreen:t.flags.clearScreen,include:t.flags.include,exclude:[...t.flags.ignore,...t.flags.exclude],ipc:!0};let s,n=!1;(await Rn()).on("data",l=>{if(l&&typeof l=="object"&&"type"in l&&l.type==="dependency"&&"path"in l&&typeof l.path=="string"){const p=l.path.startsWith("file:")?In(l.path):l.path;Gn.isAbsolute(p)&&h.add(p)}});const i=a(()=>{if(!n)return as(e,u)},"spawnProcess");let o=!1;const D=a(async(l,p="SIGTERM",C=5e3)=>{let g=!1;const y=new Promise(B=>{l.on("exit",H=>{g=!0,o=!1,B(H)})});return o=!0,l.kill(p),setTimeout(()=>{g||(Ie(Le(`Process didn't exit in ${Math.floor(C/1e3)}s. Force killing...`)),l.kill("SIGKILL"))},C),await y},"killProcess"),c=vl(async(l,p)=>{const C=l?`${l?Al(l):""}${p?` in ${_l(`./${p}`)}`:""}`:"";if(o){Ie(C,Le("Process hasn't exited. Killing process...")),s.kill("SIGKILL");return}s&&(s.exitCode===null?(Ie(C,Le("Restarting...")),await D(s)):Ie(C,Le("Rerunning...")),u.clearScreen&&process.stdout.write(bl)),s=i()},100);c();const f=a(l=>{n=!0,s?.exitCode===null?(o&&Ie(Le("Previous process hasn't exited yet. Force killing...")),D(s,o?"SIGKILL":l).then(p=>{process.exit(p??0)},()=>{})):process.exit(dt.signals[l])},"relaySignal");process.on("SIGINT",f),process.on("SIGTERM",f);const h=gl([...t._,...u.include],{cwd:process.cwd(),ignoreInitial:!0,ignored:["**/.*/**","**/.*","**/{node_modules,bower_components,vendor}/**",...u.exclude],ignorePermissionErrors:!0}).on("all",c);process.stdin.on("data",()=>c("Return key"))}),Bl=a((t,e)=>{let u;e.on("data",r=>{r&&r.type==="signal"&&u&&u(r.signal)});const s=a(()=>{const r=new Promise(i=>{setTimeout(()=>i(void 0),30),u=i});return r.then(()=>{u=void 0},()=>{}),r},"waitForSignalFromChild"),n=a(async r=>{await s()!==r&&(t.kill(r),await s()!==r&&(t.on("exit",()=>{const D=dt.signals[r];process.exit(128+D)}),t.kill("SIGKILL")))},"relaySignalToChild");process.on("SIGINT",n),process.on("SIGTERM",n)},"relaySignals"),vn={noCache:{type:Boolean,description:"Disable caching"},tsconfig:{type:String,description:"Custom tsconfig.json path"}};zu({name:"tsx",parameters:["[script path]"],commands:[Sl],flags:{...vn,version:{type:Boolean,alias:"v",description:"Show version"},help:{type:Boolean,alias:"h",description:"Show help"}},help:!1,ignoreArgv:ct()},async t=>{t.flags.version?process.stdout.write(`tsx v${On}
node `):t.flags.help&&(t.showHelp({description:"Node.js runtime enhanced with esbuild for loading TypeScript & ESM"}),console.log(`${"-".repeat(45)}
`));const e={eval:{type:String,alias:"e"},print:{type:String,alias:"p"}},{_:u,flags:s}=zu({flags:{...e,inputType:String,test:Boolean},help:!1,ignoreArgv:ct(!1)}),n=wn({...vn,...e}),i=["print","eval"].find(c=>!!s[c]);if(i){const{inputType:c}=s,f=s[i],h=xn(f,{loader:"default",sourcefile:"/eval.ts",format:c==="module"?"esm":"cjs"});n.unshift(`--${i}`,h.code)}wu(Mn)&&s.test&&u.length===0&&n.push("**/{test,test/**/*,test-*,*[.-_]test}.?(c|m)@(t|j)s");const o=await Rn(),D=as(n,{noCache:!!t.flags.noCache,tsconfigPath:t.flags.tsconfig});Bl(D,o),process.send&&D.on("message",c=>{process.send(c)}),D.send&&process.on("message",c=>{D.send(c)}),D.on("close",c=>{c===null&&(c=dt.signals[D.signalCode]+128),process.exit(c)})});
